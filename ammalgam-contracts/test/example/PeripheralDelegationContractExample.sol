// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {SafeERC20} from '@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol';

import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {BORROW_L, BORROW_X, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';

interface IDebtDelegation {
    function delegationAllowance(address onBehalfOf, address delegatee) external view returns (bool);
    function approveDelegation(
        address delegatee
    ) external;
    function borrow(address onBehalfOf, address to, IAmmalgamPair pair, uint256 amountX, uint256 amountY) external;
    function borrowLiquidity(
        address onBehalfOf,
        address to,
        IAmmalgamPair pair,
        uint256 amountL
    ) external returns (uint256, uint256);
}

contract PeripheralDelegationContractExample is IDebtDelegation, ICallback {
    using SafeERC20 for IERC20;
    using SafeERC20 for IERC20DebtToken;

    // using boolean instead of uint256 is because the actual amount is managed in the debtToken
    mapping(address => mapping(address => bool)) private _allowances;

    function delegationAllowance(address onBehalfOf, address delegatee) public view override returns (bool) {
        return _allowances[onBehalfOf][delegatee];
    }

    function approveDelegation(
        address delegatee
    ) public override {
        _allowances[msg.sender][delegatee] = true;
    }

    function borrow(
        address onBehalfOf,
        address to,
        IAmmalgamPair pair,
        uint256 amountX,
        uint256 amountY
    ) external override {
        bytes memory data = abi.encode(onBehalfOf, to);
        checkDelegation(onBehalfOf, msg.sender);
        pair.borrow(address(this), amountX, amountY, data);
    }

    function borrowLiquidity(
        address onBehalfOf,
        address to,
        IAmmalgamPair pair,
        uint256 amountLShares
    ) external override returns (uint256, uint256) {
        bytes memory data = abi.encode(onBehalfOf, to);
        checkDelegation(onBehalfOf, msg.sender);
        return pair.borrowLiquidity(address(this), amountLShares, data);
    }

    function ammalgamBorrowCallV1(
        address, // sender,
        uint256 borrowedXAssets,
        uint256 borrowedYAssets,
        uint256 borrowedXShares,
        uint256 borrowedYShares,
        bytes calldata data
    ) external {
        (address onBehalfOf, address to) = abi.decode(data, (address, address));

        (IAmmalgamPair pair, IERC20 tokenX, IERC20 tokenY) = getPairParams(msg.sender);

        if (borrowedXShares > 0) {
            IERC20DebtToken debtTokenX = IERC20DebtToken(address(pair.tokens(BORROW_X)));
            debtTokenX.safeTransfer(onBehalfOf, borrowedXShares);
            tokenX.safeTransfer(to, borrowedXAssets);
        }
        if (borrowedYShares > 0) {
            IERC20DebtToken debtTokenY = IERC20DebtToken(address(pair.tokens(BORROW_Y)));
            debtTokenY.safeTransfer(onBehalfOf, borrowedYShares);
            tokenY.safeTransfer(to, borrowedYAssets);
        }
    }

    function ammalgamBorrowLiquidityCallV1(
        address, // sender,
        uint256 borrowedXAssets,
        uint256 borrowedYAssets,
        uint256 borrowedLShares,
        bytes calldata data
    ) external {
        (address onBehalfOf, address to) = abi.decode(data, (address, address));

        (IAmmalgamPair pair, IERC20 tokenX, IERC20 tokenY) = getPairParams(msg.sender);

        if (borrowedLShares > 0) {
            IERC20DebtToken debtTokenL = IERC20DebtToken(address(pair.tokens(BORROW_L)));
            debtTokenL.safeTransfer(onBehalfOf, borrowedLShares);
            tokenX.safeTransfer(to, borrowedXAssets);
            tokenY.safeTransfer(to, borrowedYAssets);
        }
    }

    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external { /* noop */ }

    function checkDelegation(address onBehalfOf, address delegatee) private view {
        if (delegatee != onBehalfOf) {
            require(delegationAllowance(onBehalfOf, delegatee), 'Delegation not allowed');
        }
    }

    // slither-disable-next-line naming-convention
    function getPairParams(
        address _pair
    ) private view returns (IAmmalgamPair pair, IERC20 tokenX, IERC20 tokenY) {
        pair = IAmmalgamPair(_pair);
        (tokenX, tokenY) = pair.underlyingTokens();
    }

    function ammalgamSwapCallV1(address sender, uint256 amountX, uint256 amountY, bytes calldata data) external { /* noop */ }
}
