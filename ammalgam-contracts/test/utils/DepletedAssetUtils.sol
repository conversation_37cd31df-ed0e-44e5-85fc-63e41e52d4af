// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';
import {Q128, N_TIMES_FEE} from 'contracts/libraries/constants.sol';

library DepletedAssetUtils {
    uint256 private constant Q64 = 0x10000000000000000;
    uint256 private constant BUFFER = 95;

    error MissingOutGteReserveOut();
    error MissingGteActual();

    // Y_{0}\left(\frac{-M^{2}+\ 100M}{-M^{2}+\ 100M+100n}\right)
    uint256 constant BOUNDARY_NUMERATOR =
        QuadraticSwapFees.MAX_QUADRATIC_FEE_PERCENT * (100 - QuadraticSwapFees.MAX_QUADRATIC_FEE_PERCENT);
    uint256 constant BOUNDARY_DENOMINATOR = BOUNDARY_NUMERATOR + 100 * QuadraticSwapFees.N;
    uint256 constant LINEAR_OUT_NUMERATOR_SUBTRACT = QuadraticSwapFees.MAX_QUADRATIC_FEE_PERCENT
        * (2 + QuadraticSwapFees.MAX_QUADRATIC_FEE_PERCENT / QuadraticSwapFees.N);
    uint256 constant LINEAR_OUT_DENOMINATOR = 100 - 2 * QuadraticSwapFees.MAX_QUADRATIC_FEE_PERCENT;

    function computeExpectedSwapInAmount(
        uint256 swapAmountOut,
        uint256 reserveIn,
        uint256 reserveOut,
        uint256 missingIn,
        uint256 missingOut
    ) internal pure returns (uint256 swapAmountIn) {
        return computeExpectedSwapInAmount(swapAmountOut, reserveIn, reserveIn, reserveOut, missingIn, missingOut);
    }

    function computeExpectedSwapInAmount(
        uint256 swapAmountOut,
        uint256 reserveIn,
        uint256 referenceReserveIn,
        uint256 reserveOut,
        uint256 missingIn,
        uint256 missingOut
    ) internal pure returns (uint256 swapAmountIn) {
        if (missingOut >= reserveOut) {
            revert MissingOutGteReserveOut();
        }

        // Not great that there is no coverage for this case.
        require(referenceReserveIn <= reserveIn, 'Ref reserve less than ');

        // uint256 newReserveOut = reserveOut - swapAmountOut;
        // uint256 newReserveIn = computeCurve(newReserveOut, reserveOut, reserveIn, missingOut,
        // missingIn, 1);

        uint256 adjReserveOut = reserveOut;
        if (reserveOut * BUFFER < 100 * missingOut) {
            adjReserveOut = (reserveOut - missingOut) * 100 / (100 - BUFFER);
        }

        uint256 adjReserveIn = reserveIn;
        if (reserveIn * BUFFER < 100 * missingIn) {
            adjReserveIn = (reserveIn - missingIn) * 100 / (100 - BUFFER);
        }

        if ((reserveOut - swapAmountOut) * BUFFER < 100 * missingOut) {
            swapAmountOut = adjReserveOut - 100 * (reserveOut - swapAmountOut - missingOut) / (100 - BUFFER);
        }

        uint256 boundary = BOUNDARY_NUMERATOR * reserveOut / BOUNDARY_DENOMINATOR;
        if (swapAmountOut > boundary) {
            swapAmountIn = Math.ceilDiv(
                reserveIn
                    * (adjReserveOut * 100 * Q128 / (adjReserveOut - swapAmountOut) - LINEAR_OUT_NUMERATOR_SUBTRACT * Q128),
                (LINEAR_OUT_DENOMINATOR * Q128)
            ) - reserveIn;
        } else {
            uint256 reserveDiff = adjReserveIn - referenceReserveIn;
            // this would underflow if
            //  referenceReserveIn * 50 < N * (reserveIn - referenceReserveIn)
            //  referenceReserveIn * (50 + N) < N * reserveIn
            //  referenceReserveIn (50 + N) / N < reserveIn
            //  referenceReserveIn * 7 / 2 < reserveIn
            // which is beyond the range of the boundary condition.
            uint256 innerToSquare = (referenceReserveIn * 50 - N_TIMES_FEE * reserveDiff) * Q64 / referenceReserveIn;

            swapAmountIn = Math.ceilDiv(
                referenceReserveIn
                    * (
                        50 * Q64 + N_TIMES_FEE * Q64 * reserveDiff / referenceReserveIn
                            - Math.sqrt(
                                innerToSquare * innerToSquare
                                    - (100 * N_TIMES_FEE * Q128 * swapAmountOut / (adjReserveOut - swapAmountOut))
                            )
                    ),
                N_TIMES_FEE * Q64
            );
            return swapAmountIn;
        }
    }

    function computeExpectedSwapInAmountWithoutFees(
        uint256 swapAmountOut,
        uint256 reserveIn,
        uint256 reserveOut,
        uint256 missingIn,
        uint256 missingOut
    ) internal pure returns (uint256 swapAmountIn) {
        if (missingOut >= reserveOut) {
            revert MissingOutGteReserveOut();
        }
        uint256 newReserveOut = reserveOut - swapAmountOut;
        uint256 newReserveIn = computeCurve(newReserveOut, reserveOut, reserveIn, missingOut, missingIn, 1);

        swapAmountIn = newReserveIn - reserveIn;
    }

    function computeExpectedSwapOutAmount(
        uint256 swapAmountIn,
        uint256 reserveIn,
        uint256 reserveOut,
        uint256 missingIn,
        uint256 missingOut
    ) internal pure returns (uint256 expectedSwapOut) {
        return computeExpectedSwapOutAmount(swapAmountIn, reserveIn, reserveIn, reserveOut, missingIn, missingOut);
    }

    function computeExpectedSwapOutAmount(
        uint256 swapAmountIn,
        uint256 reserveIn,
        uint256 referenceReserveIn,
        uint256 reserveOut,
        uint256 missingIn,
        uint256 missingOut
    ) internal pure returns (uint256 expectedSwapOut) {
        uint256 newReserveIn = reserveIn * QuadraticSwapFees.BIPS_Q64
            + swapAmountIn
                * (
                    QuadraticSwapFees.BIPS_Q64
                        - QuadraticSwapFees.calculateSwapFeeBipsQ64(swapAmountIn, reserveIn, referenceReserveIn)
                );
        uint256 newReserveOut =
            computeCurve(newReserveIn, reserveIn, reserveOut, missingIn, missingOut, QuadraticSwapFees.BIPS_Q64);
        expectedSwapOut = reserveOut - newReserveOut;
    }

    function computeExpectedSwapOutAmountWithoutFees(
        uint256 swapAmountIn,
        uint256 reserveIn,
        uint256 reserveOut,
        uint256 missingIn,
        uint256 missingOut
    ) internal pure returns (uint256 expectedSwapOut) {
        uint256 newReserveIn = reserveIn + swapAmountIn;
        uint256 newReserveOut = computeCurve(newReserveIn, reserveIn, reserveOut, missingIn, missingOut, 1);
        expectedSwapOut = reserveOut - newReserveOut;
    }

    /**
     * @dev See Desmos chart functions C_X and C_y for https://www.Desmos.com/calculator/22bdlqiazt
     * @param a         starting value x or y
     * @param reserveA  reserve for start asset
     * @param reserveB  reserve for end asset
     * @param missingA  missing for start asset
     * @param missingB  missing for end asset
     * @param decimals  decimals when a is passed in with additional precision due to fees
     * @return b        ending value y or x
     */
    function computeCurve(
        uint256 a,
        uint256 reserveA,
        uint256 reserveB,
        uint256 missingA,
        uint256 missingB,
        uint256 decimals
    ) internal pure returns (uint256 b) {
        uint256 adjustedA = actualToAdjusted(reserveA, missingA);
        uint256 adjustedB = actualToAdjusted(reserveB, missingB);

        uint256 k = adjustedA * adjustedB;
        uint256 firstAdjusted = Math.ceilDiv(k, Math.ceilDiv(actualToAdjusted(a, missingA * decimals), decimals));
        b = adjustedToActual(firstAdjusted, missingB);
    }

    function computeAmountsForRepayLiquidity(
        uint256 liquidity,
        uint256 missingXAssets,
        uint256 missingYAssets,
        uint256 reserveXAssets,
        uint256 reserveYAssets,
        uint256 _activeLiquidityAssets
    ) internal pure returns (uint256 amountX, uint256 amountY) {
        return (
            computeAmountForRepayLiquidity(liquidity, missingXAssets, reserveXAssets, _activeLiquidityAssets),
            computeAmountForRepayLiquidity(liquidity, missingYAssets, reserveYAssets, _activeLiquidityAssets)
        );
    }

    function computeAmountForRepayLiquidity(
        uint256 liquidity,
        uint256 missingAssets,
        uint256 reserveAssets,
        uint256 _activeLiquidityAssets
    ) private pure returns (uint256 amountAssets) {
        if (uint256(reserveAssets) * BUFFER >= missingAssets * 100) {
            amountAssets = Math.ceilDiv(liquidity * reserveAssets, _activeLiquidityAssets);
        } else {
            uint256 c = Math.ceilDiv(
                (liquidity + _activeLiquidityAssets) * (reserveAssets - missingAssets) * 100 / _activeLiquidityAssets,
                (100 - BUFFER)
            );
            uint256 p = Math.ceilDiv(
                (liquidity + _activeLiquidityAssets) * (reserveAssets - missingAssets), _activeLiquidityAssets
            ) + missingAssets;
            amountAssets =
                Math.max((c > reserveAssets) ? c - reserveAssets : 0, (p > reserveAssets) ? p - reserveAssets : 0);
        }
    }

    /**
     * @dev See Desmos chart functions X_A or Y_A for https://www.Desmos.com/calculator/22bdlqiazt
     * private due to modifications of equations for preserving precision.
     * @param actual actual x or y
     * @param missing missing assets associated with actual
     */
    function actualToAdjusted(uint256 actual, uint256 missing) private pure returns (uint256 adjusted) {
        if (missing >= actual) {
            revert MissingGteActual();
        }
        if (missing * 100 < actual * BUFFER) {
            // counterintuitively we multiply by (100 - BUFFER) here rather than dividing in the else to preserve precision.
            adjusted = actual * (100 - BUFFER);
        } else {
            adjusted = (actual - missing) * 100;
        }
    }

    /**
     * @dev See Desmos chart functions X_I or Y_I for https://www.Desmos.com/calculator/22bdlqiazt inverse of actualToAdjusted
     * private due to modifications of equations for preserving precision.
     * @param adjusted adjusted value of X
     * @param missing missing assets associated with adjusted
     */
    function adjustedToActual(uint256 adjusted, uint256 missing) private pure returns (uint256 actual) {
        if (missing * 100 < Math.ceilDiv(adjusted, (100 - BUFFER)) * BUFFER) {
            // In the context of computeCurve, we need to divide by (100 - BUFFER) here rather than multiplying in the else to preserve precision.
            actual = Math.ceilDiv(adjusted, (100 - BUFFER));
        } else {
            actual = Math.ceilDiv(adjusted, 100) + missing;
        }
    }
}
