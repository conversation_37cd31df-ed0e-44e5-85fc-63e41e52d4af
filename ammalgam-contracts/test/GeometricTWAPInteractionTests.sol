// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {AmmalgamFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {ISaturationAndGeometricTWAPState} from 'contracts/interfaces/ISaturationAndGeometricTWAPState.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {adjustAmountsByTickPrice} from 'test/shared/utilities.sol';
import {DEFAULT_MID_TERM_INTERVAL, Q128} from 'contracts/libraries/constants.sol';

import {Ownable} from '@openzeppelin/contracts/access/Ownable.sol';

/**
 * @notice  Tests of GeometricTWAP integration with pair contract.
 * @dev     Testing scenarios:
 *              Long-term interval authentication
 *              Configuration for the long term interval
 *              Compute deposit Y for max borrow X with missing blocks
 *              Initial mint should successfully initialize `Observation` struct and record `firstTick` price
 */
contract GeometricTWAPInteractionTests is Test {
    using GeometricTWAP for GeometricTWAP.Observations;

    IAmmalgamFactory private factory;
    IPairHarness private pair;
    address private tester;
    address private random;

    FactoryPairTestFixture private fixture;

    uint256 private constant depositX = 2e18;
    uint256 private constant initialX = 80e18;
    uint256 private constant initialY = 20e18;

    uint8 private constant LTV = 75;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();

        tester = address(1111);
        random = address(99);

        fixture.transferTokensTo(random, initialX, initialY);
        fixture.transferTokensTo(tester, depositX, 0);
    }

    function test_resetLongTermBufferSizeByUnauthorizedShouldFail() public {
        uint24 longTermIntervalConfigFactor = 0;
        ISaturationAndGeometricTWAPState satState = factory.saturationAndGeometricTWAPState();
        address pairAddress = fixture.pairAddress();

        vm.expectRevert(); // docs say to use `expectPartialRevert` but is not found.
        vm.prank(random); // not owner
        satState.configLongTermInterval(pairAddress, longTermIntervalConfigFactor);
    }

    function test_invalidLongTermIntervalConfig() public {
        // Initialise `Observation` struct
        fixture.mintFor(random, initialX, initialY);

        vm.prank(factory.feeToSetter());
        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        fixture.configLongTermInterval(0);
    }

    function test_InitialMintShouldInitializeObservation() public {
        uint256 _priceXInQ128 = Math.mulDiv(initialX, Q128, initialY);
        int16 firstTick = TickMath.getTickAtPrice(_priceXInQ128);

        GeometricTWAP.Observations memory obs = pair.exposed_observations();

        // verify default observation state
        assertEq(obs.lastTick, 0, 'Last tick is 0');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First mid-term is 0');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term is 0');

        // Initialise `Observation` struct
        fixture.mintFor(random, initialX, initialY);
        obs = pair.exposed_observations();

        // verify initialized observation state
        assertEq(obs.lastTick, firstTick, 'Last tick is updated with firstTick');

        // verify mid-term state
        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First mid-term slot is still 0');
        assertEq(obs.midTermTimeInterval[0], block.timestamp, 'First mid-term observation interval');

        // verify long-term state
        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term slot is still 0');
        assertEq(obs.longTermTimeInterval[0], block.timestamp, 'First long-term observation interval');

        // increment block and `deposit` to update observation state
        fixture.newBlock(1);
        fixture.depositFor(tester, depositX, 0);
        obs = pair.exposed_observations();

        // verify updated observation state
        assertEq(obs.lastTick, firstTick, 'Last tick is still same as the firstTick');

        // verify mid-term state
        assertEq(obs.midTermIndex, 2, 'midTermIndex is set to 2');
        assertEq(
            obs.midTermCumulativeSum[1],
            firstTick * int16(DEFAULT_MID_TERM_INTERVAL),
            'Second mid-term slot is updated with firstTick * DEFAULT_MID_TERM_INTERVAL'
        );
        assertEq(
            obs.midTermTimeInterval[1],
            block.timestamp,
            'Second mid-term observation interval is set to current block timestamp'
        );

        // verify long-term state
        assertEq(obs.longTermIndex, 1, 'longTermIndex is still set to 1');
    }
}
