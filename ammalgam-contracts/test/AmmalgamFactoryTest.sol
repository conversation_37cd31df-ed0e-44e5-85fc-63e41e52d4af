// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {ERC20} from '@openzeppelin/contracts/token/ERC20/ERC20.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {AmmalgamFactory, IPairFactory, PairFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {PluginRegistry} from 'contracts/tokens/PluginRegistry.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {ITokenFactory} from 'contracts/interfaces/factories/ITokenFactory.sol';
import {INewTokensFactory} from 'contracts/interfaces/factories/INewTokensFactory.sol';
import {deployFactory, deployTokenFactory} from 'contracts/utils/deployHelper.sol';
import {SaturationAndGeometricTWAPState} from 'contracts/SaturationAndGeometricTWAPState.sol';

import {DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG} from 'contracts/libraries/constants.sol';

import {getCreate2Address} from 'test/shared/utilities.sol';

contract AmmalgamFactoryTest is Test {
    error ZeroAddress();

    IAmmalgamFactory private factory;
    IPairFactory private pairFactory;
    IAmmalgamPair private pair;
    INewTokensFactory private tokenFactory;
    PluginRegistry private pluginRegistry;
    SaturationAndGeometricTWAPState private saturationAndGeometricTWAPState;

    address private wallet;
    address private other;

    address[] TEST_ADDRESSES = [******************************************, ******************************************];

    event PairCreated(address indexed tokenX, address indexed tokenY, address pair, uint256);
    event NewFeeTo(address indexed feeTo);
    event NewFeeToSetter(address indexed feeToSetter);

    function setUp() public {
        wallet = vm.addr(1);
        other = vm.addr(2);

        pluginRegistry = new PluginRegistry();
        tokenFactory = deployTokenFactory();
        pairFactory = new PairFactory();
        saturationAndGeometricTWAPState =
            new SaturationAndGeometricTWAPState(DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
        factory = new AmmalgamFactory(
            wallet,
            address(tokenFactory),
            address(pairFactory),
            address(pluginRegistry),
            address(saturationAndGeometricTWAPState)
        );
    }

    function testInitialStates() public view {
        assertEq(factory.feeTo(), address(0x0));
        assertEq(factory.feeToSetter(), wallet);
        assertEq(factory.allPairsLength(), 0);
    }

    function testCreatePair() public {
        createPair(TEST_ADDRESSES[0], TEST_ADDRESSES[1]);
    }

    function testCreatePairReverse() public {
        createPair(TEST_ADDRESSES[1], TEST_ADDRESSES[0]);
    }

    function testSetFeeTo() public {
        vm.startPrank(other);
        vm.expectRevert(AmmalgamFactory.Forbidden.selector);
        factory.setFeeTo(other);
        vm.stopPrank();

        vm.startPrank(wallet);
        factory.setFeeTo(wallet);
        assertEq(factory.feeTo(), wallet);
    }

    function testSetFeeToSetter() public {
        vm.startPrank(other);
        vm.expectRevert(AmmalgamFactory.Forbidden.selector);
        factory.setFeeToSetter(other);
        vm.stopPrank();

        vm.startPrank(wallet);
        factory.setFeeToSetter(other);
        assertEq(factory.feeToSetter(), other);
        vm.expectRevert(AmmalgamFactory.Forbidden.selector);
        factory.setFeeToSetter(wallet);
    }

    function testSetFeeToSetterConstructorToZeroAddressFails() public {
        vm.expectRevert(AmmalgamFactory.FeeToSetterIsZeroAddress.selector);
        factory = new AmmalgamFactory(
            address(0),
            address(tokenFactory),
            address(pairFactory),
            address(pluginRegistry),
            address(saturationAndGeometricTWAPState)
        );
    }

    function testSetTokenFactoryToZeroAddressFails() public {
        vm.expectRevert(AmmalgamFactory.ZeroAddress.selector);
        factory = new AmmalgamFactory(
            wallet, address(0), address(pairFactory), address(pluginRegistry), address(saturationAndGeometricTWAPState)
        );
    }

    function testZeroCreatePairAddressFails() public {
        vm.expectRevert(ZeroAddress.selector);
        new AmmalgamFactory(
            wallet, address(tokenFactory), address(0), address(pluginRegistry), address(saturationAndGeometricTWAPState)
        );
    }

    function testSetFeeToMethodToZeroAddressFails() public {
        vm.startPrank(wallet);
        vm.expectRevert(AmmalgamFactory.FeeToIsZeroAddress.selector);
        factory.setFeeTo(address(0));
    }

    function testSetFeeToSetterMethodToZeroAddressFails() public {
        vm.startPrank(wallet);
        vm.expectRevert(AmmalgamFactory.FeeToSetterIsZeroAddress.selector);
        factory.setFeeToSetter(address(0));
    }

    function createPair(address tokenX, address tokenY) private {
        bytes memory creationCode = type(AmmalgamPair).creationCode;

        //call func in utilities.sol
        address create2Address = getCreate2Address(address(factory), tokenX, tokenY, creationCode);

        vm.expectEmit(true, true, false, true);
        emit PairCreated(TEST_ADDRESSES[0], TEST_ADDRESSES[1], create2Address, 1);
        factory.createPair(tokenX, tokenY);

        assertEq(factory.getPair(tokenX, tokenY), create2Address, 'factory.getPair(...), create2Address');

        vm.expectRevert(AmmalgamFactory.PairExists.selector);
        factory.createPair(tokenX, tokenY);

        vm.expectRevert(AmmalgamFactory.PairExists.selector);
        factory.createPair(tokenY, tokenX);

        assertEq(factory.getPair(tokenX, tokenY), create2Address);
        assertEq(factory.getPair(tokenY, tokenX), create2Address);
        assertEq(factory.allPairs(0), create2Address);
        assertEq(factory.allPairsLength(), 1, 'allPairsLength');
        pair = IAmmalgamPair(create2Address);
        (IERC20 tokenXERC20, IERC20 tokenYERC20) = pair.underlyingTokens();
        assertEq(address(tokenXERC20), TEST_ADDRESSES[0]);
        assertEq(address(tokenYERC20), TEST_ADDRESSES[1]);
    }

    function testPluginRegistrySetOwner() public {
        vm.startPrank(other);
        vm.expectRevert();
        pluginRegistry.transferOwnership(other);
        vm.stopPrank();

        vm.startPrank(address(this));
        pluginRegistry.transferOwnership(other);
        vm.stopPrank();

        vm.startPrank(address(this));
        vm.expectRevert();
        pluginRegistry.updatePlugin(other, true);
        vm.stopPrank();

        vm.startPrank(other);
        pluginRegistry.updatePlugin(other, true);
        assertEq(pluginRegistry.isPluginAllowed(other), true);
        vm.stopPrank();
    }

    function testPluginRegistryUpdatePlugin() public {
        vm.startPrank(other);
        vm.expectRevert();
        pluginRegistry.updatePlugin(other, true);
        vm.stopPrank();

        vm.startPrank(address(this));
        pluginRegistry.updatePlugin(other, true);
        assertEq(pluginRegistry.isPluginAllowed(other), true);
        vm.stopPrank();
    }
}
