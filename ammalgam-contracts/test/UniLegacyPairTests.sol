// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {DEPOSIT_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {MINIMUM_LIQUIDITY, computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';

contract UniLegacyPairTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;
    address private tester3;

    FactoryPairTestFixture private fixture;

    event Transfer(address indexed from, address indexed to, uint256 amount);
    event Sync(uint256 reserveX, uint256 reserveY);
    event Mint(address indexed sender, address indexed to, uint256 assets, uint256 shares);
    event Deposit(address indexed sender, uint256 amountX, uint256 amountY);
    event Burn(address indexed sender, address indexed to, uint256 assets, uint256 shares);
    event Swap(
        address indexed sender,
        uint256 amountXIn,
        uint256 amountYIn,
        uint256 amountXOut,
        uint256 amountYOut,
        address indexed to
    );

    function setUp() public {
        tester = address(this); // test for EOA wallet -> tester = vm.addr(1113); vm.startPrank(tester);
        tester2 = vm.addr(1112);
        tester3 = vm.addr(1113);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(tester, 500_000e18, 500_000e18).transferTokensTo(tester2, 5000e18, 5000e18)
            .transferTokensTo(tester3, 5000e18, 5000e18);
    }

    function testMint() public {
        uint256 tokenXAmount = 1e18;
        uint256 tokenYAmount = 4e18;

        fixture.transferTokensTo(address(pair), tokenXAmount, tokenYAmount);

        uint256 expectedLiquidity = 2e18;

        vm.expectEmit(true, true, false, true);
        emit Mint(tester, address(factory), MINIMUM_LIQUIDITY, MINIMUM_LIQUIDITY);
        vm.expectEmit(true, true, true, true);
        emit Transfer(address(0), address(factory), MINIMUM_LIQUIDITY);
        vm.expectEmit(true, true, false, true);
        emit Mint(tester, tester, expectedLiquidity - MINIMUM_LIQUIDITY, expectedLiquidity - MINIMUM_LIQUIDITY);
        vm.expectEmit(true, true, true, true);
        emit Transfer(address(0), tester, expectedLiquidity - MINIMUM_LIQUIDITY);
        vm.expectEmit(false, false, false, true);
        emit Sync(tokenXAmount, tokenYAmount);

        pair.mint(tester);

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), expectedLiquidity);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), expectedLiquidity - MINIMUM_LIQUIDITY);
        assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), tokenYAmount);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();

        assertEq(_reserveX, tokenXAmount);
        assertEq(_reserveY, tokenYAmount);
    }

    function testSwapTestCase1() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 1e18, 5e18, 10e18);
    }

    function testSwapTestCase2() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 1e18, 10e18, 5e18);
    }

    function testSwapTestCase3() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 2e18, 5e18, 10e18);
    }

    function testSwapTestCase4() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 2e18, 10e18, 5e18);
    }

    function testSwapTestCase5() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 1e18, 10e18, 10e18);
    }

    function testSwapTestCase6() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 1e18, 100e18, 100e18);
    }

    function testSwapTestCase7() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runSwapTestCase(tester, 1e18, 1000e18, 1000e18);
    }

    function testOptimisticTestCase1() public {
        uint256 reserveX = 5e18;
        uint256 reserveY = 10e18;
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runOptimisticSwapTestCase(
            tester,
            reserveX
                * (QuadraticSwapFees.BIPS_Q64 - QuadraticSwapFees.calculateSwapFeeBipsQ64(reserveX, reserveX, reserveX))
                / QuadraticSwapFees.BIPS_Q64,
            reserveX,
            reserveY
        );
    }

    function testOptimisticTestCase2() public {
        uint256 reserveX = 10e18;
        uint256 reserveY = 5e18;
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runOptimisticSwapTestCase(
            tester,
            reserveX
                * (QuadraticSwapFees.BIPS_Q64 - QuadraticSwapFees.calculateSwapFeeBipsQ64(reserveX, reserveX, reserveX))
                / QuadraticSwapFees.BIPS_Q64,
            reserveX,
            reserveY
        );
    }

    function testOptimisticTestCase3() public {
        uint256 reserveX = 5e18;
        uint256 reserveY = 5e18;
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.runOptimisticSwapTestCase(
            tester,
            reserveX
                * (QuadraticSwapFees.BIPS_Q64 - QuadraticSwapFees.calculateSwapFeeBipsQ64(reserveX, reserveX, reserveX))
                / QuadraticSwapFees.BIPS_Q64,
            reserveX,
            reserveY
        );
    }

    function testSwapTokenX() public {
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        uint256 testerTokenXBalance = fixture.tokenX().balanceOf(tester);
        uint256 testerTokenYBalance = fixture.tokenY().balanceOf(tester);
        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        uint256 swapAmount = 1e18;
        uint256 expectedOutputAmount = computeExpectedSwapOutAmount(swapAmount, tokenXAmount, tokenYAmount);

        fixture.tokenX().transfer(address(pair), swapAmount);

        vm.expectEmit(true, true, false, true);
        emit Transfer(address(pair), tester, expectedOutputAmount);
        vm.expectEmit(true, true, false, true);
        emit Swap(tester, swapAmount, 0, 0, expectedOutputAmount, tester);
        vm.expectEmit(false, false, false, true);
        emit Sync(tokenXAmount + swapAmount, tokenYAmount - expectedOutputAmount);

        pair.swap(0, expectedOutputAmount, tester, '');

        (uint112 reserveX, uint112 reserveY,) = pair.getReserves();
        assertEq(reserveX, tokenXAmount + swapAmount);
        assertEq(reserveY, tokenYAmount - expectedOutputAmount);
        assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount + swapAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), tokenYAmount - expectedOutputAmount);

        assertEq(fixture.tokenX().balanceOf(tester), testerTokenXBalance - tokenXAmount - swapAmount);
        assertEq(fixture.tokenY().balanceOf(tester), testerTokenYBalance - tokenYAmount + expectedOutputAmount);
    }

    function testSwapTokenY() public {
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        uint256 testerTokenXBalance = fixture.tokenX().balanceOf(tester);
        uint256 testerTokenYBalance = fixture.tokenY().balanceOf(tester);

        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        uint256 swapAmount = 1e18;
        uint256 expectedOutputAmount = computeExpectedSwapOutAmount(swapAmount, tokenYAmount, tokenXAmount);

        fixture.tokenY().transfer(address(pair), swapAmount);

        vm.expectEmit(true, true, false, true);
        emit Transfer(address(pair), tester, expectedOutputAmount);
        vm.expectEmit(true, true, false, true);
        emit Swap(tester, 0, swapAmount, expectedOutputAmount, 0, tester);
        vm.expectEmit(false, false, false, true);
        emit Sync(tokenXAmount - expectedOutputAmount, tokenYAmount + swapAmount);

        pair.swap(expectedOutputAmount, 0, tester, '');

        (uint112 reserveX, uint112 reserveY,) = pair.getReserves();
        assertEq(reserveX, tokenXAmount - expectedOutputAmount);
        assertEq(reserveY, tokenYAmount + swapAmount);
        assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount - expectedOutputAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), tokenYAmount + swapAmount);

        assertEq(fixture.tokenX().balanceOf(tester), testerTokenXBalance - tokenXAmount + expectedOutputAmount);
        assertEq(fixture.tokenY().balanceOf(tester), testerTokenYBalance - tokenYAmount - swapAmount);
    }

    function testBurn() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        uint256 testerBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 testerBalanceY = fixture.tokenX().balanceOf(tester);

        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);
        uint256 expectedLiquidity = 3e18;
        pair.tokens(DEPOSIT_L).transfer(address(pair), expectedLiquidity - MINIMUM_LIQUIDITY);

        vm.expectEmit(true, true, false, true);
        emit Burn(tester, tester, expectedLiquidity - MINIMUM_LIQUIDITY, expectedLiquidity - MINIMUM_LIQUIDITY);
        vm.expectEmit(true, true, true, true);
        emit Transfer(address(pair), address(0), expectedLiquidity - MINIMUM_LIQUIDITY);
        vm.expectEmit(true, true, false, true);
        emit Transfer(address(pair), tester, tokenXAmount - 1000);
        vm.expectEmit(true, true, false, true);
        emit Transfer(address(pair), tester, tokenYAmount - 1000);
        vm.expectEmit(false, false, false, true);
        emit Sync(1000, 1000);

        (uint256 outAmountX, uint256 outAmountY) = pair.burn(tester);
        assertEq(outAmountX, tokenXAmount - 1000);
        assertEq(outAmountY, tokenYAmount - 1000);

        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 0);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), MINIMUM_LIQUIDITY);
        assertEq(fixture.tokenX().balanceOf(address(pair)), 1000);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 1000);

        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX - 1000);
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY - 1000);
    }

    function testFeeToOff() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        uint256 swapAmount = 1e18;
        uint256 expectedOutputAmount = 996_006_981_039_903_216;
        fixture.tokenY().transfer(address(pair), swapAmount);
        pair.swap(expectedOutputAmount, 0, tester, '');

        uint256 expectedLiquidity = 1000e18;
        pair.tokens(DEPOSIT_L).transfer(address(pair), expectedLiquidity - MINIMUM_LIQUIDITY);
        pair.burn(tester);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), MINIMUM_LIQUIDITY);
    }
}
