// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {
    DEFAULT_MID_TERM_INTERVAL,
    MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG,
    LENDING_TICK_NOT_AVAILABLE,
    MAX_TICK_DELTA,
    Q32
} from 'contracts/libraries/constants.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';

import {getTickRange} from 'test/shared/utilities.sol';
import {
    GeometricTWAPTestFixture,
    DEFAULT_LONG_TERM_INTERVAL,
    DEFAULT_TICK_VALUE
} from 'test/shared/GeometricTWAPTestFixture.sol';

/**
 * @notice  Unit tests for GeometricTWAPSpec.
 * @dev     This contract contains unit tests for GeometricTWAPSpec. The tests attempt to cover:
 *          1. Verification of DEFAULT_TICK_VALUE ranges and calculations in various scenarios based on time elapsed
 *                between recorded observations.
 *          2. Confirmation of the behavior of the GeometricTWAP contract in different conditions.
 */
contract GeometricTWAPSpecTests is Test {
    GeometricTWAPTestFixture private fixture;

    using GeometricTWAP for GeometricTWAP.Observations;

    GeometricTWAP.Observations private obsWithoutInitialization;

    uint256 internal constant MID_TERM_ARRAY_LAST_INDEX = GeometricTWAP.MID_TERM_ARRAY_LENGTH - 1;
    uint256 internal constant LONG_TERM_ARRAY_LAST_INDEX = GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1;
    uint256 internal constant DEFAULT_LONG_TERM_BUFFER = DEFAULT_LONG_TERM_INTERVAL * LONG_TERM_ARRAY_LAST_INDEX;
    uint32 lendingTimestamp;

    function setUp() public {
        obsWithoutInitialization.lastLendingStateTick = LENDING_TICK_NOT_AVAILABLE;
        lendingTimestamp = GeometricTWAP.getCurrentTimestamp();
    }

    function testMinimumLongTermTimeUpdateConfigInit() public {
        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        GeometricTWAP.initializeObservationStruct(
            obsWithoutInitialization, 12, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG - 1
        );

        // minimum passes
        GeometricTWAP.initializeObservationStruct(obsWithoutInitialization, 12, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);

        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        GeometricTWAP.initializeObservationStruct(
            obsWithoutInitialization, 8, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * 8 / 12 - 1
        );

        // maximum fails
        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        GeometricTWAP.initializeObservationStruct(
            obsWithoutInitialization, 8, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * 8 / 12
        );
    }

    function testMinimumLongTermTimeUpdateConfigUpdate() public {
        obsWithoutInitialization.initializeObservationStruct(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG
        );
        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        GeometricTWAP.configLongTermInterval(obsWithoutInitialization, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG - 1);

        // minimum passes
        GeometricTWAP.configLongTermInterval(obsWithoutInitialization, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
    }

    function testBlockUpdateBeforeInitialization() public {
        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        GeometricTWAP.configLongTermInterval(obsWithoutInitialization, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
    }

    /**
     * @dev There is no assertion here, but the test runs for the 2000 years to
     *      verify there is no overflow in the cumulative tick or timestamp.
     */
    function testOverFlowOfCumulativeTick() public {
        obsWithoutInitialization.initializeObservationStruct(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG
        );
        obsWithoutInitialization.addObservationAndSetLendingState(TickMath.MAX_TICK / 2);
        uint32 currentTimestamp;

        // (2**32 -1) / 60 / 60 / 24 / 365 / 51 = 2.67 years per update for 51 updates.
        uint32 maxDistanceBetweenUpdates = uint32(type(uint32).max / GeometricTWAP.MID_TERM_ARRAY_LENGTH);

        for (uint256 i = 0; i < 1000; i++) {
            vm.warp(block.timestamp + maxDistanceBetweenUpdates);
            currentTimestamp = GeometricTWAP.getCurrentTimestamp();
            obsWithoutInitialization.recordObservation(TickMath.MAX_TICK / 2, currentTimestamp);
        }
    }

    function testGeometricTWAP_InvalidMidTermIntervalConfig() public {
        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        fixture = new GeometricTWAPTestFixture(0, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_InvalidLongTermIntervalConfig() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        vm.expectRevert(GeometricTWAP.InvalidIntervalConfig.selector);
        fixture.changeInterval(0);
    }

    function testGeometricTWAP_firstBlock() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        (int16 minTick, int16 maxTick, int256 lastTick) = fixture.getTicks(DEFAULT_TICK_VALUE);

        assertEq(
            minTick,
            DEFAULT_TICK_VALUE - int256(GeometricTWAP.LOG_BASE_OF_ROOT_TWO),
            'Default Tick - LOG_BASE_OF_ROOT_TWO = 22 - 178'
        );
        assertEq(
            maxTick,
            DEFAULT_TICK_VALUE + int256(GeometricTWAP.LOG_BASE_OF_ROOT_TWO) + 1,
            'Default Tick + 1 + LOG_BASE_OF_ROOT_TWO = 22 + 1 + 178'
        );
        assertEq(lastTick, DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_InitializeWithOneUpdate() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // verify mid-term state
        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is still 0');
        assertEq(obs.midTermTimeInterval[0], block.timestamp, 'First mid-term interval');

        // verify long-term state
        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term slot is still 0');
        assertEq(obs.longTermTimeInterval[0], block.timestamp, 'First long-term interval');
    }

    function testGeometricTWAP_AfterTwoUpdates() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state
        assertEq(obs.midTermIndex, 2, 'midTermIndex is set to 2');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is still 0');
        assertEq(obs.midTermCumulativeSum[1], currentCumulativeTick, 'Second slot updated with current block price');
        assertEq(obs.midTermTimeInterval[0], 1, 'First interval should be 1');
        assertEq(obs.midTermTimeInterval[1], block.timestamp, 'Second interval should be 13');

        // verify long-term state
        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term slot is still 0');
        assertEq(obs.longTermCumulativeSum[1], 0, 'Second long-term slot is 0');
        assertEq(obs.longTermTimeInterval[0], 1, 'First long-term interval should be 1');
        assertEq(obs.longTermTimeInterval[1], 0, 'Second long-term interval should be 0');
    }

    function testGeometricTWAP_AfterOneLongTermInterval() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(DEFAULT_LONG_TERM_INTERVAL, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state
        assertEq(obs.midTermIndex, DEFAULT_LONG_TERM_INTERVAL + 1, 'midTermIndex is set to 11');
        assertEq(
            obs.midTermCumulativeSum[DEFAULT_LONG_TERM_INTERVAL],
            currentCumulativeTick,
            '11th slot slot updated with block price'
        );
        assertEq(
            obs.midTermTimeInterval[DEFAULT_LONG_TERM_INTERVAL],
            block.timestamp,
            'last observation interval should be block.timestamp'
        );

        // verify long-term state
        assertEq(obs.longTermIndex, 2, 'longTermIndex is set to 2');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term slot is 0');
        assertEq(
            obs.longTermCumulativeSum[1], currentCumulativeTick, 'Second long-term slot updated with second block price'
        );
        assertEq(obs.longTermCumulativeSum[2], 0, 'Third long-term slot is 0');
        assertEq(obs.longTermTimeInterval[0], 1, 'First long-term interval should be first block timestamp');
        assertEq(obs.longTermTimeInterval[1], block.timestamp, 'Second long-term interval should be block.timestamp');
        assertEq(obs.longTermTimeInterval[2], 0, 'Third long-term interval should be 0');
    }

    function testGeometricTWAP_AfterOneLapAroundMidTermBuffer() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(MID_TERM_ARRAY_LAST_INDEX, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state
        assertEq(obs.midTermIndex, 0, 'midTermIndex is set to 0');
        assertEq(
            obs.midTermCumulativeSum[MID_TERM_ARRAY_LAST_INDEX],
            currentCumulativeTick,
            'Last slot updated with current block price'
        );
        assertEq(
            obs.midTermTimeInterval[MID_TERM_ARRAY_LAST_INDEX],
            block.timestamp,
            'last observation interval should be block.timestamp'
        );

        // verify state updates as they overwrite first values
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        obs = fixture.getObservationStruct();
        currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state
        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(obs.midTermCumulativeSum[0], currentCumulativeTick, 'First slot updated with current cumulative price');
        assertEq(obs.midTermTimeInterval[0], block.timestamp, 'last observation interval should be block.timestamp');
    }

    function testGeometricTWAP_AfterOneLapAroundLongTermBuffer() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(DEFAULT_LONG_TERM_BUFFER, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify long-term state
        assertEq(obs.longTermIndex, 0, 'longTermIndex is set to 0');
        assertEq(
            obs.longTermCumulativeSum[LONG_TERM_ARRAY_LAST_INDEX],
            currentCumulativeTick,
            'Last long-term slot updated with current block price'
        );
        assertEq(
            obs.longTermTimeInterval[LONG_TERM_ARRAY_LAST_INDEX],
            block.timestamp,
            'Last long-term interval should be block.timestamp'
        );

        fixture.generateObservations(DEFAULT_LONG_TERM_INTERVAL, DEFAULT_TICK_VALUE);

        obs = fixture.getObservationStruct();
        currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(
            obs.longTermCumulativeSum[0], currentCumulativeTick, 'First long-term slot updated with cumulative price'
        );
        assertEq(obs.longTermTimeInterval[0], block.timestamp, 'First long-term interval should be block.timestamp');
    }

    function testGeometricTWAP_WithOneMissedBlock() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        // skip a block
        fixture.generatePastBlocks(1);
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state
        assertEq(obs.midTermIndex, 2, 'midTermIndex is set to 2');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is still 0');
        assertEq(obs.midTermCumulativeSum[1], currentCumulativeTick, 'Second slot updated third block price');
        assertEq(obs.midTermTimeInterval[0], 1, 'First interval should be first block timestamp');
        assertEq(obs.midTermTimeInterval[1], block.timestamp, 'Second interval should be block.timestamp');

        // verify long-term state
        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term is still 0');
        assertEq(obs.longTermTimeInterval[0], 1, 'longTermTimeInterval slot should be first block timestamp');
    }

    function testGeometricTWAP_WithOneMissedBlockAroundMidTermCycle() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        // skip a block
        fixture.generatePastBlocks(1);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // Skip to short term buffer cycle
        fixture.generateObservations(MID_TERM_ARRAY_LAST_INDEX, DEFAULT_TICK_VALUE);

        obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state prior to clearing the missing block
        assertEq(obs.midTermIndex, 0, 'midTermIndex is set to 0');
        assertEq(
            obs.midTermCumulativeSum[MID_TERM_ARRAY_LAST_INDEX],
            currentCumulativeTick,
            'Cumulative tick reflects the missed block'
        );
        assertEq(obs.midTermTimeInterval[0], 1, 'first observation interval should be first block timestamp');
        assertEq(
            obs.midTermTimeInterval[1],
            2 * 12 + 1, // 12s per block
            'next observation interval should include the missed block timestamp'
        );

        // verify mid-term state after clearing the missing block
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        obs = fixture.getObservationStruct();
        currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(obs.midTermCumulativeSum[0], currentCumulativeTick, 'Cumulative tick reflects the missed block');
        assertEq(obs.midTermTimeInterval[0], block.timestamp, 'First observation interval should be block.timestamp');
    }

    function testGeometricTWAP_WithOneMissedBlockAroundLongTermCycle() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        // Skip one long term interval
        fixture.generatePastBlocks(1);

        // Complete one long term buffer cycle
        fixture.generateObservations(DEFAULT_LONG_TERM_INTERVAL * 9 - 2, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // verify long-term state
        assertEq(obs.longTermIndex, 0, 'longTermIndex is set to 0');
        assertEq(
            obs.longTermCumulativeSum[LONG_TERM_ARRAY_LAST_INDEX],
            DEFAULT_TICK_VALUE * int256((DEFAULT_LONG_TERM_BUFFER) * 12), // 1 missed block is ignored, 12s per block
            'Cumulative tick reflects the missed block'
        );
        assertEq(
            obs.longTermTimeInterval[1],
            DEFAULT_LONG_TERM_INTERVAL * 12 + 1, // 1 missed block is ignored, 12s per block
            'Second interval still reflects the missing long term interval blocks'
        );

        // One more block resets the long term buffer to clear the missing block
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify long-term state
        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(
            obs.longTermCumulativeSum[0], currentCumulativeTick, 'Cumulative tick does not reflects the missed block'
        );
        assertEq(
            obs.longTermTimeInterval[0],
            block.timestamp,
            'First interval reflect the block.timestamp and resets the missed blocks'
        );
    }

    function testGeometricTWAP_OneMidTermRoundMissedBlock() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(MID_TERM_ARRAY_LAST_INDEX, DEFAULT_TICK_VALUE);

        // skip a block
        fixture.generatePastBlocks(1);
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state
        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(obs.midTermCumulativeSum[0], currentCumulativeTick, 'First slot updated with last block price');
        assertEq(obs.midTermTimeInterval[0], block.timestamp, 'Last interval should be block.timestamp');
    }

    function testGeometricTWAP_LongTermBufferBlocksWithMissingBlocks() public {
        uint24 longTermIntervalConfigFactor = 8;
        uint24 longTermIntervalConfig = MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * longTermIntervalConfigFactor;
        fixture = new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, longTermIntervalConfig, DEFAULT_TICK_VALUE);

        uint256 firstActiveBlocks = 1;
        // A large number of missed blocks occurring after the first active blocks, yet the system remains operational.
        uint256 missingAfterFirstActiveBlocks = Q32;
        uint256 secondActiveBlocks = longTermIntervalConfig * LONG_TERM_ARRAY_LAST_INDEX;

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.mineBlock(missingAfterFirstActiveBlocks);
        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE);

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_NineDaysLongTermBuffer() public {
        uint24 longTermIntervalConfig = 1 days; // 1 day in seconds
        fixture = new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, longTermIntervalConfig, DEFAULT_TICK_VALUE);

        // This will make it at the beginning of the 9th period (longTermIndex = 8)
        uint256 activeBlocks = Math.ceilDiv(longTermIntervalConfig * LONG_TERM_ARRAY_LAST_INDEX, 12); // 12s per block

        fixture.generateObservations(activeBlocks, DEFAULT_TICK_VALUE);

        fixture.verifyIndexes(block.timestamp - 1, longTermIntervalConfig);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_NegativeTicks() public {
        uint24 longTermIntervalConfigFactor = 24;
        uint24 longTermIntervalConfig = MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * longTermIntervalConfigFactor;
        fixture = new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, longTermIntervalConfig, DEFAULT_TICK_VALUE);

        uint256 activeBlocks = Math.ceilDiv(longTermIntervalConfig * (LONG_TERM_ARRAY_LAST_INDEX + 2), 12); // 12s per block
        int16 negativeTick = TickMath.MIN_TICK;

        fixture.generateObservations(activeBlocks, negativeTick);

        fixture.verifyIndexes(block.timestamp - 1, longTermIntervalConfig);
        fixture.verifyTicks(negativeTick);
    }

    function testGeometricTWAP_PositiveTicks() public {
        uint24 longTermIntervalConfigFactor = 24;
        uint24 longTermIntervalConfig = MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * longTermIntervalConfigFactor;
        fixture = new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, longTermIntervalConfig, DEFAULT_TICK_VALUE);

        uint256 activeBlocks = Math.ceilDiv(longTermIntervalConfig * (LONG_TERM_ARRAY_LAST_INDEX + 2), 12); // 12s per block
        int16 positiveTick = TickMath.MAX_TICK;

        fixture.generateObservations(activeBlocks, positiveTick);

        fixture.verifyIndexes(block.timestamp - 1, longTermIntervalConfig);
        fixture.verifyTicks(positiveTick);
    }

    function testGeometricTWAP_SimpleTicksWithMissingBlocks() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        uint256 firstActiveBlocks = 1;
        uint256 missingAfterFirstActiveBlocks = 150;
        uint256 secondActiveBlocks =
            Math.ceilDiv(MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * (LONG_TERM_ARRAY_LAST_INDEX + 1), 12); // 12s per block

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.mineBlock(missingAfterFirstActiveBlocks);
        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE);

        uint256 duration = block.timestamp - 150 * 12 - 1; // 150 missed blocks; 12s per block

        fixture.verifyIndexes(duration, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    // Simulate a sudden upward price movement at the last block which is limited by the `MAX_TICK_DELTA`.
    // When the price experiences a significant increase and the long-term value has not been updated,
    // the maximum value should be assigned to the current value, while the long-term value becomes the minimum.
    // In this case, "max" refers to the current tick allowed, and "min" refers to the long-term value.
    function testGeometricTWAP_PriceMovementUp() public {
        uint24 longTermIntervalConfig = 6 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        fixture = new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, longTermIntervalConfig, DEFAULT_TICK_VALUE);

        uint256 firstActiveBlocks = Math.ceilDiv(longTermIntervalConfig * (LONG_TERM_ARRAY_LAST_INDEX + 1), 12); // 12s per block
        uint256 secondActiveBlocks = 1;

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);

        int16 newTickAtTheEndBlock = TickMath.MAX_TICK;
        fixture.generateObservations(secondActiveBlocks, newTickAtTheEndBlock);

        fixture.verifyIndexes(block.timestamp - 1, longTermIntervalConfig);

        int16 currentTick = DEFAULT_TICK_VALUE + int16(MAX_TICK_DELTA); // outlier detection
        (int16 midTermTick,) = computeMidTermTicksAfterPriceShock(DEFAULT_TICK_VALUE, currentTick);
        int16 longTermTick = DEFAULT_TICK_VALUE;

        fixture.verifyTicks(longTermTick, midTermTick, currentTick, newTickAtTheEndBlock);
    }

    // Simulate a sudden downward price movement at the last block which is limited by the `MAX_TICK_DELTA`.
    // When the price experiences a significant decrease and the long-term value has just been updated,
    // the maximum value should be assigned to the long-term value, while the current value becomes the minimum.
    // In this case, "max" refers to the long-term value, and "min" refers to the current tick allowed.
    function testGeometricTWAP_PriceMovementDown() public {
        uint24 longTermIntervalConfig = 6 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        fixture = new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, longTermIntervalConfig, DEFAULT_TICK_VALUE);

        uint256 firstActiveBlocks = Math.ceilDiv(longTermIntervalConfig * (LONG_TERM_ARRAY_LAST_INDEX + 1), 12); // 12s per block
        uint256 secondActiveBlocks = 1;

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);

        int16 newTickAtTheEndBlock = TickMath.MIN_TICK;
        fixture.generateObservations(secondActiveBlocks, newTickAtTheEndBlock);

        fixture.verifyIndexes(block.timestamp - 1, longTermIntervalConfig);

        int16 currentTick = DEFAULT_TICK_VALUE - int16(MAX_TICK_DELTA); // outlier detection
        (int16 midTermTick,) = computeMidTermTicksAfterPriceShock(DEFAULT_TICK_VALUE, currentTick);
        int16 longTermTick = DEFAULT_TICK_VALUE;

        fixture.verifyTicks(longTermTick, midTermTick, currentTick, newTickAtTheEndBlock);
    }

    function testGeometricTWAP_FullyLoadBufferAfterIntervalChange() public {
        uint24 initialLongTermIntervalConfig = MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        uint24 longTermIntervalConfigFactor = 2;

        uint256 firstActiveBlocks = initialLongTermIntervalConfig * GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1; // long-term index = 8
        uint24 newInterval = initialLongTermIntervalConfig * longTermIntervalConfigFactor; // double the prior interval to config
        uint256 secondActiveBlocks = newInterval * GeometricTWAP.LONG_TERM_ARRAY_LENGTH; // long-term index = 8

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.changeInterval(newInterval);
        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        assertEq(obs.longTermIndex, 0, 'longTermIndex = 0');

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_LongTermTickChangesAfterConfigMovedUp() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generatePastBlocks(1);

        uint256 firstActiveBlocks = DEFAULT_LONG_TERM_INTERVAL * GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1;
        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);

        uint24 longTermIntervalConfig = 2 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        fixture.changeInterval(longTermIntervalConfig);

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_ReturnsSameValueAfterConfigMovedDown() public {
        uint24 longTermIntervalConfigFactor = 2;
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL,
            MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * longTermIntervalConfigFactor,
            DEFAULT_TICK_VALUE
        );

        uint256 firstActiveBlocks =
            DEFAULT_LONG_TERM_INTERVAL * longTermIntervalConfigFactor * GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1;
        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);

        fixture.verifyTicks(DEFAULT_TICK_VALUE);

        fixture.changeInterval(1 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_PartialLoadBufferAfterIntervalChange() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        uint256 firstActiveBlocks = DEFAULT_LONG_TERM_INTERVAL * GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1;
        uint24 longTermIntervalConfig = 2 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        uint24 nextInterval = DEFAULT_LONG_TERM_INTERVAL * longTermIntervalConfig;
        uint256 secondActiveBlocks = nextInterval - 1; // longTermIndex = 0

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.changeInterval(longTermIntervalConfig);
        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE);

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_ModifyLongTermIntervalWithMissingBlocks() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        /**
         * Testing a massive number of missing blocks, yet the system remains operational
         * 2_628_000 = 365 days * 24 * 60 * 60 / 12
         */
        uint256 missingAfterFirstActiveBlocks = 2_628_000;

        uint256 firstActiveBlocks = DEFAULT_LONG_TERM_INTERVAL * GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1;
        uint24 nextInterval = 7200; // 7200 = 24 hours blocks
        uint256 secondActiveBlocks = nextInterval / 12; // 12s per block

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE); // longTermIndex = 8
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 0, 'longTermIndex = 0');

        fixture.mineBlock(missingAfterFirstActiveBlocks);
        fixture.changeInterval(nextInterval);

        obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 0, 'longTermIndex');

        fixture.generateObservations(1, DEFAULT_TICK_VALUE); // longTermIndex = 0
        obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 1, 'longTermIndex');

        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE); // longTermIndex = 1
        obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 2, 'longTermIndex');

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_ChangeLongTermIntervalBeforeFullCycle() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        uint256 firstActiveBlocks = DEFAULT_LONG_TERM_INTERVAL * LONG_TERM_ARRAY_LAST_INDEX;
        uint24 secondInterval = 2 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        uint256 secondActiveBlocks = 1;
        uint24 thirdInterval = 3 * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
        uint256 thirdActiveBlocks = thirdInterval * DEFAULT_LONG_TERM_INTERVAL * 2;

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);
        fixture.changeInterval(secondInterval);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);

        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);

        // lack blocks for a fully loaded buffer after the second interval change
        fixture.changeInterval(thirdInterval);
        fixture.generateObservations(thirdActiveBlocks, DEFAULT_TICK_VALUE);

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_DecrementLongTermConfigToLowerThanCurrentIndex() public {
        uint24 initialLongTermIntervalConfigFactor = 2;
        uint24 initialLongTermIntervalConfig =
            MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG * initialLongTermIntervalConfigFactor;
        fixture =
            new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, initialLongTermIntervalConfig, DEFAULT_TICK_VALUE);

        // Fill the long-term buffer => (longTermInterval / 12s per block) * longTerm array length
        uint256 longTermBufferBlocks =
            Math.ceilDiv(initialLongTermIntervalConfig, 12) * (LONG_TERM_ARRAY_LAST_INDEX + 1) - 1;
        fixture.generateObservations(longTermBufferBlocks, DEFAULT_TICK_VALUE);

        // State before update to config
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        assertEq(obs.longTermIndex, 0, 'longTermIndex is set to 0');
        assertEq(
            obs.longTermTimeInterval[LONG_TERM_ARRAY_LAST_INDEX],
            Math.ceilDiv(initialLongTermIntervalConfig, 12) * LONG_TERM_ARRAY_LAST_INDEX * 12 + 1, // 12s per block
            'longTermInterval[last index] is set to time required to fill the long-term array'
        );

        uint24 nextLongTermIntervalConfig = MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;

        // config change does not impact readings
        fixture.verifyTicks(DEFAULT_TICK_VALUE);
        fixture.changeInterval(nextLongTermIntervalConfig);
        fixture.verifyTicks(DEFAULT_TICK_VALUE);

        // state after config change
        obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 0, 'longTermIndex is set to 0');

        // Iterating the block does not impact the price
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);
        obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 1, 'New long term observation recorded');
        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    function testGeometricTWAP_LendingStateTickFirstCallNotAvailable() public {
        int56 lendingStateTick = obsWithoutInitialization.getLendingStateTickAndCheckpoint(0, 0);
        assertEq(
            lendingStateTick,
            LENDING_TICK_NOT_AVAILABLE,
            'lendingStateTick is not available when calling for the first time'
        );
    }

    function testGeometricTWAP_LendingStateTickSameBlockCallUnchanged() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(DEFAULT_LONG_TERM_BUFFER, DEFAULT_TICK_VALUE);

        assertEq(fixture.getLendingStateTickAndCheckpoint(0, 0), DEFAULT_TICK_VALUE);

        assertEq(
            fixture.getLendingStateTickAndCheckpoint(0, 0),
            DEFAULT_TICK_VALUE,
            'lendingStateTick stays same when calling in the same block'
        );
    }

    function testGeometricTWAP_LendingStateTick_NotAvailable() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        // skip a block
        fixture.generatePastBlocks(1);
        fixture.generateObservations(1, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        fixture.generateObservations(65 - 2, DEFAULT_TICK_VALUE);

        obs = fixture.getObservationStruct();

        // trigger the first call to initial lastLendingBlock to current block.number
        fixture.getLendingStateTickAndCheckpoint(0, 0);

        uint256 blocks = 100;

        vm.roll(block.number + blocks);

        // generate ticks for the past blocks
        fixture.generateObservations(blocks, DEFAULT_TICK_VALUE);

        int16 lendingStateTick = fixture.getLendingStateTickAndCheckpoint(0, 0);

        // make sure the lendingStateTick is changed
        assertEq(lendingStateTick, DEFAULT_TICK_VALUE, 'lendingStateTick is set to DEFAULT_TICK_VALUE');
    }

    function testGeometricTWAP_LendingStateUpdateForOneBlock() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(DEFAULT_LONG_TERM_BUFFER, DEFAULT_TICK_VALUE);

        // emulate state update that would call the
        // obs.checkpointLendingCumulativeSum() function.
        uint32 nextTimestamp = GeometricTWAP.getCurrentTimestamp();
        fixture.getLendingStateTickAndCheckpoint(0, nextTimestamp - lendingTimestamp);
        lendingTimestamp = nextTimestamp;

        // roll the block
        int16 tickForNextBlock = 27;
        fixture.generateObservations(1, tickForNextBlock);
        fixture.getObservationStruct();

        // verify that the lending state is updated
        int16 lendingStateTick =
            fixture.getLendingStateTickAndCheckpoint(0, GeometricTWAP.getCurrentTimestamp() - lendingTimestamp);

        // The lending state tick isn't updated immediately because of using the `lastTick` for cumulative sum
        assertEq(lendingStateTick, tickForNextBlock, 'lendingStateTick is updated');
    }

    function testGeometricTWAP_LendingStateUpdateForTwoBlocks() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        fixture.generateObservations(DEFAULT_LONG_TERM_BUFFER, DEFAULT_TICK_VALUE);

        // emulate state update that would call the
        // obs.checkpointLendingCumulativeSum() function.
        uint32 nextTimestamp = GeometricTWAP.getCurrentTimestamp();
        fixture.getLendingStateTickAndCheckpoint(0, nextTimestamp - lendingTimestamp);
        lendingTimestamp = nextTimestamp;

        // roll the block
        int16 tickForNextBlock = 32;
        fixture.generateObservations(1, tickForNextBlock);

        int16 tickForNextBlock2 = 37;
        fixture.generateObservations(1, tickForNextBlock2);

        // verify that the lending state is updated
        int16 lendingStateTick =
            fixture.getLendingStateTickAndCheckpoint(0, GeometricTWAP.getCurrentTimestamp() - lendingTimestamp);

        assertEq(lendingStateTick, (tickForNextBlock + tickForNextBlock2) / 2, 'lendingStateTick is updated');
    }

    function testGeometricTWAP_MidTermIntervalConfigs() public {
        uint8[5] memory midTermIntervalConfigs = [24, 36, 48, 60, 120];
        uint8[5] memory longTermIntervalConfigFactors = [2, 2, 1, 1, 1];

        for (uint8 i = 0; i < midTermIntervalConfigs.length; i++) {
            uint24 midTermIntervalConfig = midTermIntervalConfigs[i];
            uint24 longTermIntervalConfigFactor = longTermIntervalConfigFactors[i];

            // Mid-term interval config test cases
            testGeometricTWAP_MidTermIntervalConfig_Initialize(
                midTermIntervalConfig, longTermIntervalConfigFactor * midTermIntervalConfig * 14
            );
            testGeometricTWAP_MidTermIntervalConfig_WithMissingBlocks(
                midTermIntervalConfig, longTermIntervalConfigFactor * midTermIntervalConfig * 14
            );
            testGeometricTWAP_MidTermIntervalConfig_AfterOneLapAroundMidTermBuffer(
                midTermIntervalConfig, longTermIntervalConfigFactor * midTermIntervalConfig * 14
            );
            testGeometricTWAP_MidTermIntervalConfig_AfterOneLongTermInterval(
                midTermIntervalConfig, longTermIntervalConfigFactor * midTermIntervalConfig * 14
            );
            testGeometricTWAP_MidTermIntervalConfig_AfterOneLapAroundLongTermBuffer(
                midTermIntervalConfig, longTermIntervalConfigFactor * midTermIntervalConfig * 14
            );
        }
    }

    /**
     * Test cases for `minTick` or `maxTick` to default to `TickMath.MIN_TICK` & `TickMath.MAX_TICK`,
     * when outside the tick range for a given `factor` where `8` => `longTerm` array is `100%` filled.
     */

    /**
     * delta > TickMath.MAX_TICK - currentTick
     * delta > currentTick - TickMath.MIN_TICK
     * factor = 8
     * The result is `TickMath.MIN_TICK` & `TickMath.MAX_TICK` which would pass both the if conditions for tick range bounds check.
     */
    function testGeometricTWAP_TickRange_MinAndMaxTickBound() public pure {
        int16 long = TickMath.MIN_TICK + 1;
        int16 mid = 2;
        int16 spot = 0;

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, 8);
        assertEq(minTick, TickMath.MIN_TICK, 'minTick');
        assertEq(maxTick, TickMath.MAX_TICK, 'maxTick');
    }

    /**
     * Considering,
     *   ```math
     *   TickMath.MIN_TICK < currentTick - delta - \frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX}
     *   ```
     * with $factor = 3$, $delta = long - spot$,
     *   ```math
     *   long < 2 \cdot spot - \frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} - TickMath.MIN_TICK
     *   ```
     * Here we test long where the above does not hold.
     *
     */
    function testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz(
        int16 long
    ) public pure {
        uint16 factor = 3;
        int256 factorTerm = int256(uint256(LONG_TERM_ARRAY_LAST_INDEX) - factor)
            * int256(GeometricTWAP.LOG_BASE_OF_ROOT_TWO) / int256(LONG_TERM_ARRAY_LAST_INDEX);
        int16 spot = TickMath.MIN_TICK / 3;
        int16 mid = spot + 1;
        long = int16(bound(long, spot * 2 - TickMath.MIN_TICK - factorTerm, TickMath.MAX_TICK));
        uint16 delta = uint16(long - spot);
        int16 buffer = int16(int16(delta) + factorTerm);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        assertEq(minTick, TickMath.MIN_TICK, 'minTick');
        int16 expectedMax = spot + buffer + 1;
        assertEq(maxTick, TickMath.MAX_TICK < expectedMax ? TickMath.MAX_TICK : expectedMax, 'maxTick');
    }

    function testGeometricTWAP_TickRange_MinTickBound_MidMax_Fuzz(
        int16 mid
    ) public pure {
        uint16 factor = 4;
        int256 factorTerm = int256(uint256(LONG_TERM_ARRAY_LAST_INDEX) - factor)
            * int256(GeometricTWAP.LOG_BASE_OF_ROOT_TWO) / int256(LONG_TERM_ARRAY_LAST_INDEX);
        int16 spot = TickMath.MIN_TICK / 4;
        int16 long = spot + 1;
        mid = int16(bound(mid, spot * 2 - TickMath.MIN_TICK - factorTerm, TickMath.MAX_TICK));
        uint16 delta = uint16(mid - spot);
        int16 buffer = int16(int16(delta) + factorTerm);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        assertEq(minTick, TickMath.MIN_TICK, 'minTick');
        int16 expectedMax = spot + buffer + 1;
        assertEq(maxTick, TickMath.MAX_TICK < expectedMax ? TickMath.MAX_TICK : expectedMax, 'maxTick');
    }

    /**
     * Considering
     *   ```math
     *   currentTick + delta + \frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} < TickMath.MAX_TICK
     *   ```
     * with $factor = 5$, $delta = spot - long$,
     *   ```math
     *   2 \cdot spot - TickMath.MAX_TICK + \frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} < long
     */
    function testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz(
        int16 long
    ) public pure {
        uint16 factor = 5;
        int16 spot = TickMath.MAX_TICK / 5;
        int16 mid = 0;
        long = int16(
            bound(
                long,
                TickMath.MIN_TICK,
                spot * 2 - TickMath.MAX_TICK
                    + int256(uint256(LONG_TERM_ARRAY_LAST_INDEX) - factor) * int256(GeometricTWAP.LOG_BASE_OF_ROOT_TWO)
                        / int256(LONG_TERM_ARRAY_LAST_INDEX)
            )
        );
        uint16 delta = uint16(spot - long); // < TickMath.MAX_TICK - currentTick - (LONG_TERM_ARRAY_LAST_INDEX - factor) * LOG_BASE_OF_ROOT_TWO / LONG_TERM_ARRAY_LAST_INDEX
        int16 buffer = int16(
            uint16(
                delta
                    + (LONG_TERM_ARRAY_LAST_INDEX - factor) * GeometricTWAP.LOG_BASE_OF_ROOT_TWO
                        / LONG_TERM_ARRAY_LAST_INDEX
            )
        );

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        int16 expectedMin = spot - buffer;
        assertEq(minTick, expectedMin < TickMath.MIN_TICK ? TickMath.MIN_TICK : expectedMin, 'minTick');
        assertEq(maxTick, TickMath.MAX_TICK, 'maxTick');
    }

    function testGeometricTWAP_TickRange_MaxTickBound_MidMin_Fuzz(
        int16 mid
    ) public pure {
        uint16 factor = 6;
        int16 spot = TickMath.MAX_TICK / 6;
        int16 long = 0;
        mid = int16(
            bound(
                mid,
                TickMath.MIN_TICK,
                spot * 2 - TickMath.MAX_TICK
                    + int256(uint256(LONG_TERM_ARRAY_LAST_INDEX) - factor) * int256(GeometricTWAP.LOG_BASE_OF_ROOT_TWO)
                        / int256(LONG_TERM_ARRAY_LAST_INDEX)
            )
        );
        uint16 delta = uint16(spot - mid); // < TickMath.MAX_TICK - currentTick - (LONG_TERM_ARRAY_LAST_INDEX - factor) * LOG_BASE_OF_ROOT_TWO / LONG_TERM_ARRAY_LAST_INDEX
        int16 buffer = int16(
            uint16(
                delta
                    + (LONG_TERM_ARRAY_LAST_INDEX - factor) * GeometricTWAP.LOG_BASE_OF_ROOT_TWO
                        / LONG_TERM_ARRAY_LAST_INDEX
            )
        );

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        int16 expectedMin = spot - buffer;
        assertEq(minTick, expectedMin < TickMath.MIN_TICK ? TickMath.MIN_TICK : expectedMin, 'minTick');
        assertEq(maxTick, TickMath.MAX_TICK, 'maxTick');
    }

    function testGeometricTWAP_TickRange_PriceMovementSharplyDown() public pure {
        int16 long = 112;
        int16 mid = 22;
        int16 spot = TickMath.MIN_TICK;

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, 8);
        assertEq(minTick, spot, 'minTick');
        assertEq(maxTick, long + 1, 'maxTick');
    }

    function testGeometricTWAP_TickRange_PriceMovementSharplyUp() public pure {
        int16 long = 22;
        int16 mid = 11;
        int16 spot = TickMath.MAX_TICK;

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, 8);
        assertEq(minTick, mid, 'minTick');
        assertEq(maxTick, spot, 'maxTick');
    }

    function testGeometricTWAP_TickRanges() public pure {
        for (uint24 i = 1; i < 9; i++) {
            exerciseGeometricTWAP_TickRange_LongMidSpot(i);
            exerciseGeometricTWAP_TickRange_LongSpotMid(i);
            exerciseGeometricTWAP_TickRange_SpotMidLong(i);
            exerciseGeometricTWAP_TickRange_SpotLongMid(i);
            exerciseGeometricTWAP_TickRange_MidSpotLong(i);
            exerciseGeometricTWAP_TickRange_MidLongSpot(i);
            exerciseGeometricTWAP_TickRange_SpotEqualsMidLow(i);
            exerciseGeometricTWAP_TickRange_SpotEqualsMidHigh(i);
            exerciseGeometricTWAP_TickRange_SpotEqualsLongLow(i);
            exerciseGeometricTWAP_TickRange_SpotEqualsLongHigh(i);
        }
    }

    function testGeometricTWAP_TickRange_LongTermIndexAsFactor() public {
        uint256 midTermBufferInterval = MID_TERM_ARRAY_LAST_INDEX;

        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        for (uint16 i = 0; i < DEFAULT_LONG_TERM_BUFFER + 1; i++) {
            uint16 interval = i + 1;
            if (i > 0) {
                fixture.generateObservations(1, DEFAULT_TICK_VALUE);
            }
            obs = fixture.getObservationStruct();

            int16 cumulativeTick = int16(i * 12) * DEFAULT_TICK_VALUE; // 12s per block
            uint16 factor = i / DEFAULT_LONG_TERM_INTERVAL;

            int16 midTermTick = int16(
                interval > midTermBufferInterval || i == 0
                    ? DEFAULT_TICK_VALUE
                    : cumulativeTick / int256(uint256(i * 12)) // 12s per block
            );
            int16 longTermTick = int16(
                interval > DEFAULT_LONG_TERM_BUFFER || factor <= LONG_TERM_ARRAY_LAST_INDEX || i == 0
                    ? DEFAULT_TICK_VALUE
                    : obs.longTermCumulativeSum[factor] / int256(uint256(i * 12)) // 12s per block
            );

            // verify long-term state, only if long-term is recorded
            if (i % DEFAULT_LONG_TERM_INTERVAL == 0) {
                assertEq(
                    obs.longTermCumulativeSum[factor],
                    cumulativeTick,
                    'Slot updated with last recorded long-term observation'
                );
            }

            // verify ticks
            (int16 _longTermTick, int16 _midTermTick, int16 _lastTick) =
                fixture.getObservedTicks(factor > LONG_TERM_ARRAY_LAST_INDEX);
            assertEq(longTermTick, _longTermTick, 'longTermTick');
            assertEq(midTermTick, _midTermTick, 'midTermTick');
            assertEq(DEFAULT_TICK_VALUE, _lastTick, 'lastTick');

            // verify tick range
            (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(
                longTermTick, midTermTick, DEFAULT_TICK_VALUE, DEFAULT_TICK_VALUE, factor
            );
            verifyMinAndMaxTick(minTick, maxTick, DEFAULT_TICK_VALUE, uint16(DEFAULT_TICK_VALUE - longTermTick), factor);
        }
    }

    function testGeometricTWAP_TickRange_LongTermIndexAsFactorWithMissingBlocks() public {
        uint256 midTermBufferInterval = MID_TERM_ARRAY_LAST_INDEX;

        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        uint256 totalMissedBlocks = 0;
        uint256 prevLongTermIndex = 0;

        for (uint16 i = 0; i < DEFAULT_LONG_TERM_BUFFER + 1; i++) {
            uint16 interval = i + 1;
            if (i > 0) {
                // verify the state after initializing before generating an observation.
                fixture.generateObservations(1, DEFAULT_TICK_VALUE);
            }
            obs = fixture.getObservationStruct();

            int24 cumulativeTick = int24(uint24((i + totalMissedBlocks) * 12)) * int24(DEFAULT_TICK_VALUE); // 12s per block
            uint256 priorPrevLongTermIndex = prevLongTermIndex;

            prevLongTermIndex = (
                (
                    GeometricTWAP.getCurrentTimestamp() - obs.longTermTimeInterval[prevLongTermIndex]
                        >= DEFAULT_MID_TERM_INTERVAL * 14 ? 1 : 0
                ) + prevLongTermIndex
            ) % GeometricTWAP.LONG_TERM_ARRAY_LENGTH;

            assertEq(
                obs.longTermIndex,
                (prevLongTermIndex + 1) % GeometricTWAP.LONG_TERM_ARRAY_LENGTH,
                'Verify we are on the correct index for the long-term array'
            );

            int16 midTermTick = int16(
                interval > midTermBufferInterval
                    ? DEFAULT_TICK_VALUE
                    : cumulativeTick / int256(midTermBufferInterval * 12) // 12s per block
            );

            int16 longTermTick = int16(
                interval > DEFAULT_LONG_TERM_BUFFER
                    ? DEFAULT_TICK_VALUE
                    : obs.longTermCumulativeSum[prevLongTermIndex] / int256(DEFAULT_LONG_TERM_BUFFER * 12) // 12s per block
            );

            // verify mid-term state for every recorded observation
            assertEq(
                obs.midTermCumulativeSum[i % GeometricTWAP.MID_TERM_ARRAY_LENGTH],
                cumulativeTick,
                'Slot updated with last recorded mid-term observation'
            );

            // generate missed blocks and verify long-term state, only if long-term is recorded
            if (i > 0 && priorPrevLongTermIndex < prevLongTermIndex) {
                // `missedBlocks` = `prevLongTermIndex`
                fixture.generatePastBlocks(prevLongTermIndex);
                totalMissedBlocks += prevLongTermIndex;

                assertEq(
                    obs.longTermCumulativeSum[prevLongTermIndex],
                    cumulativeTick,
                    'Slot updated with last recorded long-term observation'
                );
            }

            uint256 factor = prevLongTermIndex;

            // verify tick range
            (int16 minTick, int16 maxTick) =
                getTickRange(longTermTick, midTermTick, DEFAULT_TICK_VALUE, DEFAULT_TICK_VALUE);
            (int16 _minTick, int16 _maxTick) = GeometricTWAP.getTickRangeInternal(
                longTermTick, midTermTick, DEFAULT_TICK_VALUE, DEFAULT_TICK_VALUE, factor
            );
            verifyMinAndMaxTick(_minTick, _maxTick, DEFAULT_TICK_VALUE, uint16(maxTick - minTick), factor);
        }
    }

    function testGeometricTWAP_TickRange_VerifyTicksAfterIntervalChange() public {
        uint24 initialLongTermIntervalConfig = DEFAULT_LONG_TERM_INTERVAL;
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );

        uint256 firstActiveBlocks = initialLongTermIntervalConfig * GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1; // long-term index = 0
        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);

        uint24 newLongTermIntervalConfigFactor = 10;
        uint24 newInterval = initialLongTermIntervalConfig * newLongTermIntervalConfigFactor; // 160

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        fixture.changeInterval(newLongTermIntervalConfigFactor * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);

        for (uint24 i = 0; i < GeometricTWAP.LONG_TERM_ARRAY_LENGTH; i++) {
            fixture.generateObservations(newInterval, DEFAULT_TICK_VALUE);

            obs = fixture.getObservationStruct();

            assertEq(obs.longTermIndex, (i + 1) % GeometricTWAP.LONG_TERM_ARRAY_LENGTH, 'longTermIndex = i');
            fixture.verifyTicks(DEFAULT_TICK_VALUE);
        }
    }

    function testGeometricTWAP_TickRange_PriceUpwards() public {
        int16 minTick;
        int16 maxTick;

        int16 firstTick = 10;
        fixture =
            new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, firstTick);

        fixture.generateObservations(1000, firstTick);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        (minTick, maxTick,) = fixture.getTicks(firstTick);

        assertEq(minTick, firstTick, 'minTick should be 10');
        assertEq(maxTick, firstTick + 1, 'minTick should be 11');

        // big price change
        int16 newTick = 50;

        fixture.generateObservations(1, newTick);
        obs = fixture.getObservationStruct();

        (minTick, maxTick,) = fixture.getTicks(newTick);
        assertEq(minTick, -20, 'minTick should be -20');
        assertEq(maxTick, 61, 'maxTick should be 61');
    }

    function testGeometricTWAP_TickRange_PriceDownwards() public {
        int16 minTick;
        int16 maxTick;

        int16 firstTick = 10;
        fixture =
            new GeometricTWAPTestFixture(DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, firstTick);

        fixture.generateObservations(1000, firstTick);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();
        (minTick, maxTick,) = fixture.getTicks(firstTick);

        assertEq(minTick, firstTick, 'minTick should be 10');
        assertEq(maxTick, firstTick + 1, 'minTick should be 11');

        // big price change
        int16 newTick = -50;

        fixture.generateObservations(1, newTick);
        obs = fixture.getObservationStruct();

        (minTick, maxTick,) = fixture.getTicks(newTick);
        assertEq(minTick, -60, 'minTick should be -60');
        assertEq(maxTick, 61, 'maxTick should be 61');
    }

    function testGeometricTWAP_Outlier_MaxTickDelta() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        fixture.generateObservations(1, DEFAULT_TICK_VALUE);
        obs = fixture.getObservationStruct();

        int16 currentCumulativeTick = DEFAULT_TICK_VALUE * int16(int256(block.timestamp - 1));

        // verify mid-term state after recorded observation
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is 0');
        assertEq(obs.midTermCumulativeSum[1], currentCumulativeTick, 'Second slot updated with new tick');

        // Record observation with `newTick = MAX_TICK`
        fixture.generateObservations(1, TickMath.MAX_TICK);
        obs = fixture.getObservationStruct();

        int16 maxTickDelta = int16(int256(LONG_TERM_ARRAY_LAST_INDEX) * MAX_TICK_DELTA); // outlier detection
        int16 recordedTick = DEFAULT_TICK_VALUE + maxTickDelta;
        int16 cumulativeTick = (recordedTick + DEFAULT_TICK_VALUE) * 12; // 12s per block

        // verify mid-term state after recorded observation
        assertEq(obs.midTermCumulativeSum[2], cumulativeTick, 'Third slot updated with lastTick + MAX_TICK_DELTA');
        assertEq(obs.lastTick, recordedTick, 'lastTick is updated with recordedTick');

        // Record observation with `newTick = MIN_TICK`
        fixture.generateObservations(1, TickMath.MIN_TICK);
        obs = fixture.getObservationStruct();

        recordedTick -= maxTickDelta;
        cumulativeTick += recordedTick * 12; // 12s per block

        // verify mid-term state after recorded observation
        assertEq(obs.midTermCumulativeSum[3], cumulativeTick, 'Fourth slot updated with lastTick - MAX_TICK_DELTA');
        assertEq(obs.lastTick, recordedTick, 'lastTick is updated with recordedTick');
    }

    function testGeometricTWAP_Outlier_PriceGoesUp() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        int16 maxTickDelta = int16(int256(LONG_TERM_ARRAY_LAST_INDEX) * MAX_TICK_DELTA); // outlier detection
        int16 expectedTick = DEFAULT_TICK_VALUE + (maxTickDelta * int16(DEFAULT_LONG_TERM_INTERVAL));

        // Record observation with price going up
        fixture.generateObservations(DEFAULT_LONG_TERM_INTERVAL, expectedTick);
        obs = fixture.getObservationStruct();

        // verify mid-term state after recorded observation
        assertEq(obs.lastTick, expectedTick, 'lastTick == expectedTick');
    }

    function testGeometricTWAP_Outlier_PriceGoesDown() public {
        fixture = new GeometricTWAPTestFixture(
            DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG, DEFAULT_TICK_VALUE
        );
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        int16 maxTickDelta = int16(int256(LONG_TERM_ARRAY_LAST_INDEX) * MAX_TICK_DELTA); // outlier detection
        int16 expectedTick = -(maxTickDelta * int16(DEFAULT_LONG_TERM_INTERVAL));

        // Record observation with price going up
        fixture.generateObservations(DEFAULT_LONG_TERM_INTERVAL, expectedTick);
        obs = fixture.getObservationStruct();

        // verify mid-term state after recorded observation
        assertEq(obs.lastTick, expectedTick + DEFAULT_TICK_VALUE, 'lastTick == expectedTick');
    }

    /**
     * Utils
     */
    function computeLongTermTicksAfterPriceShock(
        uint56 blocks,
        int16 priorTick,
        int16 newTick,
        uint24 longTermIntervalConfig
    ) private pure returns (int16 longTermTick, int56 longTermSumTick) {
        int56 currentCumulative = int56(priorTick) * int56(blocks) + int56(newTick);
        int56 firstCumulative = int56(priorTick);
        longTermSumTick = (currentCumulative - firstCumulative);
        longTermTick = int16(longTermSumTick / int24((longTermIntervalConfig * uint24(LONG_TERM_ARRAY_LAST_INDEX))));
    }

    function computeMidTermTicksAfterPriceShock(
        int16 priorTick,
        int16 newTick
    ) private pure returns (int16 midTermTick, int56 midTermSumTick) {
        int256 currentCumulative = priorTick * int256(MID_TERM_ARRAY_LAST_INDEX) + newTick;
        int256 firstCumulative = priorTick;
        midTermSumTick = int56(currentCumulative - firstCumulative);
        midTermTick = int16(midTermSumTick / int256(MID_TERM_ARRAY_LAST_INDEX));
    }

    /**
     * Mid-term interval configuration test cases
     */
    function testGeometricTWAP_MidTermIntervalConfig_Initialize(
        uint24 midTermIntervalConfig,
        uint24 longTermIntervalConfigFactor
    ) private {
        uint256 initialBlockTimestamp = block.timestamp;
        fixture = new GeometricTWAPTestFixture(midTermIntervalConfig, longTermIntervalConfigFactor, DEFAULT_TICK_VALUE);
        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // verify mid-term interval config
        assertEq(obs.midTermIntervalConfig, midTermIntervalConfig, 'midTermIntervalConfig');

        // verify mid-term state after initialization
        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is still 0');
        assertEq(obs.midTermTimeInterval[0], initialBlockTimestamp, 'First mid-term interval');

        fixture.generateObservations(midTermIntervalConfig / 12, DEFAULT_TICK_VALUE); // 12s per block
        obs = fixture.getObservationStruct();

        // verify mid-term state after second recorded observation
        assertEq(obs.midTermIndex, 2, 'midTermIndex is set to 2');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is still 0');
        assertEq(
            obs.midTermCumulativeSum[1],
            int24(midTermIntervalConfig) * DEFAULT_TICK_VALUE,
            'Second slot updated with last block price'
        );
        assertEq(obs.midTermTimeInterval[0], initialBlockTimestamp, 'First mid-term interval');
        assertEq(obs.midTermTimeInterval[1], block.timestamp, 'Second mid-term interval');
    }

    function testGeometricTWAP_MidTermIntervalConfig_WithMissingBlocks(
        uint24 midTermIntervalConfig,
        uint24 longTermIntervalConfigFactor
    ) private {
        uint256 initialBlockTimestamp = block.timestamp;
        fixture = new GeometricTWAPTestFixture(midTermIntervalConfig, longTermIntervalConfigFactor, DEFAULT_TICK_VALUE);

        uint24 missedBlocks = 1;

        // `missedBlocks` must be greater than `midTermIntervalConfig` to be recorded.
        fixture.generatePastBlocks(midTermIntervalConfig / 12); // 12s per block
        fixture.generateObservations(missedBlocks, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // verify mid-term state
        assertEq(obs.midTermIndex, 2, 'midTermIndex is set to 2');
        assertEq(obs.midTermCumulativeSum[0], 0, 'First slot is still 0');
        assertEq(
            obs.midTermCumulativeSum[1],
            int24(midTermIntervalConfig + 12) * DEFAULT_TICK_VALUE, // 12s per block
            'Second slot updated with second block price including missing block'
        );
        assertEq(obs.midTermTimeInterval[0], initialBlockTimestamp, 'First mid-term interval includes missing block');
        assertEq(obs.longTermTimeInterval[0], initialBlockTimestamp, 'First long-term interval includes missing block');

        uint256 secondBlockTimestamp = block.timestamp;

        // `missedBlocks` must be greater than `midTermIntervalConfig` to be recorded.
        fixture.generatePastBlocks(midTermIntervalConfig / 12); // 12s per block
        fixture.generateObservations(missedBlocks, DEFAULT_TICK_VALUE);
        obs = fixture.getObservationStruct();

        // verify mid-term state
        assertEq(obs.midTermIndex, 3, 'midTermIndex is set to 3');
        assertEq(
            obs.midTermCumulativeSum[2],
            int24(2 * (midTermIntervalConfig + 12)) * DEFAULT_TICK_VALUE, // 2 slots includes missed blocks
            'Third slot updated with third block price including missing block'
        );
        assertEq(obs.midTermTimeInterval[1], secondBlockTimestamp, 'Second mid-term interval includes missing block');
        assertEq(
            obs.longTermTimeInterval[0],
            initialBlockTimestamp, // 2 slots includes missed blocks
            'First long-term interval includes missing block'
        );
    }

    function testGeometricTWAP_MidTermIntervalConfig_AfterOneLapAroundMidTermBuffer(
        uint24 midTermIntervalConfig,
        uint24 longTermIntervalConfigFactor
    ) private {
        uint256 midTermBufferInterval = (midTermIntervalConfig / 12) * MID_TERM_ARRAY_LAST_INDEX; // 12s per block
        int56 midTermBufferTick = DEFAULT_TICK_VALUE * int56(midTermIntervalConfig * uint56(MID_TERM_ARRAY_LAST_INDEX));
        fixture = new GeometricTWAPTestFixture(midTermIntervalConfig, longTermIntervalConfigFactor, DEFAULT_TICK_VALUE);
        fixture.generateObservations(midTermBufferInterval, DEFAULT_TICK_VALUE);

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // verify mid-term state
        assertEq(obs.midTermIndex, 0, 'midTermIndex is set to 0');
        assertEq(
            obs.midTermCumulativeSum[MID_TERM_ARRAY_LAST_INDEX],
            midTermBufferTick,
            'Last slot updated with last block price'
        );
        assertEq(
            obs.midTermTimeInterval[MID_TERM_ARRAY_LAST_INDEX],
            block.timestamp,
            'last observation interval should be block.timestamp'
        );

        // verify state updates as they overwrite first values
        fixture.generateObservations(midTermIntervalConfig / 12, DEFAULT_TICK_VALUE); // 12s per block

        obs = fixture.getObservationStruct();

        assertEq(obs.midTermIndex, 1, 'midTermIndex is set to 1');
        assertEq(
            obs.midTermCumulativeSum[0],
            midTermBufferTick + int24(midTermIntervalConfig) * DEFAULT_TICK_VALUE,
            'First slot updated with current cumulative price'
        );
        assertEq(obs.midTermTimeInterval[0], block.timestamp, 'first observation interval should be block.timestamp');
    }

    function testGeometricTWAP_MidTermIntervalConfig_AfterOneLongTermInterval(
        uint24 midTermIntervalConfig,
        uint24 longTermIntervalConfig // now old * 8 * 14
    ) private {
        uint256 initialBlockTimestamp = block.timestamp;

        fixture = new GeometricTWAPTestFixture(midTermIntervalConfig, longTermIntervalConfig, DEFAULT_TICK_VALUE);
        fixture.generateObservations(longTermIntervalConfig / 12, DEFAULT_TICK_VALUE); // 12s per block

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        uint24 currentMidTermIndex = uint24(longTermIntervalConfig / midTermIntervalConfig);
        int24 currentCumulativeTick = DEFAULT_TICK_VALUE * int24(longTermIntervalConfig);

        // verify mid-term state
        assertEq(obs.midTermIndex, currentMidTermIndex + 1, 'midTermIndex');
        assertEq(
            obs.midTermCumulativeSum[currentMidTermIndex],
            currentCumulativeTick,
            '8th slot updated with last recorded tick'
        );
        assertEq(
            obs.midTermTimeInterval[currentMidTermIndex],
            block.timestamp,
            'current mid-term observation interval should be block.timestamp'
        );

        // verify long-term state
        assertEq(obs.longTermIndex, 2, 'longTermIndex is set to 2');
        assertEq(obs.longTermCumulativeSum[0], 0, 'First long-term slot is still 0');
        assertEq(
            obs.longTermCumulativeSum[1], currentCumulativeTick, 'Second long-term slot updated with first block price'
        );
        assertEq(obs.longTermCumulativeSum[2], 0, 'Third long-term slot should be 0');
        assertEq(
            obs.longTermTimeInterval[0],
            initialBlockTimestamp,
            'First long-term interval should be longTermIntervalConfig'
        );
        assertEq(
            obs.longTermTimeInterval[1], block.timestamp, 'Second long-term interval should be longTermIntervalConfig'
        );
        assertEq(obs.longTermTimeInterval[2], 0, 'Third long-term interval should be 0');
    }

    function testGeometricTWAP_MidTermIntervalConfig_AfterOneLapAroundLongTermBuffer(
        uint24 midTermIntervalConfig,
        uint24 longTermIntervalConfig // now old * 8 * 14
    ) private {
        uint256 longTermBufferInterval = GeometricTWAP.LONG_TERM_ARRAY_LENGTH * longTermIntervalConfig;
        int256 longTermBufferTick = int256(longTermBufferInterval) * DEFAULT_TICK_VALUE;

        fixture = new GeometricTWAPTestFixture(midTermIntervalConfig, longTermIntervalConfig, DEFAULT_TICK_VALUE);
        fixture.generateObservations(
            (longTermIntervalConfig * LONG_TERM_ARRAY_LAST_INDEX) / 12,
            DEFAULT_TICK_VALUE // 12s per block
        );

        GeometricTWAP.Observations memory obs = fixture.getObservationStruct();

        // verify long-term state
        assertEq(obs.longTermIndex, 0, 'longTermIndex is set to 0');
        assertEq(
            obs.longTermCumulativeSum[LONG_TERM_ARRAY_LAST_INDEX],
            DEFAULT_TICK_VALUE * int256(LONG_TERM_ARRAY_LAST_INDEX * uint256(longTermIntervalConfig)),
            'Last slot updated with last recorded long-term observation'
        );
        assertEq(
            obs.longTermTimeInterval[LONG_TERM_ARRAY_LAST_INDEX],
            block.timestamp,
            'last observation interval should be block.timestamp'
        );

        // verify state updates as they overwrite first values
        fixture.generateObservations(longTermIntervalConfig / 12, DEFAULT_TICK_VALUE); // 12s per block

        obs = fixture.getObservationStruct();
        assertEq(obs.longTermIndex, 1, 'longTermIndex is set to 1');
        assertEq(obs.longTermCumulativeSum[0], longTermBufferTick, 'First slot updated with current cumulative price');
        assertEq(obs.longTermTimeInterval[0], block.timestamp, 'first observation interval should be block.timestamp');

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }

    /**
     * Unit test cases for `GeometricTWAP.getTickRangeInternal` including `factor` parameter.
     * All the different possibilities of the order from lowest to greatest price of the below prices:
     * spot price (S), mid-term price (M), and the long-term price (L)
     */
    function exerciseGeometricTWAP_TickRange_LongMidSpot(
        uint24 factor
    ) private pure {
        int16 long = -3;
        int16 mid = -2;
        int16 spot = -1;
        uint16 delta = uint16(spot - long);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_LongSpotMid(
        uint24 factor
    ) private pure {
        int16 long = 4;
        int16 mid = 6;
        int16 spot = 5;
        uint16 delta = uint16(mid - long);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_SpotMidLong(
        uint24 factor
    ) private pure {
        int16 long = 2;
        int16 mid = 1;
        int16 spot = 0;
        uint16 delta = uint16(long - spot);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_SpotLongMid(
        uint24 factor
    ) private pure {
        int16 long = 2;
        int16 mid = 3;
        int16 spot = 1;
        uint16 delta = uint16(mid - spot);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_MidSpotLong(
        uint24 factor
    ) private pure {
        int16 long = 5;
        int16 mid = 3;
        int16 spot = 4;
        uint16 delta = uint16(long - mid);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_MidLongSpot(
        uint24 factor
    ) private pure {
        int16 long = 7;
        int16 mid = 4;
        int16 spot = 12;
        uint16 delta = uint16(spot - mid);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_SpotEqualsMidLow(
        uint24 factor
    ) private pure {
        int16 long = 23;
        int16 mid = 18;
        int16 spot = 18;
        uint16 delta = uint16(long - spot);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_SpotEqualsMidHigh(
        uint24 factor
    ) private pure {
        int16 long = 16;
        int16 mid = 24;
        int16 spot = 24;
        uint16 delta = uint16(spot - long);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_SpotEqualsLongLow(
        uint24 factor
    ) private pure {
        int16 long = 16;
        int16 mid = 25;
        int16 spot = 16;
        uint16 delta = uint16(mid - spot);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function exerciseGeometricTWAP_TickRange_SpotEqualsLongHigh(
        uint24 factor
    ) private pure {
        int16 long = 22;
        int16 mid = 18;
        int16 spot = 22;
        uint16 delta = uint16(spot - mid);

        (int16 minTick, int16 maxTick) = GeometricTWAP.getTickRangeInternal(long, mid, spot, spot, factor);
        verifyMinAndMaxTick(minTick, maxTick, spot, delta, factor);
    }

    function verifyMinAndMaxTick(int16 minTick, int16 maxTick, int16 spot, uint16 delta, uint256 factor) private pure {
        int24 buffer = int24(
            uint24(
                delta
                    + GeometricTWAP.LOG_BASE_OF_ROOT_TWO * (LONG_TERM_ARRAY_LAST_INDEX - factor)
                        / LONG_TERM_ARRAY_LAST_INDEX
            )
        );

        assertEq(minTick, spot - buffer, 'minTick');
        assertEq(maxTick, spot + buffer + 1, 'maxTick');
    }
}
