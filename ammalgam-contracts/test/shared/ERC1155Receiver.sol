// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {IERC1155R<PERSON>eiver} from '@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol';

contract ERC1155Receiver is IERC1155<PERSON><PERSON>eiver {
    function supportsInterface(
        bytes4 /* interfaceId */
    ) public view virtual override returns (bool) {
        revert('this implementation is not used for test but required to implement in ERC165');
    }

    function onERC1155Received(
        address,
        address,
        uint256,
        uint256,
        bytes memory
    ) public virtual override returns (bytes4) {
        return this.onERC1155Received.selector;
    }

    function onERC1155BatchReceived(
        address,
        address,
        uint256[] memory,
        uint256[] memory,
        bytes memory
    ) public virtual override returns (bytes4) {
        return this.onERC1155BatchReceived.selector;
    }
}
