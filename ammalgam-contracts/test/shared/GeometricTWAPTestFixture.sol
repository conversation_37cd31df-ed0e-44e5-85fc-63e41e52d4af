// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {LENDING_TICK_NOT_AVAILABLE} from 'contracts/libraries/constants.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {Q32} from 'contracts/libraries/constants.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';

import {getTickRange} from 'test/shared/utilities.sol';

int16 constant DEFAULT_TICK_VALUE = 22;
uint16 constant DEFAULT_LONG_TERM_INTERVAL = 10; // 10 blocks or 10 * 12s = 120s, longTermInterval is 112s

contract GeometricTWAPTestFixture is Test {
    using GeometricTWAP for GeometricTWAP.Observations;

    GeometricTWAP.Observations public obs;
    uint32 public timestamp;

    constructor(uint24 midTermIntervalConfig, uint24 longTermIntervalConfigFactor, int16 firstTick) {
        // set the last element of long-term buffer to the lowest value of type int56
        // to be used to check if the buffer is initialized.
        obs.lastLendingStateTick = LENDING_TICK_NOT_AVAILABLE;
        obs.initializeObservationStruct(midTermIntervalConfig, longTermIntervalConfigFactor);
        obs.addObservationAndSetLendingState(firstTick);
        timestamp = GeometricTWAP.getCurrentTimestamp();
    }

    function generateObservations(uint256 blocks, int16 tick) public {
        for (uint256 i = 0; i < blocks; i++) {
            mineBlock(1);
            uint32 currentTimestamp = GeometricTWAP.getCurrentTimestamp();
            if (obs.recordObservation(tick, currentTimestamp - timestamp)) timestamp = currentTimestamp;
        }
    }

    function getLendingStateTickAndCheckpoint(
        uint32 timeElapsedSinceUpdate,
        uint32 timeElapsedSinceLendingUpdate
    ) public returns (int16 tick) {
        return obs.getLendingStateTickAndCheckpoint(timeElapsedSinceUpdate, timeElapsedSinceLendingUpdate);
    }

    function changeInterval(
        uint24 longTermIntervalConfig
    ) public {
        obs.configLongTermInterval(longTermIntervalConfig);
    }

    function getTicks(
        int16 currentTick
    ) public view returns (int16 minTick, int16 maxTick, int16 lastTick) {
        (minTick, maxTick) = obs.getTickRange(currentTick);
        lastTick = obs.lastTick;
    }

    function getObservedTicks(
        bool isLongTermBufferInitialized
    ) public view returns (int16 longTermTick, int16 midTermTick, int16 lastTick) {
        (longTermTick, midTermTick, lastTick) = obs.getObservedTicks(isLongTermBufferInitialized);
    }

    function getObservationStruct() public view returns (GeometricTWAP.Observations memory) {
        return obs;
    }

    function generatePastBlocks(
        uint256 blocks
    ) public returns (uint256) {
        mineBlock(blocks);
        return block.number;
    }

    function verifyIndexes(uint256 duration, uint24 interval) public view {
        (uint24 _midTermIndex, uint24 _longTermIndex) = calculateIndex(duration, interval);
        assertEq(obs.midTermIndex, _midTermIndex, 'midTermIndex');
        assertEq(obs.longTermIndex, _longTermIndex, 'longTermIndex');
    }

    function verifyTicks(
        int16 tick
    ) public view {
        verifyTicks(tick, tick, tick, tick);
    }

    function verifyTicks(int16 longTermTick, int16 midTermTick, int16 _blockTick, int16 _currentTick) public view {
        (int16 _minTick, int16 _maxTick) = getTickRange(longTermTick, midTermTick, _blockTick, _currentTick);

        int16 delta = _maxTick - _minTick;

        // `factor` = 8 => `longTerm` array is `100%` filled.
        _minTick = intMax(TickMath.MIN_TICK, _blockTick - delta);
        _maxTick = intMin(TickMath.MAX_TICK, _blockTick + delta + 1);

        // get tick from the contract
        (int16 minTick, int16 maxTick, int16 blockTick) = getTicks(_currentTick);

        assertEq(minTick, _minTick, 'minTick = _minTick');
        assertEq(maxTick, _maxTick, 'maxTick = _maxTick');
        assertEq(blockTick, _blockTick, 'blockTick = _blockTick');
    }

    function calculateIndex(
        uint256 duration,
        uint24 interval
    ) private pure returns (uint24 midTermIndex, uint24 longTermIndex) {
        /**
         * Calculate indices based on elapsed time (`duration`) and interval.
         * `duration / 12` converts time in seconds to an approximate block count (12s per block).
         * The modulo operation ensures the indices wrap around the array lengths.
         */
        midTermIndex = uint24((duration / 12 + 1) % GeometricTWAP.MID_TERM_ARRAY_LENGTH); // 12s per block
        longTermIndex = uint24((duration / interval + 1) % GeometricTWAP.LONG_TERM_ARRAY_LENGTH);
    }

    function mineBlock(
        uint256 blockStep
    ) public {
        vm.roll(block.number + blockStep);
        vm.warp(block.timestamp + blockStep * 12);
    }

    function intMin(int16 a, int16 b) public pure returns (int16) {
        return a < b ? a : b;
    }

    function intMax(int16 a, int16 b) public pure returns (int16) {
        return a > b ? a : b;
    }
}
