// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {IERC20} from '@openzeppelin/contracts/token/ERC20/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {SafeCast} from '@openzeppelin/contracts/utils/math/SafeCast.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {AmmalgamFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {Interest} from 'contracts/libraries/Interest.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';
import {Q128, Q144} from 'contracts/libraries/constants.sol';

import {StubERC20} from 'test/shared/StubErc20.sol';

uint112 constant MINIMUM_LIQUIDITY = 10 ** 3;

function getCreate2Address(
    address factoryAddress,
    address tokenA,
    address tokenB,
    bytes memory creationCode
) pure returns (address) {
    (address tokenX, address tokenY) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);

    address pair = address(
        uint160(
            uint256(
                keccak256(
                    abi.encodePacked(
                        hex'ff', factoryAddress, keccak256(abi.encodePacked(tokenX, tokenY)), keccak256(creationCode)
                    )
                )
            )
        )
    );

    return pair;
}

function mapSeedToRange(uint256 seed, uint256 minInclusive, uint256 maxInclusive) pure returns (uint256) {
    require(minInclusive <= maxInclusive, 'minInclusive must not exceed maxInclusive');
    uint256 rangeWidth = maxInclusive - minInclusive + 1;
    return (seed % rangeWidth) + minInclusive;
}

function getStubToken(string memory name, string memory symbol, uint256 mintAmount_) returns (IERC20) {
    return new StubERC20(name, symbol, mintAmount_);
}

/**
 * @dev Adjusts the amounts of X and Y based on the tick price.
 *
 * This function calculates the adjusted amounts of tokens X and Y based on the given amounts and the current tick price.
 * It first calculates the square root of the price ratio, sqrtPriceX96, using the reserves of tokens X and Y.
 * Then, it determines the tick value based on the sqrtPriceX96.
 * Next, it calculates the new square root of the price ratio, sqrtPriceX96ByTick, based on the obtained tick value.
 * Finally, it adjusts the given amounts, amountX and amountY, by scaling them according to the new sqrtPriceX96.
 *
 * @param amountX The original amount of X.
 * @param amountY The original amount of Y.
 * @param reservesX The reserves of X in the pool.
 * @param reservesY The reserves of Y in the pool.
 * @return
 *  The adjusted amount of amountX.
 *  The adjusted amount of amountY.
 */
function adjustAmountsByTickPrice(
    uint256 amountX,
    uint256 amountY,
    uint256 reservesX,
    uint256 reservesY
) pure returns (uint256, uint256) {
    uint256 priceInXInQ144 = reservesX * Q144 / reservesY;
    uint256 priceQ128 = priceInXInQ144 >> 16;
    uint256 sqrtPriceInXInQ72 = Math.sqrt(priceInXInQ144);

    int16 tick = TickMath.getTickAtPrice(priceQ128);

    uint256 sqrtPriceInXByTickInQ72 = TickMath.getSqrtPriceAtTick(tick);

    return
        (amountX * sqrtPriceInXByTickInQ72 / sqrtPriceInXInQ72, amountY * sqrtPriceInXInQ72 / sqrtPriceInXByTickInQ72);
}

function adjustAmountsByTickPriceForBorrow(
    uint256 amountX,
    uint256 amountY,
    uint256 reservesX,
    uint256 reservesY
) pure returns (uint256, uint256) {
    uint256 priceInXInQ144 = reservesX * Q144 / reservesY;
    uint256 priceInXInQ128 = priceInXInQ144 >> 16;
    uint256 sqrtPriceInXInQ72 = Math.sqrt(priceInXInQ144);

    int16 tick = TickMath.getTickAtPrice(priceInXInQ128);

    uint256 sqrtPriceInXByTickInQ72ForX = TickMath.getSqrtPriceAtTick(tick);
    uint256 sqrtPriceInXByTickInQ72ForY = TickMath.getSqrtPriceAtTick(tick + 1);
    return (
        amountX * sqrtPriceInXByTickInQ72ForX / sqrtPriceInXInQ72,
        amountY * sqrtPriceInXInQ72 / sqrtPriceInXByTickInQ72ForY
    );
}

function adjustAmountsByTickPriceForDeposit(
    uint256 amountX,
    uint256 amountY,
    uint256 reservesX,
    uint256 reservesY
) pure returns (uint256, uint256) {
    uint256 priceInXInQ144 = reservesX * Q144 / reservesY;
    uint256 priceInXInQ128 = priceInXInQ144 >> 16;
    uint256 sqrtPriceInXInQ72 = Math.sqrt(priceInXInQ144);

    int16 tick = TickMath.getTickAtPrice(priceInXInQ128);
    uint256 sqrtPriceInXByTickInQ72ForX = TickMath.getSqrtPriceAtTick(tick + 1);
    uint256 sqrtPriceInXByTickInQ72ForY = TickMath.getSqrtPriceAtTick(tick);
    return (
        amountX * sqrtPriceInXByTickInQ72ForX / sqrtPriceInXInQ72,
        amountY * sqrtPriceInXInQ72 / sqrtPriceInXByTickInQ72ForY
    );
}

function adjustAmountsByTickPriceForBorrowL(
    uint256 amountX,
    uint256 amountY,
    uint256 reservesX,
    uint256 reservesY
) pure returns (uint256, uint256) {
    uint256 priceInXInQ144 = reservesX * Q144 / reservesY;
    uint256 priceInXInQ128 = priceInXInQ144 >> 16;
    uint256 sqrtPriceInXInQ72 = Math.sqrt(priceInXInQ144);

    int16 tick = TickMath.getTickAtPrice(priceInXInQ128);
    uint256 sqrtPriceInXByTickInQ72ForX = TickMath.getSqrtPriceAtTick(tick + 1);
    uint256 sqrtPriceInXByTickInQ72ForY = TickMath.getSqrtPriceAtTick(tick + 1);
    return (
        amountX * sqrtPriceInXByTickInQ72ForX / sqrtPriceInXInQ72,
        amountY * sqrtPriceInXInQ72 / sqrtPriceInXByTickInQ72ForY
    );
}

function adjustAmountsByTickPrice(
    uint256 amountX,
    uint256 amountY,
    uint256 reservesX,
    uint256 reservesY,
    int16 tick
) pure returns (uint256, uint256) {
    uint256 priceInXInQ144 = reservesX * Q144 / reservesY;
    uint256 sqrtPriceInXInQ72 = Math.sqrt(priceInXInQ144);

    uint256 sqrtPriceInXByTickInQ72 = TickMath.getSqrtPriceAtTick(tick);

    return
        (amountX * sqrtPriceInXByTickInQ72 / sqrtPriceInXInQ72, amountY * sqrtPriceInXInQ72 / sqrtPriceInXByTickInQ72);
}

function adjustAmountsByTickPrice(
    uint256 amountX,
    uint256 amountY,
    uint256 reservesX,
    uint256 reservesY,
    int16 minTick,
    int16 maxTick
) pure returns (uint256, uint256) {
    uint256 priceInXInQ144 = reservesX * Q144 / reservesY;
    uint256 sqrtPriceInXInQ72 = Math.sqrt(priceInXInQ144);

    uint256 sqrtPriceInXByMinTickInQ72 = TickMath.getSqrtPriceAtTick(minTick);
    uint256 sqrtPriceInXByMaxTickInQ72 = TickMath.getSqrtPriceAtTick(maxTick);

    return (
        amountX * sqrtPriceInXByMinTickInQ72 / sqrtPriceInXInQ72,
        amountY * sqrtPriceInXInQ72 / sqrtPriceInXByMaxTickInQ72
    );
}

function computeExpectedLiquidity(
    uint256 amountX,
    uint256 amountY,
    uint256 reserveX,
    uint256 reserveY
) pure returns (uint256 liquidity) {
    uint256 totalSupply = Math.sqrt(reserveX * reserveY);

    if (totalSupply == 0) {
        liquidity = Math.sqrt(amountX * amountY) - MINIMUM_LIQUIDITY;
    } else {
        liquidity = Math.min((amountX * totalSupply) / reserveX, (amountY * totalSupply) / reserveY);
    }
}

function toHexCapitalString(
    bytes4 value
) pure returns (string memory) {
    // Convert bytes4 to hex representation
    bytes memory alphabet = '0123456789ABCDEF';
    bytes memory str = new bytes(10); // 0x + 8 hex chars for bytes4
    str[0] = '0';
    str[1] = 'x';
    for (uint256 i = 0; i < 4; i++) {
        str[2 + i * 2] = alphabet[uint256(uint8(value[i] >> 4))];
        str[3 + i * 2] = alphabet[uint256(uint8(value[i] & 0x0f))];
    }
    return string(str);
}

/*
 *  SWAP HELPER METHODS
 */
function computeExpectedSwapOutAmount(
    uint256 amountIn,
    uint256 tokenInReserveAmount,
    uint256 tokenOutReserveAmount
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapOutAmount(amountIn, tokenInReserveAmount, tokenOutReserveAmount, 0, 0);
}

function computeExpectedSwapOutAmount(
    uint256 amountIn,
    uint256 reserveIn,
    uint256 reserveOut,
    uint256 missingOut
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapOutAmount(amountIn, reserveIn, reserveOut, 0, missingOut);
}

function computeExpectedSwapOutAmount(
    uint256 amountIn,
    uint256 reserveIn,
    uint256 reserveOut,
    uint256 missingIn,
    uint256 missingOut
) pure returns (uint256 expectedSwapOut) {
    return DepletedAssetUtils.computeExpectedSwapOutAmount(amountIn, reserveIn, reserveOut, missingIn, missingOut);
}

function computeExpectedSwapOutAmount(
    uint256 amountIn,
    uint256 reserveIn,
    uint256 referenceReserveIn,
    uint256 reserveOut,
    uint256 missingIn,
    uint256 missingOut
) pure returns (uint256 expectedSwapOut) {
    return DepletedAssetUtils.computeExpectedSwapOutAmount(
        amountIn, reserveIn, referenceReserveIn, reserveOut, missingIn, missingOut
    );
}

function computeExpectedSwapOutAmountWithoutFees(
    uint256 amountIn,
    uint256 reserveIn,
    uint256 reserveOut
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapOutAmountWithoutFees(amountIn, reserveIn, reserveOut, 0, 0);
}

function computeExpectedSwapOutAmountWithoutFees(
    uint256 swapAmountIn,
    uint256 reserveIn,
    uint256 reserveOut,
    uint256 missingIn,
    uint256 missingOut
) pure returns (uint256 swapAmountOut) {
    return DepletedAssetUtils.computeExpectedSwapOutAmountWithoutFees(
        swapAmountIn, reserveIn, reserveOut, missingIn, missingOut
    );
}

function computeExpectedSwapInAmount(uint256 amountOut, uint256 reserveIn, uint256 reserveOut) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapInAmount(amountOut, reserveIn, reserveOut, 0, 0);
}

function computeExpectedSwapInAmount(
    uint256 amountOut,
    uint256 reserveIn,
    uint256 reserveOut,
    uint256 missingOut
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapInAmount(amountOut, reserveIn, reserveOut, 0, missingOut);
}

function computeExpectedSwapInAmount(
    uint256 amountOut,
    uint256 reserveIn,
    uint256 reserveOut,
    uint256 missingIn,
    uint256 missingOut
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapInAmount(amountOut, reserveIn, reserveOut, missingIn, missingOut);
}

function computeExpectedSwapInAmount(
    uint256 amountOut,
    uint256 reserveIn,
    uint256 referenceReserveIn,
    uint256 reserveOut,
    uint256 missingIn,
    uint256 missingOut
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapInAmount(
        amountOut, reserveIn, referenceReserveIn, reserveOut, missingIn, missingOut
    );
}

function computeExpectedSwapInAmountWithoutFees(
    uint256 amountOut,
    uint256 reserveIn,
    uint256 reserveOut
) pure returns (uint256) {
    return DepletedAssetUtils.computeExpectedSwapInAmountWithoutFees(amountOut, reserveIn, reserveOut, 0, 0);
}

function computeExpectedSwapInAmountWithoutFees(
    uint256 swapAmountOut,
    uint256 reserveIn,
    uint256 reserveOut,
    uint256 missingIn,
    uint256 missingOut
) pure returns (uint256 swapAmountIn) {
    return DepletedAssetUtils.computeExpectedSwapInAmountWithoutFees(
        swapAmountOut, reserveIn, reserveOut, missingIn, missingOut
    );
}

/**
 * GeometricTWAP utility method
 */
function getTickRange(
    int256 longTermTick,
    int256 midTermTick,
    int256 blockTick,
    int256 currentTick
) pure returns (int16 minTick, int16 maxTick) {
    minTick = SafeCast.toInt16(
        longTermTick < blockTick
            ? (
                midTermTick < longTermTick
                    ? (midTermTick < currentTick ? midTermTick : currentTick)
                    : (longTermTick < currentTick ? longTermTick : currentTick)
            )
            : (midTermTick < blockTick)
                ? (midTermTick < currentTick ? midTermTick : currentTick)
                : (blockTick < currentTick ? blockTick : currentTick)
    );

    maxTick = SafeCast.toInt16(
        longTermTick > blockTick
            ? (
                midTermTick > longTermTick
                    ? (midTermTick > currentTick ? midTermTick : currentTick)
                    : (longTermTick > currentTick ? longTermTick : currentTick)
            )
            : (midTermTick > blockTick)
                ? (midTermTick > currentTick ? midTermTick : currentTick)
                : (blockTick > currentTick ? blockTick : currentTick)
    );
}

function getCompoundRate(
    uint256 duration,
    uint256 borrowedAssets,
    uint256 depositedAssets
) pure returns (uint256 compoundRate) {
    uint256 utilization = Math.mulDiv(borrowedAssets, WAD, depositedAssets, Math.Rounding.Ceil);
    uint256 expectedRatePerSecondInWads = Interest.getAnnualInterestRatePerSecondInWads(utilization);
    compoundRate = MathLib.wTaylorCompounded(expectedRatePerSecondInWads, duration);
}

function getExpectedInterest(
    uint256 duration,
    uint256 borrowedAssets,
    uint256 depositedAssets
) pure returns (uint256 interest) {
    interest = getExpectedInterestInWad(duration, borrowedAssets, depositedAssets) / WAD;
}

function getExpectedInterestInWad(
    uint256 duration,
    uint256 borrowedAssets,
    uint256 depositedAssets
) pure returns (uint256 interest) {
    uint256 compoundRate = getCompoundRate(duration, borrowedAssets, depositedAssets);
    interest = borrowedAssets * compoundRate;
}

function getTickAtPrice(uint256 reserveX, uint256 reserveY) pure returns (int16) {
    uint256 _priceXInQ128 = Math.mulDiv(reserveX, Q128, reserveY);
    return TickMath.getTickAtPrice(_priceXInQ128);
}
