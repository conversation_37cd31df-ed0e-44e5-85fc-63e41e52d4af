// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {TickMath} from 'contracts/libraries/TickMath.sol';

/**
 * @title TickMathSpecTests
 * @dev The tests verify the correctness of the TickMath library by comparing the results vs julia arbitrary precision values
 */
contract TickMathSpecTests is Test {
    // exact

    function testGetSqrtPriceAtTickForPowerOfTwo() public pure {
        // string(floor(BigInt, B^tick * big(2)^72), base=16)

        // negative near edge
        assertEq(
            TickMath.getSqrtPriceAtTick(TickMath.MIN_TICK + 1),
            0x10040,
            'test tick power TickMath.MIN_TICK + 1 = 0x-4d8e'
        );

        // negative powers of two
        assertEq(TickMath.getSqrtPriceAtTick(-16_384), 0x3746fe3, 'test tick power -16384 = 0x-4000');
        assertEq(TickMath.getSqrtPriceAtTick(-8192), 0x1dbd4effd593a, 'test tick power -8192 = 0x-2000');
        assertEq(TickMath.getSqrtPriceAtTick(-4096), 0x15d0460cb40a7356, 'test tick power -4096 = 0x-1000');
        assertEq(TickMath.getSqrtPriceAtTick(-2048), 0x4aba5e0da8e29a77f, 'test tick power -2048 = 0x-800');
        assertEq(TickMath.getSqrtPriceAtTick(-1024), 0x2294012b4d1cbe1865, 'test tick power -1024 = 0x-400');
        assertEq(TickMath.getSqrtPriceAtTick(-512), 0x5e15c89991553a6dc1, 'test tick power -512 = 0x-200');
        assertEq(TickMath.getSqrtPriceAtTick(-256), 0x9b3229ed2432991a2e, 'test tick power -256 = 0x-100');
        assertEq(TickMath.getSqrtPriceAtTick(-128), 0xc7530338a302e81d82, 'test tick power -128 = 0x-80');
        assertEq(TickMath.getSqrtPriceAtTick(-64), 0xe1e43f8ddd09226227, 'test tick power -64 = 0x-40');
        assertEq(TickMath.getSqrtPriceAtTick(-32), 0xf0799caf21e927ea12, 'test tick power -32 = 0x-20');
        assertEq(TickMath.getSqrtPriceAtTick(-16), 0xf81dba7137fcc6d22f, 'test tick power -16 = 0x-10');
        assertEq(TickMath.getSqrtPriceAtTick(-8), 0xfc06f9045e406ff001, 'test tick power -8 = 0x-8');
        assertEq(TickMath.getSqrtPriceAtTick(-4), 0xfe017f801000000000, 'test tick power -4 = 0x-4');
        assertEq(TickMath.getSqrtPriceAtTick(-2), 0xff0040000000000000, 'test tick power -2 = 0x-2');
        assertEq(TickMath.getSqrtPriceAtTick(1), 0x1008040201008040201, 'test tick power 1 = 0x1');
        assertEq(TickMath.getSqrtPriceAtTick(2), 0x10100c08050301c1009, 'test tick power 2 = 0x2');
        assertEq(TickMath.getSqrtPriceAtTick(4), 0x10202828231c150f0a5, 'test tick power 4 = 0x4');
        assertEq(TickMath.getSqrtPriceAtTick(8), 0x104090f14b8daeae939, 'test tick power 8 = 0x8');
        assertEq(TickMath.getSqrtPriceAtTick(16), 0x1082266f427d51be550, 'test tick power 16 = 0x10');
        assertEq(TickMath.getSqrtPriceAtTick(32), 0x11086f8f7146996be25, 'test tick power 32 = 0x20');
        assertEq(TickMath.getSqrtPriceAtTick(64), 0x1221f1836a02964e2d7, 'test tick power 64 = 0x40');
        assertEq(TickMath.getSqrtPriceAtTick(128), 0x148ca76a2a262dc4817, 'test tick power 128 = 0x80');
        assertEq(TickMath.getSqrtPriceAtTick(256), 0x1a64770202fbfed816c, 'test tick power 256 = 0x100');
        // from tick = 2^9, we check relative error
        assertApproxEqRel(TickMath.getSqrtPriceAtTick(512), 0x2b88f999980636d5059, 1, 'test tick power 512 = 0x200');
        assertApproxEqRel(TickMath.getSqrtPriceAtTick(1024), 0x7674d23bf9fc2e8fa93, 1, 'test tick power 1024 = 0x400');
        assertApproxEqRel(TickMath.getSqrtPriceAtTick(2048), 0x36cfe71e7d757ba58cf0, 1, 'test tick power 2048 = 0x800');
        assertApproxEqRel(
            TickMath.getSqrtPriceAtTick(4096), 0xbbc5e5870e47e33775903, 1, 'test tick power 4096 = 0x1000'
        );
        assertApproxEqRel(
            TickMath.getSqrtPriceAtTick(8192), 0x89bab64e5e83e763663a7676, 1e4, 'test tick power 8192 = 0x2000'
        );
        assertApproxEqRel(
            TickMath.getSqrtPriceAtTick(16_384),
            0x4a195f4d4cde0bb5e82297905dabc4,
            1e11,
            'test tick power 16384 = 0x4000'
        );
        assertApproxEqRel(
            TickMath.getSqrtPriceAtTick(TickMath.MAX_TICK),
            0xffbfc6509a7540f2dad155e56bd219a6,
            1e13,
            'test tick power TickMath.MAX_TICK = 0x4d8e'
        );

        // 'random' values

        assertEq(TickMath.getSqrtPriceAtTick(23), 0x10bc6233051fe8b81c3, 'test tick power 23 = 0x17');
        assertEq(TickMath.getSqrtPriceAtTick(-71), 0xded23ec3612564228e, 'test tick power -71 = 0x-47');
    }

    function testGetTickAtPriceForPowerOfTwo() public pure {
        // string(round(BigInt, B^(2*tick) * big(2)^128), base=16)

        // negative near edge

        assertEq(TickMath.getTickAtPrice(0x10081), TickMath.MIN_TICK + 1, 'test price log TickMath.MIN_TICK + 1');

        // negative powers of two

        assertEq(TickMath.getTickAtPrice(0xbef94ed7e), -16_384, 'test price log -16384');
        assertEq(TickMath.getTickAtPrice(0x3746fe3b485b7be710a06), -8192, 'test price log -8192');
        assertEq(TickMath.getTickAtPrice(0x1dbd4effd593afec2694414e4f6), -4096, 'test price log -4096');
        assertEq(TickMath.getTickAtPrice(0x15d0460cb40a7356d32b6966397c03), -2048, 'test price log -2048');
        assertEq(TickMath.getTickAtPrice(0x4aba5e0da8e29a77fabca56a012ae25), -1024, 'test price log -1024');
        assertEq(TickMath.getTickAtPrice(0x2294012b4d1cbe1865fe254cef6e40bc), -512, 'test price log -512');
        assertEq(TickMath.getTickAtPrice(0x5e15c89991553a6dc1c8a8a0931572d2), -256, 'test price log -256');
        assertEq(TickMath.getTickAtPrice(0x9b3229ed2432991a2e021bb106f5feb6), -128, 'test price log -128');
        assertEq(TickMath.getTickAtPrice(0xc7530338a302e81d8229a7f1f67fa265), -64, 'test price log -64');
        assertEq(TickMath.getTickAtPrice(0xe1e43f8ddd0922622788b108788fc191), -32, 'test price log -32');
        assertEq(TickMath.getTickAtPrice(0xf0799caf21e927ea1252fa7400a1d886), -16, 'test price log -16');
        assertEq(TickMath.getTickAtPrice(0xf81dba7137fcc6d22fafcfde71ae81e0), -8, 'test price log -8');
        assertEq(TickMath.getTickAtPrice(0xfc06f9045e406ff00100000000000000), -4, 'test price log -4');
        assertEq(TickMath.getTickAtPrice(0xfe017f80100000000000000000000000), -2, 'test price log -2');

        // positive powers of two

        assertEq(TickMath.getTickAtPrice(0x10100c08050301c10090502c180d0703c), 1, 'test price log 1');
        assertEq(TickMath.getTickAtPrice(0x10202828231c150f0a56e47ad9c818aa6), 2, 'test price log 2');
        assertEq(TickMath.getTickAtPrice(0x104090f14b8daeae9396b0d9656bd9e18), 4, 'test price log 4');
        assertEq(TickMath.getTickAtPrice(0x1082266f427d51be5503ef3300863df6a), 8, 'test price log 8');
        assertEq(TickMath.getTickAtPrice(0x11086f8f7146996be2515af4618eef4c2), 16, 'test price log 16');
        assertEq(TickMath.getTickAtPrice(0x1221f1836a02964e2d74edd08bc571bb4), 32, 'test price log 32');
        assertEq(TickMath.getTickAtPrice(0x148ca76a2a262dc481753aebff71767a1), 64, 'test price log 64');
        assertEq(TickMath.getTickAtPrice(0x1a64770202fbfed816cbdc592fd63cedb), 128, 'test price log 128');
        assertEq(TickMath.getTickAtPrice(0x2b88f999980636d505900a13f38a6744e), 256, 'test price log 256');
        assertEq(TickMath.getTickAtPrice(0x7674d23bf9fc2e8fa939d23d71ffbae64), 512, 'test price log 512');
        assertEq(TickMath.getTickAtPrice(0x36cfe71e7d757ba58cf05d1134da5da87a), 1024, 'test price log 1024');
        assertEq(TickMath.getTickAtPrice(0xbbc5e5870e47e33775903ca81188526d637), 2048, 'test price log 2048');
        assertApproxEqAbs(
            TickMath.getTickAtPrice(0x89bab64e5e83e763663a7676d2373000998b7e), 4096, 1, 'test price log 4096'
        );
        assertApproxEqAbs(
            TickMath.getTickAtPrice(0x4a195f4d4cde0bb5e82297905dabc4cb32e8e512b097), 8192, 1, 'test price log 8192'
        );
        assertEq(
            TickMath.getTickAtPrice(0x1572ad9c7103d8879513fba4b38234e160f1ac467a2bbc2bdd9fbd19),
            16_384,
            'test price log 16384'
        );

        // positive near edge

        assertApproxEqAbs(
            TickMath.getTickAtPrice(0xff7f9cbe199cdaa90141179a6a92374465d084e6fb20e5bfc6e116112865),
            TickMath.MAX_TICK - 1,
            1,
            'test price log TickMath.MAX_TICK - 1'
        );

        // 'random' values

        assertEq(TickMath.getTickAtPrice(0x11816e6c13e9e0c4aeecaa3d4c7aa7bbd), 23, 'test price log 23');

        assertEq(TickMath.getTickAtPrice(0xc1f15185e46b732f778752e8f18126ea), -71, 'test price log -71');
    }

    // egdes

    function testSqrtPriceAtMinTick() public pure {
        assertEq(TickMath.getSqrtPriceAtTick(TickMath.MIN_TICK), TickMath.MIN_SQRT_PRICE_IN_Q72, 'min tick');
    }

    function testSqrtPriceAtMaxTick() public pure {
        assertEq(TickMath.getSqrtPriceAtTick(TickMath.MAX_TICK), TickMath.MAX_SQRT_PRICE_IN_Q72, 'max tick');
    }

    function testTickOnMinPrice() public pure {
        assertEq(TickMath.getTickAtPrice(TickMath.MIN_PRICE_IN_Q128), TickMath.MIN_TICK, 'price of min tick');
    }

    function testTickOnMaxPrice() public pure {
        assertEq(TickMath.getTickAtPrice(TickMath.MAX_PRICE_IN_Q128), TickMath.MAX_TICK, 'price of max tick');
    }

    // reverts

    function testRevertOnLessThanMinTick() public {
        // 'throws for too low'
        vm.expectRevert(TickMath.TickOutOfBounds.selector);
        TickMath.getSqrtPriceAtTick(TickMath.MIN_TICK - 1);
    }

    function testRevertOnMoreThanMaxTick() public {
        // 'throws for too high'
        vm.expectRevert(TickMath.TickOutOfBounds.selector);
        TickMath.getSqrtPriceAtTick(TickMath.MAX_TICK + 1);
    }

    function testRevertOnLessThanMinPrice() public {
        // 'throws for too low'
        vm.expectRevert(TickMath.PriceOutOfBounds.selector);
        TickMath.getTickAtPrice(TickMath.MIN_PRICE_IN_Q128 - 1);
    }

    function testRevertOnMoreThanMaxPrice() public {
        // 'throws for too high'
        vm.expectRevert(TickMath.PriceOutOfBounds.selector);
        TickMath.getTickAtPrice(TickMath.MAX_PRICE_IN_Q128 + 1);
    }
}
