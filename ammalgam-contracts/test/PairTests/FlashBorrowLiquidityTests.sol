// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {SafeERC20} from '@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {RevertReasonParser} from 'lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol';

import {DEPOSIT_L, DEPOSIT_X, DEPOSIT_Y, BORROW_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IPairHarness, FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    mapSeedToRange,
    adjustAmountsByTickPrice,
    computeExpectedSwapOutAmount,
    toHexCapitalString
} from 'test/shared/utilities.sol';
import {Validation} from 'contracts/libraries/Validation.sol';

contract FlashBorrowLiquidityTests is Test {
    using SafeERC20 for IERC20;
    using SafeERC20 for IAmmalgamERC20;

    FactoryPairTestFixture private fixture;
    address private tester;
    address private pairAddress;
    IAmmalgamPair private pair;
    uint256 private initialX = 2e18;
    uint256 private initialY = 8e18;

    function setUp() public {
        address random = vm.addr(99);
        tester = vm.addr(100);
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
        pairAddress = fixture.pairAddress();
        pair = fixture.pair();
    }

    function testFlashBorrowLiquidityWithDepositX() public {
        uint256 borrowLX = 0.1e18;
        uint256 borrowLY = 0.4e18;
        uint256 borrowL = 0.2e18; // sqrt(0.1 * 0.4)
        uint256 transferX = 0.2e18;
        uint256 depositX = 0.3e18; // transferX + borrowLX
        uint256 depositY = 0;

        fixture.transferTokensTo(tester, transferX, 0);

        ICallback callee = getReentryDelegatedCall(this.depositCallback);

        bytes memory data = abi.encode(depositX, depositY);

        vm.startPrank(tester);

        fixture.tokenX().approve(address(callee), depositX);
        fixture.pair().borrowLiquidity(address(callee), borrowL, data);

        vm.stopPrank();

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialX + depositX - borrowLX;
        expectPair.tokenY = initialY - borrowLY;
        expectPair.reserveXAssets = initialX - borrowLX;
        expectPair.reserveYAssets = initialY - borrowLY;

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairBorrowedL = borrowL;
        expectTester.pairDepositedX = depositX;
        expectTester.balanceY = borrowLY;

        verifyFlashBorrow(expectPair, expectTester);
    }

    function depositCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        _fixture.transferTokensTo(sender, amountX, amountY);

        (uint256 depositX, uint256 depositY) = abi.decode(data, (uint256, uint256));

        if (depositX > 0) {
            _fixture.tokenX().transferFrom(sender, address(_fixture.pair()), depositX);
        }
        if (depositY > 0) {
            _fixture.tokenY().transferFrom(sender, address(_fixture.pair()), depositY);
        }

        _fixture.pair().deposit(sender);
    }

    function verifyFlashBorrow(
        FactoryPairTestFixture.ExpectedPairState memory expectedPairState,
        FactoryPairTestFixture.ExpectedAddressState memory expectedTester
    ) private view {
        fixture.verifyPair(expectedPairState);
        fixture.verifyAddress(expectedTester);
    }

    function testBorrowLiquidityReentryWorksLikeBorrowingTwice() public {
        uint256 amountX = 1e18;
        uint256 amountY = 4e18;
        uint256 borrowedL = 0.2e18;
        uint256 expectedBorrowLX = 0.1e18 * 2;
        uint256 expectedBorrowLY = 0.4e18 * 2;

        fixture.transferTokensTo(tester, amountX, amountY);
        fixture.depositFor(tester, amountX, amountY);

        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryBorrowAgainCallback);

        vm.startPrank(tester);
        IERC20DebtToken(address(pair.tokens(BORROW_L))).approveDebt(address(callee), borrowedL);
        pair.borrowLiquidity(address(callee), borrowedL, abi.encode(address(0)));
        vm.stopPrank();

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: initialX + amountX - expectedBorrowLX,
                tokenY: initialY + amountY - expectedBorrowLY,
                reserveXAssets: initialX - expectedBorrowLX,
                reserveYAssets: initialY - expectedBorrowLY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory testerState;

        testerState.toCheck = tester;
        testerState.balanceX = expectedBorrowLX;
        testerState.balanceY = expectedBorrowLY;
        testerState.pairDepositedX = amountX;
        testerState.pairDepositedY = amountY;
        testerState.pairBorrowedL = borrowedL * 2;

        fixture.verifyAddress(testerState);
    }

    function reentryBorrowAgainCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountLX,
        uint256 amountLY,
        uint256 amountL,
        bytes memory data
    ) external {
        address dataAddress = abi.decode(data, (address));
        // expect zero address on first call
        if (dataAddress == address(0)) {
            SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountLX);
            SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountLY);
            // encode sender to trigger else condition and to send back to original sender.
            _fixture.pair().borrowLiquidity(address(this), amountL, abi.encode(sender));
        } else {
            // second call logic
            SafeERC20.safeTransfer(_fixture.tokenX(), dataAddress, amountLX);
            SafeERC20.safeTransfer(_fixture.tokenY(), dataAddress, amountLY);
            _fixture.pair().tokens(BORROW_L).safeTransfer(dataAddress, amountL);
        }
    }

    function testBorrowLiquidityReentryDoubleBorrowTriesToRepayCallback() public {
        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryBorrowCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrowLiquidity(address(callee), 0.1e18, abi.encode(address(callee)));
    }

    function testBorrowLiquidityReentryDoubleBorrowFailsTriesToRepaySender() public {
        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryBorrowCallback);

        vm.startPrank(tester);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrowLiquidity(address(callee), 0.1e18, abi.encode(address(tester)));
    }

    function reentryBorrowCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountLX,
        uint256 amountLY,
        uint256 amountL,
        bytes memory data
    ) external {
        address repay = abi.decode(data, (address));
        IAmmalgamPair _pair = _fixture.pair();
        if (
            // assumes tester started with 0 and on the second call sender is this contract.
            _fixture.tokenX().balanceOf(sender) == 0 && _fixture.tokenY().balanceOf(sender) == 0
        ) {
            SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountLX);
            SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountLY);
            _pair.borrowLiquidity(address(this), amountL, abi.encode(repay));
        } else {
            SafeERC20.safeTransfer(_fixture.tokenX(), address(_pair), amountLX);
            SafeERC20.safeTransfer(_fixture.tokenY(), address(_pair), amountLY);
            _pair.repayLiquidity(repay);
        }
    }

    function testBorrowLiquidityReentryWithdrawFails() public {
        uint256 reentryWithdrawDepositedX = 1e18;
        uint256 reentryWithdrawDepositedY = 4e18;
        fixture.transferTokensTo(tester, reentryWithdrawDepositedX, reentryWithdrawDepositedY);
        fixture.depositFor(tester, reentryWithdrawDepositedX, reentryWithdrawDepositedY);

        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryWithdrawCallback);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_X).approve(address(callee), reentryWithdrawDepositedX);
        pair.tokens(DEPOSIT_Y).approve(address(callee), reentryWithdrawDepositedY);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrowLiquidity(address(callee), 0.5e18, abi.encode(reentryWithdrawDepositedX, reentryWithdrawDepositedY));
    }

    function reentryWithdrawCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) public {
        IAmmalgamPair _pair = _fixture.pair();
        (uint256 depositedX, uint256 depositedY) = abi.decode(data, (uint256, uint256));
        if (depositedX > 0) {
            SafeERC20.safeTransferFrom(_pair.tokens(DEPOSIT_X), sender, address(_pair), depositedX);
        }
        if (depositedY > 0) {
            SafeERC20.safeTransferFrom(_pair.tokens(DEPOSIT_Y), sender, address(_pair), depositedY);
        }
        _pair.withdraw(sender);
        // contract has borrow and withdraw amount, but will fail without repay or deposit
        SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX + depositedX);
        SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY + depositedY);
    }

    function testBorrowLiquidityReentryMint() public {
        uint256 collateralMintX = 0.01e18;
        uint256 collateralMintY = 0.04e18;
        uint256 borrowLMint = 1.98e18; // max leveraged borrow is 100x
        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryMintCallback);

        fixture.transferTokensTo(tester, collateralMintX, collateralMintY);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(callee), collateralMintX);
        fixture.tokenY().approve(address(callee), collateralMintY);
        pair.borrowLiquidity(address(callee), borrowLMint, abi.encode(collateralMintX, collateralMintY));
        vm.stopPrank();

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: initialX + collateralMintX,
                tokenY: initialY + collateralMintY,
                reserveXAssets: initialX + collateralMintX,
                reserveYAssets: initialY + collateralMintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory testerState;
        testerState.toCheck = tester;
        testerState.pairLiquidity = Math.sqrt(collateralMintX * collateralMintY) + borrowLMint;
        testerState.pairBorrowedL = borrowLMint;

        fixture.verifyAddress(testerState);
    }

    function testBorrowLiquidityReentryToReceiverMintFails() public {
        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryMintCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        pair.borrowLiquidity(address(callee), 0.75e18, abi.encode(0, 0));
    }

    function reentryMintCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        address _pairAddress = _fixture.pairAddress();
        IAmmalgamPair _pair = _fixture.pair();
        (uint256 mintX, uint256 mintY) = abi.decode(data, (uint256, uint256));
        if (mintX > 0) {
            SafeERC20.safeTransferFrom(_fixture.tokenX(), sender, _pairAddress, mintX);
        }
        if (mintY > 0) {
            SafeERC20.safeTransferFrom(_fixture.tokenY(), sender, _pairAddress, mintY);
        }
        SafeERC20.safeTransfer(_fixture.tokenX(), _pairAddress, amountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), _pairAddress, amountY);
        _pair.mint(sender);
    }

    function testBorrowLiquidityReentryBurnFails() public {
        uint256 mintX = 1e18;
        uint256 mintY = 4e18;
        uint256 burnL = 2e18;
        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryBurnCallback);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_L).approve(address(callee), burnL);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrowLiquidity(address(callee), burnL, '0x1');
    }

    function reentryBurnCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256 amountL,
        bytes memory /* data */
    ) external {
        IAmmalgamPair _pair = _fixture.pair();
        SafeERC20.safeTransferFrom(_pair.tokens(DEPOSIT_L), sender, address(_pair), amountL);
        (uint256 burnAmountX, uint256 burnAmountY) = _pair.burn(sender);
        // contract has borrow and burn amount, but will fail without repay or deposit
        SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX + burnAmountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY + burnAmountY);
    }

    function testBorrowLiquidityReentrySwapXtoYFails() public {
        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentrySwapCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrowLiquidity(address(callee), 1e18, abi.encode(false, 1e18));
    }

    function testBorrowLiquidityReentrySwapYtoXFails() public {
        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentrySwapCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrowLiquidity(address(callee), 1e18, abi.encode(false, 1e18));
    }

    function reentrySwapCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256, /* amountX */
        uint256, /* amountY */
        uint256, /* amountL */
        bytes memory data
    ) external {
        (bool swapXToY, uint256 amount) = abi.decode(data, (bool, uint256));
        (uint256 reserveX, uint256 reserveY,) = _fixture.pair().getReserves();

        if (swapXToY) {
            SafeERC20.safeTransfer(_fixture.tokenX(), address(_fixture.pair()), amount);
            uint256 expectedOutSwap = computeExpectedSwapOutAmount(amount, reserveX, reserveY);
            _fixture.pair().swap(0, expectedOutSwap, sender, '');
        } else {
            SafeERC20.safeTransfer(_fixture.tokenY(), address(_fixture.pair()), amount);
            uint256 expectedOutSwap = computeExpectedSwapOutAmount(amount, reserveY, reserveX);
            _fixture.pair().swap(expectedOutSwap, 0, sender, '');
        }
    }

    function testBorrowLiquidityReentrySwapAndDeposit() public {
        uint256 collateralX = 0.1e18;
        uint256 borrowL = 0.2e18;
        uint256 borrowLX = 0.1e18;
        uint256 borrowLY = 0.4e18;

        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentrySwapYToXAndDeposit);

        fixture.transferTokensTo(tester, collateralX, 0);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(callee), collateralX);

        uint256 expectedXOut = computeExpectedSwapOutAmount(borrowLY, initialY - borrowLY, initialX - borrowLX);
        pair.borrowLiquidity(address(callee), borrowL, abi.encode(collateralX, expectedXOut));
        vm.stopPrank();

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: initialX + collateralX,
                tokenY: initialY,
                reserveXAssets: initialX - borrowLX - expectedXOut,
                reserveYAssets: initialY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory testerState;

        testerState.toCheck = tester;
        testerState.pairDepositedX = collateralX + expectedXOut + borrowLX;
        testerState.pairBorrowedL = borrowL;

        fixture.verifyAddress(testerState);
    }

    function reentrySwapYToXAndDeposit(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        (uint256 collateralX, uint256 expectedOutSwap) = abi.decode(data, (uint256, uint256));
        SafeERC20.safeTransfer(_fixture.tokenY(), address(_fixture.pair()), amountY);
        _fixture.pair().swap(expectedOutSwap, 0, address(this), '');
        SafeERC20.safeTransferFrom(_fixture.tokenX(), sender, address(_fixture.pair()), collateralX);
        SafeERC20.safeTransfer(_fixture.tokenX(), address(_fixture.pair()), expectedOutSwap + amountX);
        _fixture.pair().deposit(sender);
    }

    function testBorrowLiquidityReentryDepositFails() public {
        uint256 borrowL = 1e18;
        uint256 borrowLX = 0.5e18;
        uint256 borrowLY = 0.2e18;

        (uint256 borrowLXAdj, uint256 borrowLYAdj) = adjustAmountsByTickPrice(borrowLX, borrowLY, initialX, initialY);

        uint256 depositXAdj = borrowLX - borrowLXAdj;
        uint256 depositYAdj = borrowLYAdj - borrowLY;

        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentryDepositCallback);

        fixture.transferTokensTo(address(callee), depositXAdj, depositYAdj);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrowLiquidity(address(callee), borrowL, abi.encode(depositXAdj, depositYAdj));
    }

    /**
     * @dev This function is specifically for testBorrowLiquidityReentryDepositFails.
     *
     * the amountX and amountY to deposit need to be adjusted by the depositXAdj and depositYAdj because the borrowedX and borrowedLY from
     * borrowed liquidity for this deposit will be adjusted in the validationSolvency with sqrtPriceX96 at tick after the callback.
     *
     */
    function reentryDepositCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        IERC20 tokenX = _fixture.tokenX();
        IERC20 tokenY = _fixture.tokenY();
        IAmmalgamPair _pair = _fixture.pair();
        address _pairAddress = address(_pair);

        (uint256 depositXAdj, uint256 depositYAdj) = abi.decode(data, (uint256, uint256));

        SafeERC20.safeTransfer(tokenX, _pairAddress, amountX - depositXAdj);
        SafeERC20.safeTransfer(tokenY, _pairAddress, amountY + depositYAdj);

        _pair.deposit(sender);
    }

    /**
     * @dev Tests that reentry when calling borrowLiquidity into the pair where the swapper executes a swap, repays liquidity, and then swaps back.
     * I had some concern that since borrowing liquidity would weaken the liquidity making it easier to swap and thus improving the testers borrow
     * liquidity position. I proved to myself that this is not the case because the benefit of borrowing liquidity and the price moving is equal to
     * the slippage loss on the swap. This test proves this as well.
     */
    function testFuzzBorrowLiquidityReentrySwapDoesNotPayTester(
        uint24 borrowLSeed,
        uint24 swapSeed,
        bool swapXToY
    ) public {
        uint256 maxBorrowL = (Math.sqrt(initialX * initialY) * 90) / 100;
        uint256 startingX = 100e18;
        uint256 startingY = 400e18;
        uint256 borrowL = mapSeedToRange(borrowLSeed, 1, maxBorrowL - 1);
        uint256 swap = mapSeedToRange(swapSeed, swapXToY ? 1 : 5, swapXToY ? startingX : startingY);

        uint256 pairX = fixture.tokenX().balanceOf(address(pair));
        uint256 pairY = fixture.tokenY().balanceOf(address(pair));

        fixture.transferTokensTo(tester, startingX, startingY);

        ReentryDelegatedCall callee = getReentryDelegatedCall(this.reentrySwapRepayCallback);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(callee), startingX);
        fixture.tokenY().approve(address(callee), startingY);
        pair.borrowLiquidity(address(callee), borrowL, abi.encode(swapXToY, swap));
        vm.stopPrank();

        /**
         * In edge cases starting x and y might be more for tester and less for the pair individually, but not when both are considered,
         * so this checks that the invariant of the pair can only get bigger and the tester can only get smaller.
         */
        assertLt(
            fixture.tokenX().balanceOf(address(tester)) * fixture.tokenY().balanceOf(address(tester)),
            startingX * startingY
        );
        assertGt(fixture.tokenX().balanceOf(address(pair)) * fixture.tokenY().balanceOf(address(pair)), pairX * pairY);
    }

    function reentrySwapRepayCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256 amountL,
        bytes memory data
    ) external {
        bool swapXToY;
        IPairHarness _pair = _fixture.pair();
        (uint256 reserveX, uint256 reserveY,) = _pair.getReserves();
        (uint256 referenceReserveX, uint256 referenceReserveY) = _pair.referenceReserves();
        {
            uint256 amount;
            (swapXToY, amount) = abi.decode(data, (bool, uint256));

            if (swapXToY) {
                if (amountX < amount) {
                    SafeERC20.safeTransferFrom(_fixture.tokenX(), sender, address(_pair), amount - amountX);
                }
                SafeERC20.safeTransfer(_fixture.tokenX(), address(_pair), amountX);
                uint256 expectedOutSwap =
                    computeExpectedSwapOutAmount(amount, reserveX, referenceReserveX, reserveY, 0, 0);
                _pair.swap(0, expectedOutSwap, address(this), '');
            } else {
                if (amountY < amount) {
                    SafeERC20.safeTransferFrom(_fixture.tokenY(), sender, address(_pair), amount - amountY);
                }
                SafeERC20.safeTransfer(_fixture.tokenY(), address(_pair), amountY);
                uint256 expectedOutSwap =
                    computeExpectedSwapOutAmount(amount, reserveY, referenceReserveY, reserveX, 0, 0);
                _pair.swap(expectedOutSwap, 0, address(this), '');
            }
        }

        {
            (reserveX, reserveY,) = _pair.getReserves();
            uint256 liquidity = _pair.tokens(DEPOSIT_L).totalSupply() - _pair.tokens(BORROW_L).totalSupply();

            _pair.tokens(DEPOSIT_L).totalSupply() - _pair.tokens(BORROW_L).totalSupply();
            SafeERC20.safeTransferFrom(
                _fixture.tokenX(), sender, address(_pair), Math.ceilDiv(amountL * reserveX, liquidity)
            );
            SafeERC20.safeTransferFrom(
                _fixture.tokenY(), sender, address(_pair), Math.ceilDiv(amountL * reserveY, liquidity)
            );
        }

        _pair.repayLiquidity(sender);
        (reserveX, reserveY,) = _pair.getReserves();
        (referenceReserveX, referenceReserveY) = _pair.referenceReserves();

        uint256 expectedSwapOut;
        if (swapXToY) {
            {
                uint256 swapBackY = _fixture.tokenY().balanceOf(address(this));
                expectedSwapOut = computeExpectedSwapOutAmount(swapBackY, reserveY, referenceReserveY, reserveX, 0, 0);
                SafeERC20.safeTransfer(_fixture.tokenY(), address(_pair), swapBackY);
            }
            _pair.swap(expectedSwapOut, 0, sender, '');
        } else {
            {
                uint256 swapBackX = _fixture.tokenX().balanceOf(address(this));
                expectedSwapOut = computeExpectedSwapOutAmount(swapBackX, reserveX, referenceReserveX, reserveY, 0, 0);
                SafeERC20.safeTransfer(_fixture.tokenX(), address(_pair), swapBackX);
            }
            _pair.swap(0, expectedSwapOut, sender, '');
        }
    }

    function getReentryDelegatedCall(
        function(FactoryPairTestFixture, address, uint256, uint256, uint256, bytes memory) external delegatedCall
    ) private returns (ReentryDelegatedCall) {
        return new ReentryDelegatedCall(fixture, delegatedCall);
    }
}

contract ReentryDelegatedCall is ICallback {
    FactoryPairTestFixture fixture;
    function(FactoryPairTestFixture, address, uint256, uint256, uint256, bytes memory) external
        private delegatedBorrowCall;

    constructor(
        FactoryPairTestFixture _fixture,
        function(FactoryPairTestFixture, address, uint256, uint256, uint256, bytes memory) external delegatedBorrowCall_
    ) {
        fixture = _fixture;
        delegatedBorrowCall = delegatedBorrowCall_;
    }

    function ammalgamSwapCallV1(address sender, uint256 amountX, uint256 amountY, bytes calldata data) public {
        /* noop */
    }

    function ammalgamBorrowCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountXShares,
        uint256 amountYShares,
        bytes calldata data
    ) external {} // noop

    function ammalgamBorrowLiquidityCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) public {
        address delegatedAddress = delegatedBorrowCall.address;
        (bool success, bytes memory message) = delegatedAddress.delegatecall(
            abi.encodeWithSelector(
                delegatedBorrowCall.selector, fixture, sender, amountXAssets, amountYAssets, amountLShares, data
            )
        );
        require(success, RevertReasonParser.parse(message, 'delegatecall '));
    }

    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external { /* noop */ }
}
