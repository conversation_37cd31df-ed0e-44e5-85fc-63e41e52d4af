// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    IDebtDelegation, PeripheralDelegationContractExample
} from 'test/example/PeripheralDelegationContractExample.sol';
import {DelegationTestFixture} from 'test/shared/DelegationTestFixture.sol';
import {computeExpectedLiquidity, MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';
import {DEPOSIT_L, DEPOSIT_Y, BORROW_X, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';

contract BorrowTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;
    address private tester3;

    uint256 private initialAmounts = 5000e18;

    FactoryPairTestFixture private fixture;
    DelegationTestFixture private delegationFixture;

    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);
        tester3 = vm.addr(1113);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(tester, initialAmounts, initialAmounts).transferTokensTo(
            tester2, initialAmounts, initialAmounts
        ).transferTokensTo(tester3, initialAmounts, initialAmounts);

        delegationFixture = new DelegationTestFixture(pair);
    }

    function testBorrowX_byDepositY() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositYAmount = 4000e18;

        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);
        fixture.depositFor(tester, 0, depositYAmount);

        uint256 borrowXAmount = 400e18;
        uint256 tokenBalanceBeforeBorrow = fixture.tokenX().balanceOf(tester);

        fixture.borrowFor(tester, borrowXAmount, 0);

        assertEq(fixture.tokenX().balanceOf(tester), tokenBalanceBeforeBorrow + borrowXAmount);
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(tester), depositYAmount);
        assertEq(pair.tokens(BORROW_X).balanceOf(tester), borrowXAmount);
    }

    function testBorrowX_byDepositK() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        uint256 borrowXAmount = 100e18;
        uint256 tokenBalanceBeforeBorrow = fixture.tokenX().balanceOf(tester);

        fixture.borrowFor(tester, borrowXAmount, 0);

        assertEq(pair.tokens(BORROW_X).balanceOf(tester), borrowXAmount);
        assertEq(fixture.tokenX().balanceOf(tester), tokenBalanceBeforeBorrow + borrowXAmount);
    }

    function testBorrowX_andSwap() public {
        uint256 swapAmount = 1e18; // sample of testSwapCase1
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        uint256 borrowAmount = 1e18;
        fixture.borrowFor(tester, borrowAmount, 0);

        // Swap should work as before
        fixture.verifySwapXToY(tester, swapAmount, tokenXAmount, tokenYAmount);
    }

    function testBorrowX_andMint() public {
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        uint256 borrowAmountX = 1e18;

        fixture.borrowFor(tester, borrowAmountX, 0);

        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        uint256 totalMintedX = tokenXAmount * 2;
        uint256 totalMintedY = tokenYAmount * 2;

        assertEq(fixture.tokenX().balanceOf(address(pair)), totalMintedX - borrowAmountX);
        assertEq(fixture.tokenY().balanceOf(address(pair)), totalMintedY);

        uint256 calculatedMintAmount = computeExpectedLiquidity(totalMintedX, totalMintedY, 0, 0);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), calculatedMintAmount);
    }

    function testBorrowX_forMint() public {
        // set tester as the liquidate provider
        uint256 firstMintXAmount = 5e18;
        uint256 firstMintYAmount = 10e18;
        fixture.mintForAndInitializeBlocks(tester, firstMintXAmount, firstMintYAmount);

        uint256 depositXForBorrow = 0;

        uint256 depositYForBorrow = 0.6e18;
        fixture.depositFor(tester2, depositXForBorrow, depositYForBorrow);

        uint256 borrowXAmount = 0.2e18;
        uint256 beforeBorrowX = fixture.tokenX().balanceOf(tester2);
        fixture.borrowFor(tester2, borrowXAmount, 0);
        assertEq(fixture.tokenX().balanceOf(tester2), borrowXAmount + beforeBorrowX);

        // mint with borrowed X
        uint256 secondMintXAmount = borrowXAmount;
        uint256 secondMintYAmount = borrowXAmount * 2; // match xyk pool ratio
        fixture.mintFor(tester2, secondMintXAmount, secondMintYAmount);

        uint256 calculatedMintAmount =
            computeExpectedLiquidity(secondMintXAmount, secondMintYAmount, firstMintXAmount, firstMintYAmount);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), calculatedMintAmount);

        assertEq(fixture.tokenX().balanceOf(address(pair)), firstMintXAmount + secondMintXAmount - borrowXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), firstMintYAmount + secondMintYAmount + depositYForBorrow);
    }

    function testBorrowX_andBurn() public {
        // tester as the initial liquidate provider
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        uint256 testerBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 testerBalanceY = fixture.tokenY().balanceOf(tester);

        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        //add liquidity for tester3
        uint256 tester3AmountL = fixture.mintFor(tester3, tokenXAmount, tokenYAmount);

        // borrow by tester2
        uint256 depositXForBorrow = 0;
        uint256 depositYForBorrow = 2e18;
        fixture.depositFor(tester2, depositXForBorrow, depositYForBorrow);
        uint256 borrowXAmount = 1e18;

        fixture.borrowFor(tester2, borrowXAmount, 0);

        // burn full amount for tester

        uint256 expectedLiquidity = 3e18 - MINIMUM_LIQUIDITY;
        vm.startPrank(tester);
        pair.tokens(DEPOSIT_L).transfer(address(pair), expectedLiquidity);

        (uint256 outAmountX, uint256 outAmountY) = pair.burn(tester);
        vm.stopPrank();

        // tester info after borrow and burn:
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 0);
        assertEq(outAmountX, tokenXAmount - 1000);
        assertEq(outAmountY, tokenYAmount - 1000);

        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX - 1000, 'Tester balance of X matches expected');
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY - 1000, 'Tester balance of Y matches expected');

        // tester2 info after borrow and burn:
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), 0);
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(tester2), depositYForBorrow);
        assertEq(pair.tokens(BORROW_X).balanceOf(tester2), borrowXAmount);

        // tester3 info after borrow and burn:
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester3), tester3AmountL);

        // pool info after borrow and burn
        uint256 testerLiquidity = pair.tokens(DEPOSIT_L).balanceOf(tester);
        uint256 tester3Liquidity = pair.tokens(DEPOSIT_L).balanceOf(tester3);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), MINIMUM_LIQUIDITY + testerLiquidity + tester3Liquidity);

        uint256 tester3MintXAmount = 3e18;
        uint256 tester3MintYAmount = 3e18;

        assertEq(fixture.tokenX().balanceOf(address(pair)), 1000 + tester3MintXAmount - borrowXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 1000 + tester3MintYAmount + depositYForBorrow);
    }

    function testBorrowX_withdrawShouldFail() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        //deposit Y
        uint256 depositXForBorrow = 0;
        uint256 depositYForBorrow = 0.2e18;
        fixture.depositFor(tester2, depositXForBorrow, depositYForBorrow);

        //borrow X against Y
        uint256 borrowXAmount = 0.1e18;
        fixture.borrowFor(tester2, borrowXAmount, 0);

        //should expect revert as the borrower is withdrawing the deposit
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);

        fixture.withdrawFor(tester2, 0, depositYForBorrow);
    }

    function testBorrowX_transferLiquidityShouldFail() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);
        fixture.mintFor(tester2, tokenXAmount, tokenYAmount);

        uint256 borrowXAmount = 2e18;
        //borrow X against K
        fixture.borrowFor(tester2, borrowXAmount, 0);

        uint256 expectedLiquidity = 2e18;

        IERC20 liquidityToken = IERC20(pair.tokens(DEPOSIT_L));
        vm.startPrank(tester2);
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        liquidityToken.transfer(address(pair), expectedLiquidity - MINIMUM_LIQUIDITY);
        vm.stopPrank();
    }

    function testBorrowX_andPartialBurn() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        // borrow by tester2
        uint256 depositXForBorrow = 0;
        uint256 depositYForBorrow = 0.2e18;
        fixture.depositFor(tester2, depositXForBorrow, depositYForBorrow);
        uint256 borrowXAmount = 0.1e18;

        fixture.borrowFor(tester2, borrowXAmount, 0);

        // partially burn 2 L for tester, after borrowing 1 X by tester2
        uint256 expectedLiquidity = 2e18;
        vm.startPrank(tester);
        pair.tokens(DEPOSIT_L).transfer(address(pair), expectedLiquidity - MINIMUM_LIQUIDITY);
        pair.burn(tester);
        vm.stopPrank();

        // tester info after borrow and burn:
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 1e18);

        // tester2 info after borrow and burn:
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), 0);
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(tester2), depositYForBorrow);
        assertEq(pair.tokens(BORROW_X).balanceOf(tester2), borrowXAmount);

        // pool info after borrow and burn
        uint256 testerLiquidity = pair.tokens(DEPOSIT_L).balanceOf(tester);
        uint256 tester2Liquidity = pair.tokens(DEPOSIT_L).balanceOf(tester2);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), MINIMUM_LIQUIDITY + testerLiquidity + tester2Liquidity);
        assertEq(fixture.tokenX().balanceOf(address(pair)), 1000 + 1e18 - borrowXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 1000 + 1e18 + depositYForBorrow);
    }

    function testBorrowX_insufficientDepositShouldFail() public {
        // not enough k, or y should failure
        uint256 tokenXAmount = 100e18;
        uint256 tokenYAmount = 100e18;
        uint256 insufficientDepositY = 5e18;

        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);
        fixture.depositFor(tester2, 0, insufficientDepositY);

        uint256 borrowAmount = 10e18;
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        fixture.borrowFor(tester2, borrowAmount, 0);
    }

    function testBorrowXAgainstYWithApproval() public {
        uint256 tokenXAmount = 4000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositYAmount = 100e18;
        uint256 borrowXAmount = 20e18;

        fixture.mintForAndInitializeBlocks(tester3, tokenXAmount, tokenYAmount);
        fixture.depositFor(tester2, 0, depositYAmount);

        delegationFixture.approveDebtTransferAndDelegation(tester2, tester, BORROW_X, borrowXAmount);
        delegationFixture.delegateBorrow(tester2, tester, tester, borrowXAmount, 0);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_X);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = tokenXAmount - borrowXAmount;
        expectPair.tokenY = tokenYAmount + depositYAmount;
        expectPair.reserveXAssets = tokenXAmount;
        expectPair.reserveYAssets = tokenYAmount;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = initialAmounts + borrowXAmount;
        expectTester.balanceY = initialAmounts;
        fixture.verifyAddress(expectTester);

        // check tester2
        FactoryPairTestFixture.ExpectedAddressState memory expectTester2;
        expectTester2.toCheck = tester2;
        expectTester2.balanceX = initialAmounts;
        expectTester2.balanceY = initialAmounts - depositYAmount;
        expectTester2.pairDepositedY = depositYAmount;
        expectTester2.pairBorrowedX = borrowXAmount;
        fixture.verifyAddress(expectTester2);
    }

    function testBorrowXAgainstLWithApproval() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 collateralMintX = 400e18;
        uint256 collateralMintY = 100e18;
        uint256 expectedCollateralL = 200e18;
        uint256 borrowXAmount = 400e18;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);
        // collateral mint
        fixture.mintFor(tester2, collateralMintX, collateralMintY);

        delegationFixture.approveDebtTransferAndDelegation(tester2, tester, BORROW_X, borrowXAmount);
        delegationFixture.delegateBorrow(tester2, tester, tester, borrowXAmount, 0);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_X);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialMintX + collateralMintX - borrowXAmount;
        expectPair.tokenY = initialMintY + collateralMintY;
        expectPair.reserveXAssets = initialMintX + collateralMintX;
        expectPair.reserveYAssets = initialMintY + collateralMintY;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = initialAmounts + borrowXAmount;
        expectTester.balanceY = initialAmounts;
        fixture.verifyAddress(expectTester);

        // check tester2
        FactoryPairTestFixture.ExpectedAddressState memory expectTester2;
        expectTester2.toCheck = tester2;
        expectTester2.balanceX = initialAmounts - collateralMintX;
        expectTester2.balanceY = initialAmounts - collateralMintY;
        expectTester2.pairLiquidity = expectedCollateralL;
        expectTester2.pairBorrowedX = borrowXAmount;
        fixture.verifyAddress(expectTester2);
    }

    function testBorrowYAgainstXWithApproval() public {
        uint256 tokenXAmount = 4000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 400e18;
        uint256 borrowYAmount = 50e18;

        fixture.mintForAndInitializeBlocks(tester3, tokenXAmount, tokenYAmount);
        fixture.depositFor(tester2, depositXAmount, 0);

        delegationFixture.approveDebtTransferAndDelegation(tester2, tester, BORROW_Y, borrowYAmount);
        delegationFixture.delegateBorrow(tester2, tester, tester, 0, borrowYAmount);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_Y);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = tokenXAmount + depositXAmount;
        expectPair.tokenY = tokenYAmount - borrowYAmount;
        expectPair.reserveXAssets = tokenXAmount;
        expectPair.reserveYAssets = tokenYAmount;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = initialAmounts;
        expectTester.balanceY = initialAmounts + borrowYAmount;
        fixture.verifyAddress(expectTester);

        // check tester2
        FactoryPairTestFixture.ExpectedAddressState memory expectTester2;
        expectTester2.toCheck = tester2;
        expectTester2.balanceX = initialAmounts - depositXAmount;
        expectTester2.balanceY = initialAmounts;
        expectTester2.pairDepositedX = depositXAmount;
        expectTester2.pairBorrowedY = borrowYAmount;
        fixture.verifyAddress(expectTester2);
    }

    function testBorrowYAgainstLWithApproval() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 collateralMintX = 400e18;
        uint256 collateralMintY = 100e18;
        uint256 expectedCollateralL = 200e18;
        uint256 borrowYAmount = 100e18;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);
        // collateral mint
        fixture.mintFor(tester2, collateralMintX, collateralMintY);

        delegationFixture.approveDebtTransferAndDelegation(tester2, tester, BORROW_Y, borrowYAmount);
        delegationFixture.delegateBorrow(tester2, tester, tester, 0, borrowYAmount);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_Y);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialMintX + collateralMintX;
        expectPair.tokenY = initialMintY + collateralMintY - borrowYAmount;
        expectPair.reserveXAssets = initialMintX + collateralMintX;
        expectPair.reserveYAssets = initialMintY + collateralMintY;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = initialAmounts;
        expectTester.balanceY = initialAmounts + borrowYAmount;
        fixture.verifyAddress(expectTester);

        // check tester2
        FactoryPairTestFixture.ExpectedAddressState memory expectTester2;
        expectTester2.toCheck = tester2;
        expectTester2.balanceX = initialAmounts - collateralMintX;
        expectTester2.balanceY = initialAmounts - collateralMintY;
        expectTester2.pairLiquidity = expectedCollateralL;
        expectTester2.pairBorrowedY = borrowYAmount;
        fixture.verifyAddress(expectTester2);
    }

    function testBorrowXAndYAgainstL() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 collateralMintX = 400e18;
        uint256 collateralMintY = 100e18;
        uint256 expectedCollateralL = 200e18;
        uint256 borrowXAmount = 100e18;
        uint256 borrowYAmount = 75e18;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);
        // collateral mint
        fixture.mintFor(tester2, collateralMintX, collateralMintY);

        delegationFixture.approveDebtTransferAndDelegation(tester2, tester, BORROW_X, borrowXAmount);
        delegationFixture.approveDebtTransferAndDelegation(tester2, tester, BORROW_Y, borrowYAmount);
        delegationFixture.delegateBorrow(tester2, tester, tester, borrowXAmount, borrowYAmount);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_X);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_Y);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialMintX + collateralMintX - borrowXAmount;
        expectPair.tokenY = initialMintY + collateralMintY - borrowYAmount;
        expectPair.reserveXAssets = initialMintX + collateralMintX;
        expectPair.reserveYAssets = initialMintY + collateralMintY;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = initialAmounts + borrowXAmount;
        expectTester.balanceY = initialAmounts + borrowYAmount;
        fixture.verifyAddress(expectTester);

        // check tester2
        FactoryPairTestFixture.ExpectedAddressState memory expectTester2;
        expectTester2.toCheck = tester2;
        expectTester2.balanceX = initialAmounts - collateralMintX;
        expectTester2.balanceY = initialAmounts - collateralMintY;
        expectTester2.pairLiquidity = expectedCollateralL;
        expectTester2.pairBorrowedX = borrowXAmount;
        expectTester2.pairBorrowedY = borrowYAmount;
        fixture.verifyAddress(expectTester2);
    }

    function testBorrowTwiceWithOneApprovalFails() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 depositX = 400e18;
        uint256 borrowedY = 25e18;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);

        // deposit
        fixture.depositFor(tester, depositX, 0);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_Y, borrowedY);
        delegationFixture.delegateBorrow(tester, tester2, tester2, 0, borrowedY);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_Y);

        // test borrow more and it should fail
        vm.expectRevert(
            abi.encodeWithSelector(
                ERC20InsufficientAllowance.selector, address(delegationFixture.delegationContract()), 0, 1
            )
        );
        delegationFixture.delegateBorrow(tester, tester2, tester2, 0, 1);
    }

    function testBorrowTwiceWithEnoughApproval() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 depositX = 400e18;
        uint256 borrowY = 25e18;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);

        // deposit
        fixture.depositFor(tester, depositX, 0);

        // approve tester2 to receive the assets
        fixture.approveDebt(tester, tester2, BORROW_Y, borrowY);

        // multiple borrows are allowed as long as it's collateral can cover debt.
        fixture.approveDebt(tester, tester2, BORROW_Y, borrowY);
        fixture.approveDebt(tester, tester2, BORROW_Y, borrowY);
    }

    function testZeroApprovalAndBorrowShouldFail() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 depositX = 400e18;
        uint256 borrowY = 1;
        uint256 approveAmountY = 0;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);

        // deposit
        fixture.depositFor(tester, depositX, 0);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_Y, approveAmountY);
        vm.expectRevert(
            abi.encodeWithSelector(
                ERC20InsufficientAllowance.selector, address(delegationFixture.delegationContract()), 0, borrowY
            )
        );
        delegationFixture.delegateBorrow(tester, tester2, tester2, 0, borrowY);
    }

    function testNoneApprovalAndBorrowShouldFail() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 depositX = 400e18;
        uint256 borrowY = 1;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);

        // deposit
        fixture.depositFor(tester, depositX, 0);

        vm.expectRevert('Delegation not allowed');
        delegationFixture.delegateBorrow(tester, tester2, tester2, 0, borrowY);
    }

    function testApprovalLessThanBorrowShouldFail() public {
        uint256 initialMintX = 4000e18;
        uint256 initialMintY = 1000e18;
        uint256 depositX = 400e18;
        uint256 borrowY = 2;
        uint256 approveAmountY = 1;

        // initial mint
        fixture.mintForAndInitializeBlocks(tester3, initialMintX, initialMintY);

        // deposit
        fixture.depositFor(tester, depositX, 0);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_Y, approveAmountY);
        vm.expectRevert(
            abi.encodeWithSelector(
                ERC20InsufficientAllowance.selector, address(delegationFixture.delegationContract()), 1, borrowY
            )
        );
        delegationFixture.delegateBorrow(tester, tester2, tester2, 0, borrowY);
    }

    function testBorrowWithoutOtherMintersFails() public {
        uint256 initialMintX = 8e18;
        uint256 initialMintY = 2e18;

        fixture.mintForAndInitializeBlocks(tester, initialMintX, initialMintY);

        vm.expectRevert(Validation.InsufficientLiquidity.selector);
        fixture.borrowLiquidityFor(tester, 3e18);
    }
}
