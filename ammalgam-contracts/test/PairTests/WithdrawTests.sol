// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {DEPOSIT_X, DEPOSIT_Y} from 'contracts/interfaces/tokens/ITokenController.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract WithdrawTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;

    FactoryPairTestFixture private fixture;

    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(tester, 500_000e18, 500_000e18).transferTokensTo(tester2, 5000e18, 5000e18);
    }

    function testWithdrawX() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 5000e18;
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount + depositXAmount);

        uint256 b4withdraw = fixture.tokenX().balanceOf(tester);

        fixture.withdrawFor(tester, depositXAmount, 0);

        assertEq(fixture.tokenX().balanceOf(tester), b4withdraw + depositXAmount);
        assertEq(pair.tokens(DEPOSIT_X).balanceOf(tester), 0);
        assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount);
    }

    function testWithdrawXandY() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 5000e18;
        uint256 depositYAmount = 1000e18;
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        uint256 b4withdraw0 = fixture.tokenX().balanceOf(tester);
        uint256 b4withdraw1 = fixture.tokenY().balanceOf(tester);

        fixture.withdrawFor(tester, depositXAmount, depositYAmount);

        assertEq(fixture.tokenX().balanceOf(tester), b4withdraw0 + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(tester), b4withdraw1 + depositYAmount);

        assertEq(pair.tokens(DEPOSIT_X).balanceOf(tester), 0);
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(tester), 0);

        assertEq(
            fixture.tokenX().balanceOf(address(pair)),
            tokenXAmount,
            'tokenX of Pair balance Should be token of liquidity only'
        );
        assertEq(
            fixture.tokenY().balanceOf(address(pair)),
            tokenYAmount,
            'tokenY of Pair balance Should be token of liquidity only'
        );
    }

    function testWithdrawXPartial() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 5000e18;
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        uint256 partialAmount = 1000e18;

        fixture.withdrawFor(tester, partialAmount, 0);
        fixture.withdrawFor(tester, depositXAmount - partialAmount, 0);

        assertEq(pair.tokens(DEPOSIT_X).balanceOf(tester), 0);
    }

    function testWithdrawXExceedsBalance() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 5000e18;
        uint256 depositYAmount = 0;
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        vm.expectRevert(
            abi.encodeWithSelector(ERC20InsufficientBalance.selector, tester, depositXAmount, depositXAmount + 1)
        );
        fixture.withdrawFor(tester, depositXAmount + 1, 0);
    }

    function testWithdrawYExceedsBalance() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 0;
        uint256 depositYAmount = 5000e18;
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        vm.expectRevert(
            abi.encodeWithSelector(ERC20InsufficientBalance.selector, tester, depositYAmount, depositYAmount + 1)
        );
        fixture.withdrawFor(tester, 0, depositYAmount + 1);
    }

    function testWithdrawToOther() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 5000e18;
        uint256 depositYAmount = 0;
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        uint256 b4withdraw = fixture.tokenX().balanceOf(tester);

        uint256 balanceXBeforeDeposit = fixture.tokenX().balanceOf(tester2);
        uint256 balanceYBeforeDeposit = fixture.tokenY().balanceOf(tester2);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_X).transfer(pairAddress, depositXAmount);
        pair.withdraw(tester2);
        vm.stopPrank();

        assertEq(fixture.tokenX().balanceOf(tester2), balanceXBeforeDeposit + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(tester2), balanceYBeforeDeposit);
        assertEq(pair.tokens(DEPOSIT_X).balanceOf(tester), 0);
        assertEq(fixture.tokenX().balanceOf(tester), b4withdraw);
    }

    function addLiquidityAndDeposit(
        address _to,
        uint256 _tokenXAmount,
        uint256 _tokenYAmount,
        uint256 _depositXAmount,
        uint256 _depositYAmount
    ) private {
        fixture.mintForAndInitializeBlocks(_to, _tokenXAmount, _tokenYAmount);
        fixture.depositFor(_to, _depositXAmount, _depositYAmount);
    }
}
