// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import 'forge-std/Test.sol';

import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract BorrowUnderflowTest is Test {
    FactoryPairTestFixture private fixture;
    address private tester;
    address private pairAddress;
    uint256 private initialX = 20e18;
    uint256 private initialY = 80e18;

    function setUp() public {
        address random = vm.addr(99);
        tester = vm.addr(100);
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
        fixture.updateExternalLiquidity(type(uint112).max / 2);
        pairAddress = fixture.pairAddress();
    }

    function testBorrowXUnderflow() public {
        uint256 borrowAmountX = 2e18;
        uint256 depositAmountY = 24e18;
        fixture.transferTokensTo(tester, 0, depositAmountY);
        fixture.depositFor(tester, 0, depositAmountY);
        fixture.borrowFor(tester, borrowAmountX, 0);

        vm.startPrank(tester);
        FlashBorrowCallee callee = new FlashBorrowCallee(fixture);
        uint256 transferXForRepay = 1e18;
        fixture.transferTokensTo(tester, transferXForRepay, 0);
        fixture.tokenX().approve(address(callee), transferXForRepay);
        callee.borrow(tester, transferXForRepay, 0);
        vm.stopPrank();

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialX - borrowAmountX + transferXForRepay;
        expectPair.tokenY = initialY + depositAmountY;
        expectPair.reserveXAssets = initialX;
        expectPair.reserveYAssets = initialY;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = borrowAmountX;
        expectTester.pairDepositedY = depositAmountY;
        expectTester.pairBorrowedX = borrowAmountX - transferXForRepay;
        fixture.verifyAddress(expectTester);
    }

    function testBorrowYUnderflow() public {
        uint256 borrowAmountY = 8e18;
        uint256 depositAmountX = 3e18;
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);
        fixture.borrowFor(tester, 0, borrowAmountY);

        vm.startPrank(tester);
        FlashBorrowCallee callee = new FlashBorrowCallee(fixture);
        uint256 transferYForRepay = 4e18;
        fixture.transferTokensTo(tester, 0, transferYForRepay);
        fixture.tokenY().approve(address(callee), transferYForRepay);
        callee.borrow(tester, 0, transferYForRepay);
        vm.stopPrank();

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialX + depositAmountX;
        expectPair.tokenY = initialY - borrowAmountY + transferYForRepay;
        expectPair.reserveXAssets = initialX;
        expectPair.reserveYAssets = initialY;
        fixture.verifyPair(expectPair);

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceY = borrowAmountY;
        expectTester.pairDepositedX = depositAmountX;
        expectTester.pairBorrowedY = borrowAmountY - transferYForRepay;
        fixture.verifyAddress(expectTester);
    }
}

contract FlashBorrowCallee is ICallback {
    FactoryPairTestFixture private fixture;

    constructor(
        FactoryPairTestFixture _fixture
    ) {
        fixture = _fixture;
    }

    function borrow(address from, uint256 transferXForRepay, uint256 transferYForRepay) external {
        bytes memory data = abi.encode(from, transferXForRepay, transferYForRepay);
        // borrow 0,0 to trigger the call back
        fixture.pair().borrow(address(this), 0, 0, data);
    }

    function ammalgamBorrowCallV1(
        address, // sender,
        uint256, // amountXAssets,
        uint256, // amountYAssets,
        uint256, // amountXShares,
        uint256, // amountYShares,
        bytes calldata data
    ) external {
        (address from, uint256 transferXForRepayAssets, uint256 transferYForRepayAssets) =
            abi.decode(data, (address, uint256, uint256));

        address _pair = fixture.pairAddress();

        if (transferXForRepayAssets > 0) {
            fixture.tokenX().transferFrom(from, _pair, transferXForRepayAssets);
        }

        if (transferYForRepayAssets > 0) {
            fixture.tokenY().transferFrom(from, _pair, transferYForRepayAssets);
        }

        fixture.pair().repay(from);
    }

    function ammalgamBorrowLiquidityCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) external {} // noop

    function ammalgamSwapCallV1(address sender, uint256 amountX, uint256 amountY, bytes calldata data) external { /* noop */ }
    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external { /* noop */ }
}
