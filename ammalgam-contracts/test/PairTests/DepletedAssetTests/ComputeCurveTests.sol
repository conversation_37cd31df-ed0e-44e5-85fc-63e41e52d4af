// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';

contract ComputeCurveTests is Test {
    function testComputeCurve() public pure {
        uint256 initialMintX = 4e18;
        uint256 initialMintY = 3e18;
        uint256 missingXAssets = 2e18;
        uint256 missingYAssets = 2e18;
        uint256 buffer = 95;

        assertEq(computeCurve(initialMintX, initialMintX, initialMintY, missingXAssets, missingYAssets), initialMintY);
        assertEq(computeCurve(initialMintY, initialMintY, initialMintX, missingYAssets, missingXAssets), initialMintX);
        // right buffer (5.7, ~2.105)
        uint256 bufferY = Math.ceilDiv(missingYAssets * 100, buffer); // / buffer
        assertApproxEqAbs(
            computeCurve(bufferY, initialMintY, initialMintX, missingYAssets, missingXAssets),
            5.7e18,
            2,
            'cant get exact due to truncation, both ceilDiv and div are off on bufferY.'
        );
        assertEq(computeCurve(5.7e18, initialMintX, initialMintY, missingXAssets, missingYAssets), bufferY);

        // right of buffer (6, 2.1)
        assertEq(computeCurve(6e18, initialMintX, initialMintY, missingXAssets, missingYAssets), 2.1e18);
        assertEq(computeCurve(2.1e18, initialMintY, initialMintX, missingYAssets, missingXAssets), 6e18);
        // right of buffer (8, 2.075)
        assertEq(computeCurve(8e18, initialMintX, initialMintY, missingXAssets, missingYAssets), 2.075e18);
        assertEq(computeCurve(2.075e18, initialMintY, initialMintX, missingYAssets, missingXAssets), 8e18);

        // left buffer (2.105, 5.7)
        assertEq(computeCurve(2.1e18, initialMintX, initialMintY, missingXAssets, missingYAssets), 6e18);
        assertEq(computeCurve(6e18, initialMintY, initialMintX, missingYAssets, missingXAssets), 2.1e18);

        // left of buffer (2.1, 6)
        assertEq(computeCurve(2.075e18, initialMintX, initialMintY, missingXAssets, missingYAssets), 8e18);
        assertEq(computeCurve(8e18, initialMintY, initialMintX, missingYAssets, missingXAssets), 2.075e18);
    }

    function computeCurve(
        uint256 a,
        uint256 reserveA,
        uint256 reserveB,
        uint256 missingA,
        uint256 missingB
    ) public pure returns (uint256 b) {
        uint256 decimals = 1;
        b = DepletedAssetUtils.computeCurve(a, reserveA, reserveB, missingA, missingB, decimals);
    }
}
