// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract DepletedAssetDepositTests is Test {
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random;
    address private tester;

    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    uint256 private initialLiquidity;
    uint256 private constant BUFFER = 95;

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 4e18;
        initialMintY = 3e18;
        missingXAssets = 3e18;
        missingYAssets = 2e18;

        random = vm.addr(1);
        fixture.transferTokensTo(random, initialMintX, initialMintY);
        initialLiquidity = fixture.mintForAndInitializeBlocks(random, initialMintX, initialMintY) + 1000;
        fixture.borrowFor(random, missingXAssets, missingYAssets);
    }

    function testDepletedAssetDepositXReplenishes() public {
        // move from (4, 3) to (3.15, 4 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.15e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 depositX = 1e18;
        uint256 fromReserveX = 0.15e18;

        fixture.transferTokensTo(tester, depositX, 0);
        // deposit 1x to replenish moves curve to (3, 4 + fee) and gives 0.15 extra to depositor
        fixture.depositFor(tester, depositX, 0, fromReserveX, 0);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + depositX,
                tokenY: reserveYBefore - missingYAssets,
                reserveXAssets: reserveXBefore - fromReserveX,
                reserveYAssets: reserveYBefore
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairDepositedX = depositX + fromReserveX;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetDepositXReplenishesWithoutOverflow() public {
        // move from (4, 3) to (3.15, 4 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.15e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 depositX = 4e18;
        uint256 fromReserveX = 0.15e18;

        fixture.transferTokensTo(tester, depositX, 0);
        // deposit 1x to replenish moves curve to (3, 4 + fee) and gives 0.15 extra to depositor
        fixture.depositFor(tester, depositX, 0, fromReserveX, 0);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + depositX,
                tokenY: reserveYBefore - missingYAssets,
                reserveXAssets: reserveXBefore - fromReserveX,
                reserveYAssets: reserveYBefore
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairDepositedX = depositX + fromReserveX;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetDepositXPartiallyReplenishes() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 depositX = 1e18;
        uint256 fromReserveX = depositX;

        fixture.transferTokensTo(tester, depositX, 0);
        // deposit 1x to replenish moves curve to (2.1, 6 + fee) and gives 1 extra to depositor
        fixture.depositFor(tester, depositX, 0, fromReserveX, 0);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + depositX,
                tokenY: reserveYBefore - missingYAssets,
                reserveXAssets: reserveXBefore - fromReserveX,
                reserveYAssets: reserveYBefore
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairDepositedX = depositX + fromReserveX;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetDepositYReplenishes() public {
        // move from (4, 3) to (6 + fee, 2.1) where y is depleted
        fixture.moveReservesRightToYValue(random, 2.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 depositY = 1e18;
        uint256 fromReserveY = 0.1e18;

        fixture.transferTokensTo(tester, 0, depositY);
        // deposit 1x to replenish moves curve to (6 + fee, 2) and gives 0.1 extra to depositor
        fixture.depositFor(tester, 0, depositY, 0, fromReserveY);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets,
                tokenY: reserveYBefore - missingYAssets + depositY,
                reserveXAssets: reserveXBefore,
                reserveYAssets: reserveYBefore - fromReserveY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairDepositedY = depositY + fromReserveY;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetDepositYReplenishesWithoutOverflow() public {
        // move from (4, 3) to (6 + fee, 2.1) where y is depleted
        fixture.moveReservesRightToYValue(random, 2.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 depositY = 3e18;
        uint256 fromReserveY = 0.1e18;

        fixture.transferTokensTo(tester, 0, depositY);
        // deposit 1x to replenish moves curve to (6 + fee, 2) and gives 0.1 extra to depositor
        fixture.depositFor(tester, 0, depositY, 0, fromReserveY);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets,
                tokenY: reserveYBefore - missingYAssets + depositY,
                reserveXAssets: reserveXBefore,
                reserveYAssets: reserveYBefore - fromReserveY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairDepositedY = depositY + fromReserveY;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetDepositYPartiallyReplenishes() public {
        // move from (4, 3) to (12 + fee, 2.05) where y is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 depositY = 1e18;
        uint256 fromReserveY = depositY;

        fixture.transferTokensTo(tester, 0, depositY);
        // deposit 1x to replenish moves curve to (8 + fee, 1.5) and gives  extra to depositor
        fixture.depositFor(tester, 0, depositY, 0, fromReserveY);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets,
                tokenY: reserveYBefore - missingYAssets + depositY,
                reserveXAssets: reserveXBefore,
                reserveYAssets: reserveYBefore - fromReserveY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.pairDepositedY = depositY + fromReserveY;

        fixture.verifyAddress(expectTester);
    }
}
