// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {AmmalgamFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';

contract DepletedAssetDepositTests is Test {
    IPairHarness private pair;
    address private pairAddress;

    address private random;
    address private tester;

    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    uint256 private initialLiquidity;
    uint256 private constant BUFFER = 95;

    event UpdateExternalLiquidity(uint112 externalLiquidity);

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 8e18;
        initialMintY = 2e18;

        random = vm.addr(1);
        fixture.transferTokensTo(random, initialMintX, initialMintY);
        initialLiquidity = fixture.mintForAndInitializeBlocks(random, initialMintX, initialMintY) + 1000;
    }

    function testExternalLiquidity_DefaultIsZero() public view {
        assertEq(pair.exposed_externalLiquidity(), 0);
    }

    function testExternalLiquidity_AnyAddressCanNotUpdateFuzz(
        address anyAddress
    ) public {
        vm.assume(anyAddress != fixture.factory().feeToSetter());

        vm.startPrank(anyAddress);
        vm.expectRevert(AmmalgamFactory.Forbidden.selector);
        pair.updateExternalLiquidity(100);

        assertEq(pair.exposed_externalLiquidity(), 0);
    }

    function testExternalLiquidity_Update() public {
        uint112 liquidity = 100;
        fixture.factorySetFeeTo(tester);

        // fixture is the default feeToSetter.
        vm.startPrank(address(fixture));
        vm.expectEmit(true, false, false, true);
        emit UpdateExternalLiquidity(liquidity);
        pair.updateExternalLiquidity(liquidity);
        assertEq(pair.exposed_externalLiquidity(), liquidity);
    }
}
