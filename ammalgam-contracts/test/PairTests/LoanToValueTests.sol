// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Q72} from 'contracts/libraries/constants.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    adjustAmountsByTickPrice,
    computeExpectedLiquidity,
    computeExpectedSwapInAmount,
    computeExpectedSwapInAmountWithoutFees,
    computeExpectedSwapOutAmount,
    computeExpectedSwapOutAmountWithoutFees
} from 'test/shared/utilities.sol';

contract LoanToValueTests is Test {
    uint256 private constant initialX = 8e18;
    uint256 private constant initialY = 2e18;
    uint256 private constant initialLiquidity = 4e18;
    uint256 private constant initialXPerY = initialX / initialY;

    uint8 private constant ALLOWED_LIQUIDITY_LEVERAGE = 100;
    uint8 private constant LTV = 75;
    uint8 private constant MAX_BORROW_PERCENTAGE = 90;

    function testLtvBorrowTooMuchXAndY() public {
        uint256 mintX = 4e18;
        uint256 mintY = 1e18;
        uint256 borrowX = mintX + 2; // add 2 X to make debt in L greater than collateral in L
        uint256 borrowY = mintY + 1;

        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY);
        uint256 borrowL = computeExpectedLiquidity(borrowX, borrowY, initialX, initialY);

        Validation.InputParams memory inputParams = getValidationParams(initialX, initialY, false, false);
        inputParams.userAssets[DEPOSIT_L] = mintL;
        inputParams.userAssets[BORROW_L] = borrowL;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        Validation.validateSolvency(inputParams);
    }

    function testLtvBorrowXAgainstY() public {
        uint256 borrowX = 3e18;

        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowX, initialY, initialX);
        uint256 collateralY = Math.ceilDiv(100 * collateralSwapped, LTV);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(initialX, initialY);
        inputParams.userAssets[DEPOSIT_Y] = collateralY;

        verifyMaxBorrow(inputParams, borrowX, 0);
    }

    function testLtvBorrowYAgainstX() public {
        uint256 borrowY = 0.75e18;

        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowY, initialX, initialY);
        uint256 collateralX = Math.ceilDiv(100 * collateralSwapped, LTV);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(initialX, initialY);
        inputParams.userAssets[DEPOSIT_X] = collateralX;

        verifyMaxBorrow(inputParams, 0, borrowY);
    }

    function testLtvBorrowXAgainstL() public {
        uint256 mintX = 1.6e18;
        uint256 mintY = 0.4e18;

        uint256 excessBorrowedX = computeExpectedSwapOutAmountWithoutFees(mintY * LTV / 100, initialY, initialX);
        uint256 borrowX = mintX + excessBorrowedX;

        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY);

        uint256 reserveX = initialX + mintX;
        uint256 reserveY = initialY + mintY;

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_L] = mintL;

        verifyMaxBorrow(inputParams, calculateLargestMultipleOfFactorLessOrEqualToValue(borrowX, 4), 0);
    }

    function testLtvBorrowYAgainstL() public {
        uint256 mintX = 1.6e18;
        uint256 mintY = 0.4e18;

        uint256 excessBorrowedY = computeExpectedSwapOutAmountWithoutFees(mintX * LTV / 100, initialX, initialY);
        uint256 borrowY = mintY + excessBorrowedY;

        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY);

        uint256 reserveX = initialX + mintX;
        uint256 reserveY = initialY + mintY;

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_L] = mintL;

        verifyMaxBorrow(inputParams, 0, borrowY);
    }

    function testLtvBorrowLeveragedLongX() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;

        uint256 borrowX = mintX / 2;
        uint256 borrowY =
            mintY + computeExpectedSwapOutAmountWithoutFees((mintX - borrowX) * LTV / 100, initialX, initialY);
        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY) - 1;

        uint256 reserveX = initialX + mintX;
        uint256 reserveY = initialY + mintY;

        Validation.InputParams memory inputParams = getValidationParams(reserveX, reserveY, true, true);
        inputParams.userAssets[DEPOSIT_L] = mintL;

        verifyMaxBorrow(inputParams, borrowX, borrowY);
    }

    function testLtvBorrowLeveragedShortX() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 borrowY = mintY / 2;

        // Borrow all the X 75% of the net of minted and borrowed Y
        uint256 borrowX =
            mintX + computeExpectedSwapOutAmountWithoutFees((mintY - borrowY) * LTV / 100, initialY, initialX);

        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY);

        uint256 reserveX = initialX + mintX;
        uint256 reserveY = initialY + mintY;

        Validation.InputParams memory inputParams = getValidationParams(reserveX, reserveY, true, true);
        inputParams.userAssets[DEPOSIT_L] = mintL;

        verifyMaxBorrow(inputParams, borrowX, borrowY);
    }

    /**
     * @notice Test that the max borrow is correct when borrowing L against X.
     * @dev When testing borrowing L, we want to think about the x and y that makes up as l individually so that we can
     *   take the net x which would be the collateral x less the x portion of the L and then compare that to the
     *   borrowed y portion of the l. Below we show the math used to set up this test
     *     l = sqrt(lx * ly),
     *     p = reserveX/reserveY and sp is the slippage price,
     *     (x - lx) * ltv / sp = ly, and
     *     lx = p * ly
     *
     *   Using this we can pick an amount of X to be used as collateral and then find the price by determining the
     *   amount of slippage to sell the X and then we can find the amount of y associated with l that could be borrowed
     *
     */
    function testLtvBorrowLAgainstX() public {
        uint256 borrowLY = 0.5e18;
        uint256 borrowLX = 2e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;

        uint256 swapInX = computeExpectedSwapInAmountWithoutFees(borrowLY, reserveX, reserveY);

        uint256 collateralNeededX = Math.ceilDiv(swapInX * 100, LTV);
        uint256 collateralX = collateralNeededX + borrowLX;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_X] = collateralX;

        verifyMaxBorrowLiquidity(inputParams, borrowL + 10);
    }

    function testLtvBorrowLAgainstY() public {
        uint256 borrowLY = 0.5e18;
        uint256 borrowLX = 2e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;

        uint256 swapInY = computeExpectedSwapInAmountWithoutFees(borrowLX, reserveY, reserveX);

        uint256 collateralNeededY = Math.ceilDiv(swapInY * 100, LTV);
        uint256 collateralY = collateralNeededY + borrowLY;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_Y] = collateralY;

        verifyMaxBorrowLiquidity(inputParams, borrowL);
    }

    function testLtvBorrowLAndYAgainstX() public {
        uint256 borrowLX = 2e18;
        uint256 borrowLY = 0.5e18;
        uint256 borrowY = 0.25e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;

        uint256 swapInX = computeExpectedSwapInAmountWithoutFees(borrowLY + borrowY, reserveX, reserveY);

        uint256 collateralNeededX = Math.ceilDiv(swapInX * 100, LTV);
        uint256 collateralX = collateralNeededX + borrowLX;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_X] = collateralX;
        inputParams.userAssets[BORROW_L] = borrowL;

        // BorrowL
        Validation.validateSolvency(inputParams);

        // BorrowY
        verifyMaxBorrow(inputParams, 0, borrowY);
    }

    function testLtvBorrowYAndLAgainstX() public {
        uint256 borrowLX = 2e18;
        uint256 borrowLY = 0.5e18;
        uint256 borrowY = 0.25e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;

        uint256 swapInX = computeExpectedSwapInAmountWithoutFees(borrowLY + borrowY, reserveX, reserveY);

        uint256 collateralNeededX = Math.ceilDiv(swapInX * 100, LTV);
        uint256 collateralX = collateralNeededX + borrowLX;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_X] = collateralX;
        inputParams.userAssets[BORROW_Y] = borrowY;

        // BorrowY
        Validation.validateSolvency(inputParams);

        // BorrowL
        verifyMaxBorrowLiquidity(inputParams, borrowL);
    }

    function testLtvBorrowLAndXAgainstY() public {
        uint256 borrowLX = 2e18;
        uint256 borrowLY = 0.5e18;
        uint256 borrowX = 1e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;

        uint256 swapInY = computeExpectedSwapInAmountWithoutFees(borrowLX + borrowX, reserveY, reserveX);

        uint256 collateralNeededY = Math.ceilDiv(swapInY * 100, LTV);
        uint256 collateralY = collateralNeededY + borrowLY;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_Y] = collateralY;
        inputParams.userAssets[BORROW_L] = borrowL;

        // BorrowL
        Validation.validateSolvency(inputParams);

        // BorrowY
        verifyMaxBorrow(inputParams, borrowX, 0);
    }

    function testLtvBorrowXAndLAgainstY() public {
        uint256 borrowLX = 2e18;
        uint256 borrowLY = 0.5e18;
        uint256 borrowX = 1e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;

        uint256 swapInY = computeExpectedSwapInAmountWithoutFees(borrowLX + borrowX, reserveY, reserveX);

        uint256 collateralNeededY = Math.ceilDiv(swapInY * 100, LTV);
        uint256 collateralY = collateralNeededY + borrowLY;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_Y] = collateralY;
        inputParams.userAssets[BORROW_X] = borrowX;

        // BorrowX
        Validation.validateSolvency(inputParams);

        // BorrowL
        verifyMaxBorrowLiquidity(inputParams, borrowL);
    }

    /**
     *   If after closing a deposit a user still had a small amount of
     *   liquidity left, could they later put in a different collateral
     *   and borrow x?
     */
    function testLtvBorrowXAgainstXFails() public {
        uint256 collateralX = 4e18;
        uint256 borrowX = 2e18;

        Validation.InputParams memory inputParams = getValidationParams(initialX, initialY, false, false);
        inputParams.userAssets[DEPOSIT_X] = collateralX;
        inputParams.userAssets[BORROW_X] = borrowX;

        vm.expectRevert(Validation.AmmalgamCannotBorrowAgainstSameCollateral.selector);
        Validation.validateSolvency(inputParams);
    }

    function testLtvBorrowYAgainstYFails() public {
        uint256 collateralY = 2e18;
        uint256 borrowY = 1e18;

        Validation.InputParams memory inputParams = getValidationParams(initialX, initialY, false, false);
        inputParams.userAssets[DEPOSIT_Y] = collateralY;
        inputParams.userAssets[BORROW_Y] = borrowY;

        vm.expectRevert(Validation.AmmalgamCannotBorrowAgainstSameCollateral.selector);
        Validation.validateSolvency(inputParams);
    }

    function testLtvBorrowXAgainstXYFails() public {
        uint256 collateralY = 1.7e18;
        uint256 collateralX = 1.0e18;
        uint256 borrowX = (collateralY * initialXPerY * LTV) / 100;

        Validation.InputParams memory inputParams = getValidationParams(initialX, initialY, false, false);
        inputParams.userAssets[DEPOSIT_X] = collateralX;
        inputParams.userAssets[DEPOSIT_Y] = collateralY;
        inputParams.userAssets[BORROW_X] = borrowX;

        vm.expectRevert(Validation.AmmalgamCannotBorrowAgainstSameCollateral.selector);
        Validation.validateSolvency(inputParams);
    }

    function testLtvWithdrawYWithXAgainstY() public {
        uint256 collateralY = 2e18;
        uint256 withdrawY = collateralY / 2;
        uint256 borrowX =
            computeExpectedSwapOutAmountWithoutFees((collateralY - withdrawY) * LTV / 100, initialY, initialX);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(initialX, initialY);
        inputParams.userAssets[DEPOSIT_Y] = collateralY - withdrawY;
        inputParams.userAssets[BORROW_X] = borrowX;

        verifyMaxWithdrawY(inputParams);
    }

    function testLtvWithdrawXWithYAgainstX() public {
        uint256 collateralX = 2e18;
        uint256 withdrawX = collateralX / 2;
        uint256 borrowY =
            computeExpectedSwapOutAmountWithoutFees((collateralX - withdrawX) * LTV / 100, initialX, initialY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(initialX, initialY);
        inputParams.userAssets[DEPOSIT_X] = collateralX - withdrawX;
        inputParams.userAssets[BORROW_Y] = borrowY;

        // need to deposit less than 1 to fail
        verifyMaxWithdrawX(inputParams, 5);
    }

    function testLtvWithdrawXWithLAgainstX() public {
        uint256 withdrawX = 1e18;
        uint256 borrowLY = 0.5e18;
        uint256 borrowLX = 2e18;

        uint256 swapInX = computeExpectedSwapInAmountWithoutFees(borrowLY, initialX, initialY);

        uint256 collateralNeededX = Math.ceilDiv(swapInX * 100, LTV);
        uint256 collateralXAfterWithdraw = collateralNeededX + borrowLX;
        uint256 collateralX = collateralXAfterWithdraw + withdrawX;
        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(initialX, initialY);
        inputParams.userAssets[DEPOSIT_X] = collateralX + 2 - withdrawX;
        inputParams.userAssets[BORROW_L] = borrowL;

        verifyMaxWithdrawX(inputParams);
    }

    function testLtvWithdrawYWithLAgainstY() public {
        uint256 withdrawY = 1e18;
        uint256 borrowLY = 0.5e18;
        uint256 borrowLX = 2e18;

        uint256 swapInY = computeExpectedSwapInAmountWithoutFees(borrowLX, initialY, initialX);

        uint256 collateralNeededY = Math.ceilDiv(swapInY * 100, LTV);
        uint256 collateralYAfterWithdraw = collateralNeededY + borrowLY;
        uint256 collateralY = collateralYAfterWithdraw + withdrawY;
        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(initialX, initialY);
        inputParams.userAssets[DEPOSIT_Y] = collateralY - withdrawY;
        inputParams.userAssets[BORROW_L] = borrowL;

        verifyMaxWithdrawY(inputParams);
    }

    function testLtvWithdrawXWithYAgainstXAndL() public {
        uint256 withdrawX = 1e18;
        uint256 depositXAfterWithdraw = 4e18;
        uint256 depositX = depositXAfterWithdraw + withdrawX;

        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY);

        uint256 swapYOut =
            computeExpectedSwapOutAmountWithoutFees((mintX + depositXAfterWithdraw) * LTV / 100, initialX, initialY);
        uint256 borrowY = mintY + swapYOut;

        uint256 reserveX = initialX + mintX;
        uint256 reserveY = initialY + mintY;

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_X] = depositX - withdrawX;
        inputParams.userAssets[DEPOSIT_L] = mintL;
        inputParams.userAssets[BORROW_Y] = borrowY;

        // need to deposit less than 1 to fail
        verifyMaxWithdrawX(inputParams, 17);
    }

    function testLtvWithdrawYWithXAgainstYAndL() public {
        uint256 withdrawY = 1e18;
        uint256 depositYAfterWithdraw = 1e18;
        uint256 depositY = depositYAfterWithdraw + withdrawY;

        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 mintL = computeExpectedLiquidity(mintX, mintY, initialX, initialY);

        uint256 swapXOut =
            computeExpectedSwapOutAmountWithoutFees((mintY + depositYAfterWithdraw) * LTV / 100, initialY, initialX);

        uint256 borrowX = mintX + swapXOut;

        uint256 reserveX = initialX + mintX;
        uint256 reserveY = initialY + mintY;

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_Y] = depositY - withdrawY;
        inputParams.userAssets[DEPOSIT_L] = mintL;
        inputParams.userAssets[BORROW_X] = borrowX;

        verifyMaxWithdrawY(inputParams);
    }

    function testLtvBurnLWithXAgainstL() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 mintL = Math.sqrt(mintX * mintY);

        uint256 withdrawL = mintL / 4;

        uint256 mintXAtWithdraw = mintX * 3 / 4;
        uint256 mintYAtWithdraw = mintY * 3 / 4;
        uint256 reserveXAtWithdraw = initialX + mintXAtWithdraw;
        uint256 reserveYAtWithdraw = initialY + mintYAtWithdraw;

        // Can borrow up their current liquidity in x plus 75% of the liquidity
        // in y. Slippage is based on the reserves without the users deposited
        // liquidity
        uint256 borrowX =
            mintXAtWithdraw + computeExpectedSwapOutAmountWithoutFees(mintYAtWithdraw * LTV / 100, initialY, initialX);

        // State of the reserves include the users deposited liquidity.
        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveXAtWithdraw, reserveYAtWithdraw);
        inputParams.userAssets[DEPOSIT_L] = mintL - withdrawL;
        inputParams.userAssets[BORROW_X] = borrowX;

        verifyMaxBurn(inputParams);
    }

    function testLtvBurnWithYAgainstL() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 mintL = Math.sqrt(mintX * mintY);
        uint256 withdrawL = Math.sqrt(mintX * mintY) / 5;

        uint256 mintXAtWithdraw = mintX * 4 / 5;
        uint256 mintYAtWithdraw = mintY * 4 / 5;
        uint256 reserveXAtWithdraw = initialX + mintXAtWithdraw;
        uint256 reserveYAtWithdraw = initialY + mintYAtWithdraw;

        // Can borrow up their current liquidity in y plus 75% of the liquidity
        // in x. Slippage is based on the reserves without the users deposited
        uint256 borrowY =
            mintYAtWithdraw + computeExpectedSwapOutAmountWithoutFees(mintXAtWithdraw * LTV / 100, initialX, initialY);

        // State of the reserves include the users deposited liquidity.
        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveXAtWithdraw, reserveYAtWithdraw);
        inputParams.userAssets[DEPOSIT_L] = mintL - withdrawL;
        inputParams.userAssets[BORROW_Y] = borrowY;

        verifyMaxBurn(inputParams);
    }

    function testLtvBurnWithXAgainstLAndY() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 mintL = Math.sqrt(mintX * mintY);
        uint256 depositY = mintY / 3;

        uint256 withdrawL = mintL / 4;

        uint256 mintXAtWithdraw = mintX * 3 / 4;
        uint256 mintYAtWithdraw = mintY * 3 / 4;
        uint256 reserveXAtWithdraw = initialX + mintXAtWithdraw;
        uint256 reserveYAtWithdraw = initialY + mintYAtWithdraw;

        // Can borrow up their current liquidity less withdraw with all x plus 75% of the liquidity and deposit in y.
        uint256 borrowX = mintXAtWithdraw
            + computeExpectedSwapOutAmountWithoutFees((mintYAtWithdraw + depositY) * LTV / 100, initialY, initialX);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMax(reserveXAtWithdraw, reserveYAtWithdraw);
        inputParams.userAssets[DEPOSIT_L] = mintL - withdrawL;
        inputParams.userAssets[DEPOSIT_Y] = depositY;
        inputParams.userAssets[BORROW_X] = borrowX;

        verifyMaxBurn(inputParams);
    }

    function testLtvBurnWithYAgainstLAndX() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 mintL = Math.sqrt(mintX * mintY);
        uint256 depositX = mintX / 10;

        uint256 withdrawL = Math.ceilDiv(mintL * 5, 6);

        uint256 mintXAtWithdraw = mintX / 6;
        uint256 mintYAtWithdraw = mintY / 6;
        uint256 reserveXAtWithdraw = initialX + mintXAtWithdraw;
        uint256 reserveYAtWithdraw = initialY + mintYAtWithdraw;

        // Can borrow up their current liquidity less withdraw with all y plus 75% of the liquidity and deposit in x.
        uint256 borrowY = mintYAtWithdraw
            + computeExpectedSwapOutAmountWithoutFees((mintXAtWithdraw + depositX) * LTV / 100, initialX, initialY);

        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveXAtWithdraw, reserveYAtWithdraw);
        inputParams.userAssets[DEPOSIT_L] = mintL - withdrawL;
        inputParams.userAssets[DEPOSIT_X] = depositX;
        inputParams.userAssets[BORROW_Y] = borrowY;

        verifyMaxBurn(inputParams);
    }

    function testMaxSlippageFails() public {
        uint256 depositX = 1000e18;
        uint256 borrowL = initialLiquidity / 2;
        uint256 borrowLX = 4e18;
        uint256 borrowLY = 1e18;

        uint256 reserveX = initialX - borrowLX;
        uint256 reserveY = initialY - borrowLY;
        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_X] = depositX;
        inputParams.userAssets[BORROW_L] = borrowL;

        vm.expectRevert(Validation.AmmalgamMaxSlippage.selector);
        Validation.validateSolvency(inputParams);
    }

    function testSlippageBeyondQuadraticFee() public {
        uint256 reserveX = 4e18;
        uint256 reserveY = 1e18;
        uint256 depositX = Math.ceilDiv(10e18 * 4, 3);

        uint256 borrowY = computeExpectedSwapOutAmountWithoutFees(10e18, reserveX, reserveY);
        Validation.InputParams memory inputParams = getValidationParamsIgnoreMin(reserveX, reserveY);
        inputParams.userAssets[DEPOSIT_X] = depositX;

        Validation.validateSolvency(inputParams);

        verifyMaxBorrow(inputParams, 0, borrowY, 1);
    }

    /*
     *  TEST HELPER METHODS
     */

    /**
     * @dev Since values are divided, truncated, than multiplied again, we need to find the largest multiple of the
     *   factor less than or equal to the value.
     * @param value The value to find the largest multiple of the factor less than or equal to.
     * @param factor The factor to find the largest multiple of.
     */
    function calculateLargestMultipleOfFactorLessOrEqualToValue(
        uint256 value,
        uint256 factor
    ) private pure returns (uint256) {
        return value / factor * factor;
    }

    function getValidationParamsIgnoreMax(
        uint256 reserveX,
        uint256 reserveY
    ) private pure returns (Validation.InputParams memory inputParams) {
        return getValidationParams(reserveX, reserveY, true, false);
    }

    function getValidationParamsIgnoreMin(
        uint256 reserveX,
        uint256 reserveY
    ) private pure returns (Validation.InputParams memory inputParams) {
        return getValidationParams(reserveX, reserveY, false, true);
    }

    function getValidationParams(
        uint256 reserveX,
        uint256 reserveY,
        bool useSqrtPriceMin,
        bool useSqrtPriceMax
    ) private pure returns (Validation.InputParams memory inputParams) {
        uint112 activeLiquidityAssets = uint112(Math.sqrt(reserveX * reserveY));
        uint256 activeLiquidityScalerInQ72 = Math.mulDiv(Math.sqrt(reserveX * reserveY), Q72, activeLiquidityAssets);
        uint256 sqrtPriceInQ72 = Math.sqrt(reserveX * (2 ** 112) / reserveY) * (2 ** 72) / (2 ** 56);
        inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0, 0, 0, 0],
            sqrtPriceMinInQ72: useSqrtPriceMin ? sqrtPriceInQ72 : 1,
            sqrtPriceMaxInQ72: useSqrtPriceMax
                ? sqrtPriceInQ72
                : Math.sqrt(uint256(type(uint112).max) * (2 ** 112)) * (2 ** (72 - 56)),
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });
    }

    function verifyMaxBorrow(Validation.InputParams memory inputParams, uint256 borrowX, uint256 borrowY) private {
        verifyMaxBorrow(inputParams, borrowX, borrowY, 1);
    }

    function verifyMaxBorrow(
        Validation.InputParams memory inputParams,
        uint256 borrowX,
        uint256 borrowY,
        uint256 increment
    ) private {
        Validation.InputParams memory cleanParams = copyParams(inputParams);

        if (borrowY == 0) {
            inputParams.userAssets[BORROW_X] = borrowX + increment;
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            Validation.validateSolvency(inputParams);
        } else if (borrowX == 0) {
            inputParams.userAssets[BORROW_Y] = borrowY + increment;

            vm.expectRevert(Validation.AmmalgamLTV.selector);
            Validation.validateSolvency(inputParams);
        } else {
            // Increment `borrowX`
            inputParams.userAssets[BORROW_X] = borrowX + increment;
            inputParams.userAssets[BORROW_Y] = borrowY;

            vm.expectRevert(Validation.AmmalgamLTV.selector);
            Validation.validateSolvency(inputParams);

            // Increment `borrowY`
            inputParams.userAssets[BORROW_X] = borrowX;
            inputParams.userAssets[BORROW_Y] = borrowY + increment;

            vm.expectRevert(Validation.AmmalgamLTV.selector);
            Validation.validateSolvency(inputParams);
        }

        Validation.validateSolvency(cleanParams);
    }

    function verifyMaxBorrowLiquidity(Validation.InputParams memory inputParams, uint256 borrowL) private {
        Validation.InputParams memory cleanParams = copyParams(inputParams);
        inputParams.userAssets[BORROW_L] = borrowL + 1;

        vm.expectRevert(Validation.AmmalgamLTV.selector);
        Validation.validateSolvency(inputParams);

        // max borrow approved
        Validation.validateSolvency(cleanParams);
    }

    function verifyMaxWithdrawX(
        Validation.InputParams memory inputParams
    ) private {
        verifyMaxWithdrawX(inputParams, 1);
    }

    function verifyMaxWithdrawX(Validation.InputParams memory inputParams, uint256 decrementStartingAssets) private {
        Validation.InputParams memory cleanParams = copyParams(inputParams);
        uint256 startingAssets = inputParams.userAssets[DEPOSIT_X];
        inputParams.userAssets[DEPOSIT_X] = startingAssets - decrementStartingAssets;

        vm.expectRevert(Validation.AmmalgamLTV.selector);
        Validation.validateSolvency(inputParams);

        // max burn approved
        inputParams.userAssets[DEPOSIT_X] = startingAssets;
        Validation.validateSolvency(cleanParams);
    }

    function verifyMaxWithdrawY(
        Validation.InputParams memory inputParams
    ) private {
        Validation.InputParams memory cleanParams = copyParams(inputParams);
        uint256 startingAssets = inputParams.userAssets[DEPOSIT_Y];
        inputParams.userAssets[DEPOSIT_Y] = startingAssets - 1;

        vm.expectRevert(Validation.AmmalgamLTV.selector);
        Validation.validateSolvency(inputParams);

        // max burn approved
        inputParams.userAssets[DEPOSIT_Y] = startingAssets;
        Validation.validateSolvency(cleanParams);
    }

    function verifyMaxBurn(
        Validation.InputParams memory inputParams
    ) private {
        verifyMaxBurn(inputParams, 1);
    }

    function verifyMaxBurn(Validation.InputParams memory inputParams, uint256 decrementStartingAssets) private {
        Validation.InputParams memory cleanParams = copyParams(inputParams);
        uint256 startingAssets = inputParams.userAssets[DEPOSIT_L];
        inputParams.userAssets[DEPOSIT_L] = startingAssets - decrementStartingAssets;
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        Validation.validateSolvency(inputParams);

        // max burn approved
        inputParams.userAssets[DEPOSIT_L] = startingAssets;
        Validation.validateSolvency(cleanParams);
    }

    function copyParams(
        Validation.InputParams memory inputParams
    ) private pure returns (Validation.InputParams memory) {
        uint256[6] memory userAssets = [
            inputParams.userAssets[0],
            inputParams.userAssets[1],
            inputParams.userAssets[2],
            inputParams.userAssets[3],
            inputParams.userAssets[4],
            inputParams.userAssets[5]
        ];
        return Validation.InputParams({
            userAssets: userAssets,
            sqrtPriceMinInQ72: inputParams.sqrtPriceMinInQ72,
            sqrtPriceMaxInQ72: inputParams.sqrtPriceMaxInQ72,
            activeLiquidityScalerInQ72: inputParams.activeLiquidityScalerInQ72,
            activeLiquidityAssets: inputParams.activeLiquidityAssets,
            reservesXAssets: inputParams.reservesXAssets,
            reservesYAssets: inputParams.reservesYAssets
        });
    }
}
