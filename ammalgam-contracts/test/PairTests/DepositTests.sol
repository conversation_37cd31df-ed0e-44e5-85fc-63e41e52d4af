// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/token/ERC20/IERC20.sol';
import {IERC1155Receiver} from '@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol';

import {DEPOSIT_L, DEPOSIT_X, DEPOSIT_Y} from 'contracts/interfaces/tokens/ITokenController.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {ERC1155Receiver} from 'test/shared/ERC1155Receiver.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {MINIMUM_LIQUIDITY, computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';

contract DepositTests is Test {
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;

    FactoryPairTestFixture private fixture;

    function setUp() public {
        tester = address(1111);
        tester2 = vm.addr(1112);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(tester, 5000e18, 5000e18).transferTokensTo(tester2, 5000e18, 5000e18);
    }

    function testDepositX() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 4000e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        assertEq(pair.tokens(DEPOSIT_X).balanceOf(tester), depositXAmount); // 1:1 for now
        assertEq(
            fixture.tokenX().balanceOf(address(pair)), tokenXAmount + depositXAmount, 'Should be liquidity and depositX'
        );
    }

    function testDepositY() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositYAmount = 4000e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, 0, depositYAmount);

        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(tester), depositYAmount);
        assertEq(
            fixture.tokenY().balanceOf(address(pair)), tokenYAmount + depositYAmount, 'Should be liquidity and depositY'
        );
    }

    function testDepositXAnd1() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = 3000e18;
        uint256 depositYAmount = 4000e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        assertEq(pair.tokens(DEPOSIT_X).balanceOf(tester), depositXAmount);
        assertEq(
            fixture.tokenX().balanceOf(address(pair)), tokenXAmount + depositXAmount, 'Should be liquidity and depositX'
        );
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(tester), depositYAmount);
        assertEq(
            fixture.tokenY().balanceOf(address(pair)), tokenYAmount + depositYAmount, 'Should be liquidity and depositY'
        );
    }

    function testDepositXAndSwap() public {
        uint256 swapAmount = 1e18; // see testSwapCase1
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        uint256 depositXAmount = 50e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        // Swap should work as before
        fixture.verifySwapXToY(tester, swapAmount, tokenXAmount, tokenYAmount);
    }

    function testDepositYAndSwap() public {
        uint256 swapAmount = 1e18; // see testSwapCase1
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        uint256 depositYAmount = 50e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, 0, depositYAmount);

        // Swap should work as before
        fixture.verifySwapXToY(tester, swapAmount, tokenXAmount, tokenYAmount);
    }

    function testDepositXAndMint() public {
        uint256 tokenXAmount = 1e18;
        uint256 tokenYAmount = 4e18;
        uint256 expectedLiquidity = 2e18; // sqrt(4*1)
        uint256 depositXAmount = 50e18;

        // First mint
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        // Second mint
        fixture.mintFor(tester, 2 * tokenXAmount, 2 * tokenYAmount);
        expectedLiquidity += 4e18; // sqrt((2*4)*(2*1))

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), expectedLiquidity);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), expectedLiquidity - MINIMUM_LIQUIDITY);

        assertEq(fixture.tokenX().balanceOf(address(pair)), 3 * tokenXAmount + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 3 * tokenYAmount);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 3 * tokenXAmount);
        assertEq(_reserveY, 3 * tokenYAmount);
    }

    function testDepositYAndMint() public {
        uint256 tokenXAmount = 1e18;
        uint256 tokenYAmount = 4e18;
        uint256 expectedLiquidity = 2e18; // sqrt(4*1)
        uint256 depositYAmount = 50e18;

        // First mint
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, 0, depositYAmount);

        // Second mint
        fixture.mintFor(tester, 2 * tokenXAmount, 2 * tokenYAmount);
        expectedLiquidity += 4e18; // sqrt((2*4)*(2*1))

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), expectedLiquidity, '`pair` liquidity matches expected');
        assertEq(
            pair.tokens(DEPOSIT_L).balanceOf(tester),
            expectedLiquidity - MINIMUM_LIQUIDITY,
            '`tester` liquidity matches expected'
        );

        assertEq(fixture.tokenX().balanceOf(address(pair)), 3 * tokenXAmount, '`pair` x balance matches expected');
        assertEq(
            fixture.tokenY().balanceOf(address(pair)),
            3 * tokenYAmount + depositYAmount,
            '`pair` y balance matches expected'
        );

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 3 * tokenXAmount, '`reserveX` matches expected');
        assertEq(_reserveY, 3 * tokenYAmount, '`reserveY` matches expected');
    }

    function testDepositXandYAndMint() public {
        uint256 tokenXAmount = 1e18;
        uint256 tokenYAmount = 4e18;
        uint256 expectedLiquidity = 2e18; // sqrt(4*1)
        uint256 depositXAmount = 50e18;
        uint256 depositYAmount = 60e18;

        // First mint
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        // Second mint
        fixture.mintFor(tester, 2 * tokenXAmount, 2 * tokenYAmount);
        expectedLiquidity += 4e18; // sqrt((2*4)*(2*1))

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), expectedLiquidity);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), expectedLiquidity - MINIMUM_LIQUIDITY);

        assertEq(fixture.tokenX().balanceOf(address(pair)), 3 * tokenXAmount + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 3 * tokenYAmount + depositYAmount);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 3 * tokenXAmount);
        assertEq(_reserveY, 3 * tokenYAmount);
    }

    /**
     * Force min calculation in mint to be wrong by over depositing one asset for mint.
     */
    function testDepositAndOverMintX() public {
        uint256 tokenXAmount = 1e18;
        uint256 tokenYAmount = 4e18;
        uint256 expectedLiquidity = 2e18; // sqrt(4*1)
        uint256 depositYAmount = 50e18;
        uint256 overMintX = 1000;

        // First mint
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, 0, depositYAmount);

        // Second mint
        fixture.mintFor(tester, 2 * tokenXAmount + overMintX, 2 * tokenYAmount);
        expectedLiquidity += 4e18; // sqrt((2*4)*(2*1))

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), expectedLiquidity);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), expectedLiquidity - MINIMUM_LIQUIDITY);

        assertEq(fixture.tokenX().balanceOf(address(pair)), 3 * tokenXAmount + overMintX);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 3 * tokenYAmount + depositYAmount);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 3 * tokenXAmount + overMintX, 'mint() takes overMint overage and adds it to reserves.');
        assertEq(_reserveY, 3 * tokenYAmount, '`reserveY` should match expected.');
    }

    /**
     * Force min calculation in mint to be wrong by over depositing one asset for mint.
     */
    function testDepositAndOverMintY() public {
        uint256 tokenXAmount = 1e18;
        uint256 tokenYAmount = 4e18;
        uint256 expectedLiquidity = 2e18; // sqrt(4*1)
        uint256 depositXAmount = 50e18;
        uint256 overMintY = 1000;

        // First mint
        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        // Second mint
        fixture.mintFor(tester, 2 * tokenXAmount, 2 * tokenYAmount + overMintY);
        expectedLiquidity += 4e18; // sqrt((2*4)*(2*1))

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), expectedLiquidity);
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), expectedLiquidity - MINIMUM_LIQUIDITY);

        assertEq(fixture.tokenX().balanceOf(address(pair)), 3 * tokenXAmount + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 3 * tokenYAmount + overMintY);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 3 * tokenXAmount);
        assertEq(_reserveY, 3 * tokenYAmount + overMintY, 'mint() takes overMint overage and adds it to reserves.');
    }

    function testDepositXAndBurn() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        uint256 depositXAmount = 50e18;
        uint256 expectedLiquidity = 3e18;

        uint256 testerBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 testerBalanceY = fixture.tokenY().balanceOf(tester);

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, 0);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_L).transfer(address(pair), expectedLiquidity - MINIMUM_LIQUIDITY);
        (uint256 outAmountX, uint256 outAmountY) = pair.burn(tester);
        vm.stopPrank();

        assertEq(outAmountX, tokenXAmount - 1000);
        assertEq(outAmountY, tokenYAmount - 1000);

        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 0);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), MINIMUM_LIQUIDITY);
        assertEq(fixture.tokenX().balanceOf(address(pair)), 1000 + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 1000);

        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX - 1000 - depositXAmount);
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY - 1000);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 1000);
        assertEq(_reserveY, 1000);
    }

    function testDepositYAndBurn() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        uint256 depositYAmount = 50e18;
        uint256 expectedLiquidity = 3e18;

        uint256 testerBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 testerBalanceY = fixture.tokenY().balanceOf(tester);

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, 0, depositYAmount);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_L).transfer(address(pair), expectedLiquidity - MINIMUM_LIQUIDITY);
        (uint256 outAmountX, uint256 outAmountY) = pair.burn(tester);
        vm.stopPrank();

        assertEq(outAmountX, tokenXAmount - 1000, 'x out amount matches x minted minus min liquidity');
        assertEq(outAmountY, tokenYAmount - 1000, 'y out amount matches y minted minus min liquidity');

        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 0);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), MINIMUM_LIQUIDITY);
        assertEq(fixture.tokenX().balanceOf(address(pair)), 1000);
        assertEq(fixture.tokenY().balanceOf(address(pair)), 1000 + depositYAmount);
        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX - 1000);
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY - 1000 - depositYAmount);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, 1000);
        assertEq(_reserveY, 1000);
    }

    function testDepositAndSkim() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 4e18;
        uint256 skimX = 5e18;
        uint256 skimY = 6e18;
        uint256 depositXAmount = 50e18;
        uint256 depositYAmount = 50e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        uint256 testerBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 testerBalanceY = fixture.tokenY().balanceOf(tester);

        // add extra tokens to be skimmed.
        vm.startPrank(tester);
        fixture.tokenX().transfer(pairAddress, skimX);
        fixture.tokenY().transfer(pairAddress, skimY);
        vm.stopPrank();

        uint256 tesetr2BalanceX = fixture.tokenX().balanceOf(tester2);
        uint256 tesetr2BalanceY = fixture.tokenY().balanceOf(tester2);

        pair.skim(tester2);

        assertEq(fixture.tokenX().balanceOf(tester2), tesetr2BalanceX + skimX, 'tester2 should have `skimX` amount');
        assertEq(fixture.tokenY().balanceOf(tester2), tesetr2BalanceY + skimY, 'tester2 should have `skimY` amount');

        (uint112 reserveX, uint112 reserveY,) = pair.getReserves();

        assertEq(reserveX, tokenXAmount, 'reserves are not impacted by depositXamount.');
        assertEq(reserveY, tokenYAmount, 'reserves are not impacted by depositYamount.');
        assertEq(
            fixture.tokenX().balanceOf(address(pair)),
            tokenXAmount + depositXAmount,
            'Pair has X liquidity and deposit.'
        );
        assertEq(
            fixture.tokenY().balanceOf(address(pair)),
            tokenYAmount + depositYAmount,
            'Pair has Y liquidity and deposit.'
        );
        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX - skimX, 'tester has balanceX minus skimX');
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY - skimY, 'tester has balanceY minus skimY');
    }

    function testDepositAndSync() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 4e18;
        uint256 syncX = 2e18;
        uint256 syncY = 1e18;
        uint256 depositXAmount = 50e18;
        uint256 depositYAmount = 50e18;

        addLiquidityAndDeposit(tester, tokenXAmount, tokenYAmount, depositXAmount, depositYAmount);

        uint256 beforeSyncX = fixture.tokenX().balanceOf(tester);
        uint256 beforeSyncY = fixture.tokenY().balanceOf(tester);

        // add extra tokens to be skimmed.
        vm.startPrank(tester);
        fixture.tokenX().transfer(address(pair), syncX);
        fixture.tokenY().transfer(address(pair), syncY);
        vm.stopPrank();

        pair.sync();
        (uint112 reserveX, uint112 reserveY,) = pair.getReserves();

        assertEq(reserveX, tokenXAmount + syncX, '`reserveX` should include `syncX`');
        assertEq(reserveY, tokenYAmount + syncY, '`reserveY` should include `syncY`');
        assertEq(
            fixture.tokenX().balanceOf(address(pair)),
            tokenXAmount + syncX + depositXAmount,
            'pair should have X liquidity, deposit, and sync'
        );
        assertEq(
            fixture.tokenY().balanceOf(address(pair)),
            tokenYAmount + syncY + depositYAmount,
            'pair should have Y liquidity, deposit, and sync'
        );
        assertEq(fixture.tokenX().balanceOf(tester), beforeSyncX - syncX, 'tester should have beforeSyncX minus syncX');
        assertEq(fixture.tokenY().balanceOf(tester), beforeSyncY - syncY, 'tester should have beforeSyncY minus syncY');
    }

    function testDepositReentryLock() public {
        uint256 swapAmount = 50e18;
        uint256 tokenXAmount = 5e18;
        uint256 tokenYAmount = 10e18;
        uint256 depositXAmount = 50e18;

        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        ReentryAttackReciever attacker = new ReentryAttackReciever();

        // approve transfer of tokens for attack to attacker.
        vm.startPrank(tester);
        fixture.tokenX().approve(address(attacker), depositXAmount);
        fixture.tokenX().transfer(address(attacker), depositXAmount);
        vm.stopPrank();

        uint256 expectedSwap = computeExpectedSwapOutAmount(swapAmount, tokenXAmount, tokenYAmount);

        // When ERC-1155 was removed, this specific reentry attack is no longer possible.
        attacker.attack(pairAddress, swapAmount, expectedSwap);

        assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount + depositXAmount);
        assertEq(fixture.tokenY().balanceOf(address(pair)), tokenYAmount);
        assertEq(fixture.tokenX().balanceOf(address(attacker)), 0);
        assertEq(fixture.tokenY().balanceOf(address(attacker)), 0);
    }

    function testDepositXOverflow() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositXAmount = uint256(type(uint112).max) + 1;

        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        vm.startPrank(tester);
        fixture.transferTokensTo(address(pair), depositXAmount, 0);
        vm.expectRevert(AmmalgamPair.Overflow.selector);
        pair.deposit(tester);
        vm.stopPrank();
    }

    function testDepositYOverflow() public {
        uint256 tokenXAmount = 1000e18;
        uint256 tokenYAmount = 1000e18;
        uint256 depositYAmount = uint256(type(uint112).max) + 1;

        fixture.mintFor(tester, tokenXAmount, tokenYAmount);
        fixture.transferTokensTo(address(pair), 0, depositYAmount);
        vm.expectRevert(AmmalgamPair.Overflow.selector);
        pair.deposit(tester);
    }

    function addLiquidityAndDeposit(
        address _to,
        uint256 _tokenXAmount,
        uint256 _tokenYAmount,
        uint256 _depositXAmount,
        uint256 _depositYAmount
    ) private {
        fixture.mintForAndInitializeBlocks(_to, _tokenXAmount, _tokenYAmount);
        fixture.depositFor(_to, _depositXAmount, _depositYAmount);
    }
}

contract ReentryAttackReciever is IERC1155Receiver, Test {
    IAmmalgamPair private pair;
    uint256 private swapOut;

    function attack(address _pairAddress, uint256 _swapIn, uint256 _swapOut) external {
        pair = IAmmalgamPair(_pairAddress);

        (IERC20 tokenX,) = pair.underlyingTokens();

        swapOut = _swapOut;

        tokenX.transfer(_pairAddress, _swapIn);

        pair.deposit(address(this));
    }

    function supportsInterface(
        bytes4 /* interfaceId */
    ) public view virtual override returns (bool) {
        revert('this implementation is not used for test but required to implement in ERC165');
    }

    function onERC1155Received(
        address, // operator
        address, // from
        uint256, // id
        uint256, // value
        bytes calldata // data
    ) external returns (bytes4) {
        pair.swap(0, swapOut, address(this), '');

        return this.onERC1155Received.selector;
    }

    function onERC1155BatchReceived(
        address, // operator
        address, // from
        uint256[] calldata, // ids
        uint256[] calldata, // values
        bytes calldata // data
    ) external pure returns (bytes4) {
        return this.onERC1155BatchReceived.selector;
    }
}
