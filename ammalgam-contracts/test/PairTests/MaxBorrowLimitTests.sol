// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract MaxBorrowLimitTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random = vm.addr(1111);
    address private random2 = vm.addr(1112);
    address private tester = vm.addr(1113);
    address private tester2 = vm.addr(1114);

    uint256 private constant initialX = 8e18;
    uint256 private constant initialY = 2e18;
    uint256 private constant initialLiquidity = 4e18;
    uint256 private constant initialXPerY = initialX / initialY;
    uint256 private constant largeDeposit = 15e18;
    uint256 private constant largerDeposit = 100e18;

    FactoryPairTestFixture private fixture;

    uint256 private constant MAX_BORROW_PERCENTAGE = 90;

    function setUp() public {
        random = vm.addr(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        // Add initial liquidity
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);

        vm.prank(address(fixture));
        pair.updateExternalLiquidity(4_000_000e18);
    }

    function testMaxBorrow() public {
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100;
        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL);
    }

    function testMaxBorrowAfterBorrowingLz() public {
        uint256 randomBorrowL = 1e18;

        createDepositToRandomAddresses(0, largeDeposit);
        fixture.borrowLiquidityFor(random, randomBorrowL);

        uint256 maxBorrowX =
            (initialX * MAX_BORROW_PERCENTAGE) / 100 - Math.mulDiv(randomBorrowL, initialX, initialLiquidity);
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit
            - Math.mulDiv(randomBorrowL, initialY, initialLiquidity);
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowL;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL);
    }

    function testMaxBorrowAfterBorrowingLX() public {
        uint256 randomBorrowL = 1e18;
        uint256 randomBorrowX = 1e18;

        createDepositToRandomAddresses(0, largeDeposit);
        fixture.borrowLiquidityFor(random, randomBorrowL);
        createDebtToRandomAddress(randomBorrowX, 0);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowX
            - Math.mulDiv(randomBorrowL, initialX, initialLiquidity);
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit
            - Math.mulDiv(randomBorrowL, initialY, initialLiquidity);
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowL
            - (randomBorrowX * initialLiquidity) / initialX;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL, true);
    }

    function testMaxBorrowAfterBorrowingLY() public {
        uint256 randomBorrowL = 1e18;
        uint256 randomBorrowY = 1e18;

        createDepositToRandomAddresses(largeDeposit, 0);
        fixture.borrowLiquidityFor(random2, randomBorrowL);
        createDebtToRandomAddress(0, randomBorrowY);
        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit
            - Math.mulDiv(randomBorrowL, initialX, initialLiquidity);
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowY
            - Math.mulDiv(randomBorrowL, initialY, initialLiquidity);
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowL
            - (randomBorrowY * initialLiquidity) / initialY;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL);
    }

    function testMaxBorrowAfterBorrowingLXY() public {
        uint256 randomBorrowL = 1e18;
        uint256 randomBorrowX = 1e18;
        uint256 randomBorrowY = 1e18;

        createDepositToRandomAddresses(largeDeposit, largeDeposit);
        fixture.borrowLiquidityFor(random, randomBorrowL);
        createDebtToRandomAddress(randomBorrowX, randomBorrowY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit - randomBorrowX
            - Math.mulDiv(randomBorrowL, initialX, initialLiquidity);
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit - randomBorrowY
            - Math.mulDiv(randomBorrowL, initialY, initialLiquidity);
        // Deposits exceed borrow, liquidity is thus max borrow of L minus borrowed L
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowL;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL);
    }

    function testMaxBorrowAfterBorrowingX() public {
        uint256 randomBorrowedX = 1e18;

        // Initialize pair with both a debt and deposit of x
        createDepositToRandomAddresses(0, largeDeposit);
        createDebtToRandomAddress(randomBorrowedX, 0);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedX;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit;
        uint256 maxBorrowL =
            (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - (randomBorrowedX * initialLiquidity) / initialX;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL, true);
    }

    function testMaxBorrowAfterBorrowingY() public {
        uint256 randomBorrowedY = 1e18;

        // Initialize pair with both a debt and deposit of y
        createDepositToRandomAddresses(largeDeposit, 0);
        createDebtToRandomAddress(0, randomBorrowedY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedY;
        uint256 maxBorrowL =
            (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - (randomBorrowedY * initialLiquidity) / initialY;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL);
    }

    function testMaxBorrowAfterBorrowingXY() public {
        uint256 randomBorrowedX = 1e18;
        uint256 randomBorrowedY = 1e18;

        // Initialize pair with both a debt and deposit of x and y
        createDepositToRandomAddresses(largeDeposit, largeDeposit);
        createDebtToRandomAddress(randomBorrowedX, randomBorrowedY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit - randomBorrowedX;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 + largeDeposit - randomBorrowedY;
        // Deposits exceed borrow, liquidity is thus max borrow of L minus borrowed L
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100;

        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL);
    }

    function testMaxBorrowCheckBeforeCallback() public {
        uint256 randomBorrowX = 7.2e18;

        createDepositToRandomAddresses(0, 3e18);
        createDebtToRandomAddress(randomBorrowX, 0);

        fixture.transferTokensTo(tester, 0, 3e18);
        fixture.depositFor(tester, 0, 3e18);

        FailAtCallback callback = new FailAtCallback();
        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        pair.borrow(address(callback), initialX - randomBorrowX, 0, '0x1');
        vm.stopPrank();
    }

    function testMaxBorrowLCheckBeforeCallback() public {
        uint256 randomBorrowL = 3.6e18;

        createDepositToRandomAddresses(0, 6e18);
        fixture.borrowLiquidityFor(random, randomBorrowL);

        fixture.transferTokensTo(tester, 0, 3e18);
        fixture.depositFor(tester, 0, 3e18);

        FailAtCallback callback = new FailAtCallback();
        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        pair.borrowLiquidity(address(callback), initialLiquidity - randomBorrowL, '0x1');
        vm.stopPrank();
    }

    function testMaxBorrowNoDeposits() public {
        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100;
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100;

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, maxBorrowX + 1, 0);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, 0, maxBorrowY + 1);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(random, maxBorrowL + 1);

        fixture.borrowFor(random, maxBorrowX, 0);
        fixture.repayFor(random, maxBorrowX, 0);
        fixture.borrowFor(random, 0, maxBorrowY);
        fixture.repayFor(random, 0, maxBorrowY);
        fixture.borrowLiquidityFor(random, maxBorrowL);
    }

    function testMaxBorrowNoDepositsBorrowX() public {
        uint256 randomBorrowedX = 4e18;

        // Initialize pair with both a debt of x and y
        fixture.borrowFor(random, randomBorrowedX, 0);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedX;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100;
        uint256 maxBorrowL =
            (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - (randomBorrowedX * initialLiquidity) / initialX;

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, maxBorrowX + 1, 0);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, 0, maxBorrowY + 1);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(random, maxBorrowL + 1);

        fixture.borrowFor(random, maxBorrowX, 0);
        fixture.repayFor(random, maxBorrowX, 0);
        fixture.borrowFor(random, 0, maxBorrowY);
        fixture.repayFor(random, 0, maxBorrowY);
        fixture.borrowLiquidityFor(random, maxBorrowL);
    }

    function testMaxBorrowNoDepositsBorrowY() public {
        uint256 randomBorrowedY = 1e18;

        // Initialize pair with both a debt of x and y
        fixture.borrowFor(random, 0, randomBorrowedY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedY;
        uint256 maxBorrowL =
            (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100 - (randomBorrowedY * initialLiquidity) / initialY;

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, maxBorrowX + 1, 0);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, 0, maxBorrowY + 1);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(random, maxBorrowL + 1);

        fixture.borrowFor(random, maxBorrowX, 0);
        fixture.repayFor(random, maxBorrowX, 0);
        fixture.borrowFor(random, 0, maxBorrowY);
        fixture.repayFor(random, 0, maxBorrowY);
        fixture.borrowLiquidityFor(random, maxBorrowL);
    }

    function testMaxBorrowNoDepositsBorrowXAndY() public {
        uint256 randomBorrowedX = 4e18;
        uint256 randomBorrowedY = 1e18;

        // Initialize pair with both a debt and deposit of x and y
        fixture.borrowFor(random, randomBorrowedX, 0);
        fixture.borrowFor(random, 0, randomBorrowedY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedX;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedY;
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100
            - Math.max((randomBorrowedX * initialLiquidity) / initialX, (randomBorrowedY * initialLiquidity) / initialY);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, maxBorrowX + 1, 0);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, 0, maxBorrowY + 1);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(random, maxBorrowL + 1);

        fixture.borrowFor(random, maxBorrowX, 0);
        fixture.repayFor(random, maxBorrowX, 0);
        fixture.borrowFor(random, 0, maxBorrowY);
        fixture.repayFor(random, 0, maxBorrowY);
        fixture.borrowLiquidityFor(random, maxBorrowL);
    }

    function testMaxBorrowNoDepositsBorrowXBiggerAndY() public {
        uint256 randomBorrowedX = 5e18;
        uint256 randomBorrowedY = 1e18;

        // Initialize pair with both a debt and deposit of x and y
        fixture.borrowFor(random, randomBorrowedX, 0);
        fixture.borrowFor(random, 0, randomBorrowedY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedX;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedY;
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100
            - Math.max((randomBorrowedX * initialLiquidity) / initialX, (randomBorrowedY * initialLiquidity) / initialY);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, maxBorrowX + 1, 0);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, 0, maxBorrowY + 1);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(random, maxBorrowL + 1);

        fixture.borrowFor(random, maxBorrowX, 0);
        fixture.repayFor(random, maxBorrowX, 0);
        fixture.borrowFor(random, 0, maxBorrowY);
        fixture.repayFor(random, 0, maxBorrowY);
        fixture.borrowLiquidityFor(random, maxBorrowL);
    }

    function testMaxBorrowNoDepositsBorrowXAndYBigger() public {
        uint256 randomBorrowedX = 3e18;
        uint256 randomBorrowedY = 1e18;

        // Initialize pair with both a debt and deposit of x and y
        fixture.borrowFor(random, randomBorrowedX, 0);
        fixture.borrowFor(random, 0, randomBorrowedY);

        uint256 maxBorrowX = (initialX * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedX;
        uint256 maxBorrowY = (initialY * MAX_BORROW_PERCENTAGE) / 100 - randomBorrowedY;
        uint256 maxBorrowL = (initialLiquidity * MAX_BORROW_PERCENTAGE) / 100
            - Math.max((randomBorrowedX * initialLiquidity) / initialX, (randomBorrowedY * initialLiquidity) / initialY);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, maxBorrowX + 1, 0);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(random, 0, maxBorrowY + 1);
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(random, maxBorrowL + 1);

        fixture.borrowFor(random, maxBorrowX, 0);
        fixture.repayFor(random, maxBorrowX, 0);
        fixture.borrowFor(random, 0, maxBorrowY);
        fixture.repayFor(random, 0, maxBorrowY);
        fixture.borrowLiquidityFor(random, maxBorrowL);
    }

    function exerciseMaxBorrow(
        uint256 maxBorrowX,
        uint256 maxBorrowY,
        uint256 maxBorrowL,
        bool depositYForLBorrow
    ) private {
        fixture.transferTokensTo(tester, largerDeposit, largerDeposit);
        fixture.transferTokensTo(tester2, largerDeposit, largerDeposit);
        storePairState();
        verifyMaxBorrowX(maxBorrowX);
        verifyPairAtPriorState();
        verifyMaxBorrowY(maxBorrowY);
        verifyPairAtPriorState();
        verifyMaxBorrowL(maxBorrowL, depositYForLBorrow);
    }

    function exerciseMaxBorrow(uint256 maxBorrowX, uint256 maxBorrowY, uint256 maxBorrowL) private {
        exerciseMaxBorrow(maxBorrowX, maxBorrowY, maxBorrowL, false);
    }

    function verifyMaxBorrowX(
        uint256 maxBorrowX
    ) private {
        // deposit large amount collateral for tester.
        fixture.depositFor(tester, 0, largerDeposit);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(tester, maxBorrowX + 1, 0);

        // Works at the max limit.
        fixture.borrowFor(tester, maxBorrowX, 0);

        // Restore pair and tester to prior state
        fixture.repayFor(tester, maxBorrowX, 0);
        fixture.withdrawFor(tester, 0, largerDeposit);
    }

    function verifyMaxBorrowY(
        uint256 maxBorrowY
    ) private {
        // deposit large amount collateral for tester.
        fixture.depositFor(tester, largerDeposit, 0);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowForNoEvent(tester, 0, maxBorrowY + 1);

        // Works at the max limit.
        fixture.borrowFor(tester, 0, maxBorrowY);

        // Restore pair and tester to prior state
        fixture.repayFor(tester, 0, maxBorrowY);
        fixture.withdrawFor(tester, largerDeposit, 0);
    }

    function verifyMaxBorrowL(uint256 maxBorrowL, bool depositYForLBorrow) private {
        // deposit large amount collateral for tester.
        if (depositYForLBorrow) {
            fixture.depositFor(tester, 0, largerDeposit);
        } else {
            fixture.depositFor(tester, largerDeposit, 0);
        }

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.borrowLiquidityForNoEvent(tester, maxBorrowL + 1);

        // Works at the max limit.
        (uint256 lx2, uint256 ly2) = fixture.borrowLiquidityFor(tester, maxBorrowL);

        // Restore pair and tester to prior state
        fixture.repayLiquidityFor(tester, lx2, ly2, maxBorrowL);
        if (depositYForLBorrow) {
            fixture.withdrawFor(tester, 0, largeDeposit);
        } else {
            fixture.withdrawFor(tester, largeDeposit, 0);
        }
    }

    FactoryPairTestFixture.ExpectedPairState private expectedPairState;

    function storePairState() private {
        (uint112 reserveX, uint112 reserveY,) = fixture.pair().getReserves();
        expectedPairState.reserveXAssets = reserveX;
        expectedPairState.reserveYAssets = reserveY;
        expectedPairState.tokenX = fixture.tokenX().balanceOf(fixture.pairAddress());
        expectedPairState.tokenY = fixture.tokenY().balanceOf(fixture.pairAddress());
    }

    function verifyPairAtPriorState() private view {
        fixture.verifyPair(expectedPairState);
    }

    function createDepositToRandomAddresses(uint256 amountX, uint256 amountY) private {
        if (amountX > 0) {
            fixture.transferTokensTo(random2, amountX, 0);
            fixture.depositFor(random2, amountX, 0);
        }
        if (amountY > 0) {
            fixture.transferTokensTo(random, 0, amountY);
            fixture.depositFor(random, 0, amountY);
        }
    }

    function createDebtToRandomAddress(uint256 amountX, uint256 amountY) private {
        if (amountX > 0) {
            fixture.borrowFor(random, amountX, 0);
        }
        if (amountY > 0) {
            fixture.borrowFor(random2, 0, amountY);
        }
    }
}

contract FailAtCallback is ICallback {
    function ammalgamSwapCallV1(address sender, uint256 amountX, uint256 amountY, bytes calldata data) public {} // noop

    function ammalgamBorrowCallV1(
        address, // sender
        uint256, // amountXAssets,
        uint256, // amountYAssets,
        uint256, // amountXShares,
        uint256, // amountYShares,
        bytes calldata // data
    ) public pure {
        require(false, 'Fail test if callback is reached');
    }

    function ammalgamBorrowLiquidityCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) external {} //noop

    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external { /* noop */ }
}
