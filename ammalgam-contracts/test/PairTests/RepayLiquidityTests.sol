// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {BORROW_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract RepayLiquidityTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;
    address private tester3;

    FactoryPairTestFixture private fixture;

    mapping(address => uint256) private initialBalanceX;
    mapping(address => uint256) private initialBalanceY;

    struct SlotVars {
        uint256 initialMintX;
        uint256 initialMintY;
        uint256 mintX;
        uint256 mintY;
        uint256 borrowX;
        uint256 borrowY;
        uint256 borrowL;
        uint256 depositX;
        uint256 depositY;
        uint256 repayX;
        uint256 repayY;
        uint256 repayL;
    }

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);
        tester3 = vm.addr(1113);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        initialBalanceX[tester] = 5000e18;
        initialBalanceY[tester] = 5000e18;
        initialBalanceX[tester2] = 5000e18;
        initialBalanceY[tester2] = 5000e18;
        initialBalanceX[tester3] = 5000e18;
        initialBalanceY[tester3] = 5000e18;

        fixture.transferTokensTo(tester, initialBalanceX[tester], initialBalanceX[tester]).transferTokensTo(
            tester2, initialBalanceX[tester2], initialBalanceX[tester2]
        ).transferTokensTo(tester3, initialBalanceX[tester3], initialBalanceX[tester3]);
    }

    function testRepayLiquidityInFull() public {
        SlotVars memory slot;

        slot.initialMintX = 20e18;
        slot.initialMintY = 80e18;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.borrowL = 1e18;
        slot.repayL = 1e18;

        verifyRepayLiquidity(tester, slot);
    }

    function testPartialRepayLiquidity() public {
        SlotVars memory slot;

        slot.initialMintX = 20e18;
        slot.initialMintY = 80e18;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.borrowL = 1e18;
        slot.repayL = slot.borrowL / 2;

        verifyRepayLiquidity(tester, slot);
    }

    function testRepayLiquidityAndSwap() public {
        SlotVars memory slot;

        slot.initialMintX = 20e18;
        slot.initialMintY = 80e18;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.borrowL = 1e18;
        slot.repayL = 1e18;

        initialDepositAndBorrowL(tester, slot);

        uint256 swapAmount = 1e18;
        uint256 reserveAfterBorrowX = slot.initialMintX + slot.mintX - slot.borrowX;
        uint256 reserveAfterBorrowY = slot.initialMintY + slot.mintY - slot.borrowY;
        (uint256 amountOut,,) = fixture.verifySwapXToY(tester, swapAmount, reserveAfterBorrowX, reserveAfterBorrowY);

        // recalculate borrowLX/LY after swap
        (slot.repayX, slot.repayY) = fixture.computeAmountsForRepayLiquidity(slot.repayL);

        fixture.repayLiquidityFor(tester, slot.repayX, slot.repayY, slot.repayL);

        // after swap the pool balance has been changed.
        slot.mintX += swapAmount;
        slot.mintY -= amountOut;

        assertionRepayLiquidity(tester, slot);
    }

    function testRepayLiquidityAndMint() public {
        SlotVars memory slot;

        slot.initialMintX = 20e18;
        slot.initialMintY = 80e18;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.borrowL = 1e18;
        slot.repayL = 1e18;

        initialDepositAndBorrowL(tester, slot);

        uint256 newMintX = 2e18;
        uint256 newMintY = 9e18; // try more Y to break the current XY pool ratio to gen volatility in pool
        fixture.mintFor(tester, newMintX, newMintY);

        // recalculate borrowLX/LY after new mint
        (slot.repayX, slot.repayY) = fixture.computeAmountsForRepayLiquidity(slot.repayL);

        fixture.repayLiquidityFor(tester, slot.repayX, slot.repayY, slot.repayL);

        // after swap the pool balance has been changed.
        slot.mintX += newMintX;
        slot.mintY += newMintY;

        assertionRepayLiquidity(tester, slot);
    }

    function verifyRepayLiquidity(address _tester, SlotVars memory slot) private {
        initialDepositAndBorrowL(_tester, slot);

        (slot.repayX, slot.repayY) = fixture.computeAmountsForRepayLiquidity(slot.repayL);

        fixture.repayLiquidityFor(_tester, slot.repayX, slot.repayY, slot.repayL);

        assertionRepayLiquidity(_tester, slot);
    }

    function initialDepositAndBorrowL(address _tester, SlotVars memory slot) private {
        // Initialize liquidity using a random address with 10x the needed liquidity.
        address random = vm.addr(123);
        fixture.transferTokensTo(random, slot.initialMintX, slot.initialMintY);
        fixture.mintForAndInitializeBlocks(random, slot.initialMintX, slot.initialMintY);

        fixture.mintFor(_tester, slot.mintX, slot.mintY);

        (uint256 returnedLx, uint256 returnedLy) = fixture.borrowLiquidityFor(_tester, slot.borrowL);

        (slot.borrowX, slot.borrowY) = fixture.computeAmountsForRepayLiquidity(slot.borrowL);

        assertEq(returnedLx, slot.borrowX, 'returnedLx matches computed repay');
        assertEq(returnedLy, slot.borrowY, 'returnedLy matches computed repay');
    }

    function assertionRepayLiquidity(address _tester, SlotVars memory slot) private view {
        uint256 testerBorrowedLiquidity = slot.borrowL - slot.repayL;

        uint256 pairBalanceX = slot.initialMintX + slot.mintX + slot.repayX + slot.depositX - slot.borrowX;
        uint256 pairBalanceY = slot.initialMintY + slot.mintY + slot.repayY + slot.depositY - slot.borrowY;

        uint256 testerBalanceX = initialBalanceX[_tester] + slot.borrowX - slot.mintX - slot.repayX - slot.depositX;
        uint256 testerBalanceY = initialBalanceY[_tester] + slot.borrowY - slot.mintY - slot.repayY - slot.depositY;

        assertEq(
            pair.tokens(BORROW_L).balanceOf(_tester), testerBorrowedLiquidity, 'Balance of borrowed liquidity of tester'
        );

        assertEq(fixture.tokenX().balanceOf(address(pair)), pairBalanceX, 'Balance of token X in pair');
        assertEq(fixture.tokenY().balanceOf(address(pair)), pairBalanceY, 'Balance of token Y in pair');

        assertEq(fixture.tokenX().balanceOf(_tester), testerBalanceX, 'Balance of token X in tester');
        assertEq(fixture.tokenY().balanceOf(_tester), testerBalanceY, 'Balance of token Y in tester');

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(pairBalanceX, _reserveX, 'Assert pair balanceX eq reserveX');
        assertEq(pairBalanceY, _reserveY, 'Assert pair balanceY eq reserveY');
    }

    function testRevertInsufficientRepayLiquidity() public {
        SlotVars memory slot;

        slot.initialMintX = 20e18;
        slot.initialMintY = 80e18;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.borrowL = 1e18;
        slot.repayL = 1e18;

        initialDepositAndBorrowL(tester, slot);

        (slot.repayX, slot.repayY) = fixture.computeAmountsForRepayLiquidity(slot.repayL);

        // transfer 0 for X amount to revert
        fixture.transferTokensTo(address(pair), 0, slot.repayY);
        vm.expectRevert(AmmalgamPair.InsufficientRepayLiquidity.selector);
        pair.repayLiquidity(tester);
    }

    function testRepayLiquidityOverpaid() public {
        SlotVars memory slot;

        slot.initialMintX = 20e18;
        slot.initialMintY = 80e18;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.borrowL = 1e18;
        slot.repayL = 1e18;

        initialDepositAndBorrowL(tester, slot);

        (slot.repayX, slot.repayY) = fixture.computeAmountsForRepayLiquidity(slot.repayL);

        slot.repayX += 100;
        slot.repayY += 100;

        uint256 reserveX = slot.initialMintX + slot.mintX - slot.borrowX;
        uint256 reserveY = slot.initialMintY + slot.mintY - slot.borrowY;
        uint256 activeLiquidityAssets = Math.sqrt(reserveX * reserveY);
        uint256 calcRepayL =
            Math.min(slot.repayX * activeLiquidityAssets / reserveX, slot.repayY * activeLiquidityAssets / reserveY);

        assertGt(calcRepayL, slot.repayL, 'make sure calcRepayL > slot.repayL to be overpaid');
        fixture.repayLiquidityFor(tester, slot.repayX, slot.repayY, calcRepayL);

        assertionRepayLiquidity(tester, slot);
    }
}
