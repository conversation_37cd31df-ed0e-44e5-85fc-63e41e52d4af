// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {MAG2} from 'contracts/libraries/constants.sol';
import {Convert} from 'contracts/libraries/Convert.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {DEPOSIT_L, ROUNDING_UP} from 'contracts/interfaces/tokens/ITokenController.sol';

contract MaxBurnLimitTests is Test {
    uint256 private constant MAX_BORROW_PERCENTAGE = 90;

    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random = vm.addr(1111);
    address private tester = vm.addr(1113);
    address private tester2 = vm.addr(1114);

    uint256 private constant initialX = 8e18;
    uint256 private constant initialY = 2e18;
    uint256 private constant initialLiquidity = 4e18;
    uint256 private constant sqrtPrice = 2; // Math.sqrt(initialX / initialY);

    FactoryPairTestFixture private fixture;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        // Add initial liquidity
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);

        vm.prank(address(fixture));
        pair.updateExternalLiquidity(4_000_000e18);
    }

    function testBurnLValidateMaxBorrowYLimits() public {
        uint256 collateralAmountX = 16e18;
        fixture.transferTokensTo(tester, collateralAmountX, 0);
        fixture.depositFor(tester, collateralAmountX, 0);

        uint256 borrowSomeY = 0.5e18;
        fixture.borrowForNoEvent(tester, 0, borrowSomeY);

        uint256 newReserveYAfterBorrowFromLY = initialY - borrowSomeY;
        uint256 activeLiquidityAssets = newReserveYAfterBorrowFromLY * sqrtPrice;

        uint256 totalBorrowedLAssets = borrowSomeY * activeLiquidityAssets / newReserveYAfterBorrowFromLY;

        uint256 maxAllowedBurnL = initialLiquidity - totalBorrowedLAssets * MAG2 / MAX_BORROW_PERCENTAGE;

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.burnFor(random, maxAllowedBurnL + 1);

        fixture.burnFor(random, maxAllowedBurnL);
    }

    function testBurnLValidateMaxBorrowXLimits() public {
        uint256 collateralAmountY = 16e18;
        fixture.transferTokensTo(tester, 0, collateralAmountY);
        fixture.depositFor(tester, 0, collateralAmountY);

        uint256 borrowSomeX = 2.2e18;
        fixture.borrowForNoEvent(tester, borrowSomeX, 0);

        uint256 newReserveXAfterBorrowFromLX = initialX - borrowSomeX;
        uint256 activeLiquidityAssets = newReserveXAfterBorrowFromLX / sqrtPrice;

        uint256 totalBorrowedLAssets = borrowSomeX * activeLiquidityAssets / newReserveXAfterBorrowFromLX;

        uint256 maxAllowedBurnL = initialLiquidity - Math.ceilDiv(totalBorrowedLAssets * MAG2, MAX_BORROW_PERCENTAGE);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.burnFor(random, maxAllowedBurnL + 1);

        fixture.burnFor(random, maxAllowedBurnL);
    }

    function testBurnLValidateMaxBorrowLiquidityLimits() public {
        uint256 collateralAmountX = 8e18;
        uint256 collateralAmountY = 2e18;
        fixture.transferTokensTo(tester, collateralAmountX, collateralAmountY);
        fixture.depositFor(tester, collateralAmountX, collateralAmountY);

        uint256 borrowedLiquidity = 2e18;
        fixture.borrowLiquidityFor(tester, borrowedLiquidity);

        uint256 maxAllowedBurnL = initialLiquidity - Math.ceilDiv(borrowedLiquidity * MAG2, MAX_BORROW_PERCENTAGE);

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.burnFor(random, maxAllowedBurnL + 1);

        fixture.burnFor(random, maxAllowedBurnL);
    }

    function testWithdrawXValidateMaxBorrowXLimits() public {
        uint256 depositAmountY = 800e18;
        fixture.transferTokensTo(tester, 0, depositAmountY);
        fixture.depositFor(tester, 0, depositAmountY);

        uint256 depositedX = 80e18;
        fixture.transferTokensTo(tester2, depositedX, 0);
        fixture.depositFor(tester2, depositedX, 0);

        uint256 borrowedX = 50e18;
        fixture.borrowForNoEvent(tester, borrowedX, 0);

        uint256 maxWithdrawX = initialX * MAX_BORROW_PERCENTAGE / MAG2 + depositedX - borrowedX;

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.withdrawForNoEvent(tester2, maxWithdrawX + 1, 0);

        fixture.withdrawForNoEvent(tester2, maxWithdrawX, 0);
    }

    function testWithdrawYValidateMaxBorrowYLimits() public {
        uint256 depositAmountX = 800e18;
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        uint256 depositedY = 80e18;
        fixture.transferTokensTo(tester2, 0, depositedY);
        fixture.depositFor(tester2, 0, depositedY);

        uint256 borrowedY = 50e18;
        fixture.borrowForNoEvent(tester, 0, borrowedY);

        uint256 maxWithdrawY = initialY * MAX_BORROW_PERCENTAGE / MAG2 + depositedY - borrowedY;

        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.withdrawForNoEvent(tester2, 0, maxWithdrawY + 1);

        fixture.withdrawForNoEvent(tester2, 0, maxWithdrawY);
    }

    function testBurnLAfterMaxBorrowX() public {
        uint256 depositAmountY = 8e18; // random number for a large collateral to cover the max borrow
        fixture.transferTokensTo(tester, 0, depositAmountY);
        fixture.depositFor(tester, 0, depositAmountY);

        uint256 totalLiquidityAssets = initialLiquidity;
        uint256 borrowedAssets = 0;
        uint256 borrowedLiquidityAssets = 0;
        uint256 depositedAssets = 0;

        uint256 maxBorrowX =
            computeMaxBorrowXY(depositedAssets, borrowedAssets, initialX, totalLiquidityAssets, borrowedLiquidityAssets);
        fixture.borrowForNoEvent(tester, maxBorrowX, 0);

        uint256 burnMinimumLiquidity = 2;
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.burnFor(random, burnMinimumLiquidity);
    }

    function testBurnLAfterMaxBorrowY() public {
        uint256 depositAmountX = 16e18; // random number for a large collateral to cover the max borrow
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        uint256 totalLiquidityAssets = initialLiquidity;
        uint256 borrowedAssets = 0;
        uint256 borrowedLiquidityAssets = 0;
        uint256 depositedAssets = 0;

        uint256 maxBorrowY =
            computeMaxBorrowXY(depositedAssets, borrowedAssets, initialY, totalLiquidityAssets, borrowedLiquidityAssets);
        fixture.borrowForNoEvent(tester, 0, maxBorrowY);

        uint256 burnMinimumLiquidity = 2; // 2 to avoid the check of
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.burnFor(random, burnMinimumLiquidity);
    }

    function testWithdrawXAfterMaxBorrowX() public {
        uint256 depositAmountY = 800e18; // random number for a large collateral to cover the max borrow
        fixture.transferTokensTo(tester, 0, depositAmountY);
        fixture.depositFor(tester, 0, depositAmountY);

        // Deposit X for tester2, with an amount that does not exceed the max LTV for the borrower tester
        uint256 depositXForTester2 = 80e18;
        fixture.transferTokensTo(tester2, depositXForTester2, 0);
        fixture.depositFor(tester2, depositXForTester2, 0);

        uint256 borrowedAssets = 0;
        uint256 borrowedLiquidityAssets = 0;

        uint256 maxBorrowX =
            computeMaxBorrowXY(depositXForTester2, borrowedAssets, initialX, initialLiquidity, borrowedLiquidityAssets);
        fixture.borrowForNoEvent(tester, maxBorrowX, 0);

        uint256 withdrawMinmumX = 1;
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.withdrawForNoEvent(tester2, withdrawMinmumX, 0);
    }

    function testWithdrawYAfterMaxBorrowY() public {
        uint256 depositAmountX = 800e18; // random number for a large collateral to cover the max borrow
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        // Deposit Y for tester2, with an amount that does not exceed the max LTV for the borrower tester
        uint256 depositYForTester2 = 80e18;
        fixture.transferTokensTo(tester2, 0, depositYForTester2);
        fixture.depositFor(tester2, 0, depositYForTester2);

        uint256 borrowedAssets = 0;
        uint256 borrowedLiquidityAssets = 0;
        uint256 maxBorrowY =
            computeMaxBorrowXY(depositYForTester2, borrowedAssets, initialY, initialLiquidity, borrowedLiquidityAssets);
        fixture.borrowForNoEvent(tester, 0, maxBorrowY);

        uint256 withdrawMinimumY = 1;
        vm.expectRevert(Validation.AmmalgamMaxBorrowReached.selector);
        fixture.withdrawForNoEvent(tester2, 0, withdrawMinimumY);
    }

    function computeMaxBorrowXY(
        uint256 depositedAssets,
        uint256 borrowedAssets,
        uint256 reserve,
        uint256 totalLiquidityAssets,
        uint256 borrowedLiquidityAssets
    ) internal pure returns (uint256) {
        uint256 scaledBorrowedLiquidityAssets =
            (reserve * borrowedLiquidityAssets) / (totalLiquidityAssets - borrowedLiquidityAssets);

        uint256 amount = (reserve + scaledBorrowedLiquidityAssets) * MAX_BORROW_PERCENTAGE / MAG2 + depositedAssets
            - (scaledBorrowedLiquidityAssets + borrowedAssets);

        return amount;
    }
}
