// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {
    FactoryPairTestFixture,
    IPairHarness,
    StubSaturationAndGeometricTWAPState,
    MAX_TOKEN
} from 'test/shared/FactoryPairTestFixture.sol';
import {computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';
import {
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y,
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    ROUNDING_UP
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {
    Q56,
    Q72,
    Q128,
    Q144,
    <PERSON>IP<PERSON>,
    ALLOWED_LIQUIDITY_LEVERAGE,
    MAG1,
    MAG2,
    LTVMAX_IN_MAG2,
    TRANCHE_B_IN_Q72
} from 'contracts/libraries/constants.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {Liquidation} from 'contracts/libraries/Liquidation.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {
    ALLOWED_LIQUIDITY_LEVERAGE, ALLOWED_LIQUIDITY_LEVERAGE_MINUS_ONE, Q128
} from 'contracts/libraries/constants.sol';
import {Convert} from 'contracts/libraries/Convert.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Saturation} from 'contracts/libraries/Saturation.sol';

import {
    createLXYAssetsYDXB,
    LXY,
    sumOfSeriesOfBorrowY,
    sumOfSeriesOfCollateralX
} from 'test/Saturation/SaturationTestUtils.sol';

contract HardLiquidator is ICallback {
    FactoryPairTestFixture fixture;

    constructor(
        FactoryPairTestFixture _fixture
    ) {
        fixture = _fixture;
    }

    function ammalgamSwapCallV1(address, uint256, uint256, bytes calldata) external {} // noop

    function ammalgamBorrowCallV1(
        address, // sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountXShares,
        uint256 amountYShares,
        bytes calldata data
    ) external {} // noop

    function ammalgamBorrowLiquidityCallV1(
        address, // sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) external {} // noop

    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external {
        fixture.transferTokensTo(fixture.pairAddress(), repayXInXAssets, repayYInYAssets);
    }
}

contract LeverageLiquidator is ICallback {
    FactoryPairTestFixture fixture;
    address liquidator;

    constructor(FactoryPairTestFixture _fixture, address _liquidator) {
        fixture = _fixture;
        liquidator = _liquidator;
    }

    function ammalgamSwapCallV1(address, uint256, uint256, bytes calldata) external {} // noop

    function ammalgamBorrowCallV1(
        address, // sender,
        uint256 borrowedLXAssets,
        uint256 borrowedLYAssets,
        uint256, // amountXShares,
        uint256, // amountYShares,
        bytes calldata data
    ) external {
        (address borrower, bool depositL, bool repayL) = abi.decode(data, (address, bool, bool));

        fixture.transferTokensTo(fixture.pairAddress(), borrowedLXAssets, borrowedLYAssets);
        fixture.pair().liquidate(
            borrower, address(this), depositL ? 1 : 0, 0, 0, repayL ? 1 : 0, 0, 0, 0, Liquidation.LEVERAGE
        );

        if (depositL) {
            IERC20 tokenL = fixture.pair().tokens(DEPOSIT_L);
            uint256 sharesL = tokenL.balanceOf(address(this));
            tokenL.transfer(fixture.pairAddress(), sharesL);
            fixture.pair().burn(address(this));
        } else {
            IERC20 tokenX = fixture.pair().tokens(DEPOSIT_X);
            IERC20 tokenY = fixture.pair().tokens(DEPOSIT_Y);
            uint256 sharesX = tokenX.balanceOf(address(this));
            uint256 sharesY = tokenY.balanceOf(address(this));
            tokenX.transfer(fixture.pairAddress(), sharesX);
            tokenY.transfer(fixture.pairAddress(), sharesY);
            fixture.pair().withdraw(address(this));
        }

        if (repayL) {
            fixture.transferTokensTo(fixture.pairAddress(), borrowedLXAssets + 1, borrowedLYAssets + 1);
            fixture.pair().repayLiquidity(liquidator);
        } else {
            fixture.transferTokensTo(
                fixture.pairAddress(),
                IERC4626(address(fixture.pair().tokens(BORROW_X))).maxWithdraw(liquidator),
                IERC4626(address(fixture.pair().tokens(BORROW_Y))).maxWithdraw(liquidator)
            );
            fixture.pair().repay(liquidator);
        }

        // send profit to liquidator
        fixture.transferTokensTo(
            liquidator, fixture.tokenX().balanceOf(address(this)), fixture.tokenY().balanceOf(address(this))
        );
    }

    function ammalgamBorrowLiquidityCallV1(
        address, // sender,
        uint256 borrowedLXAssets,
        uint256 borrowedLYAssets,
        uint256, // amountLShares,
        bytes calldata data
    ) external {
        (address borrower, bool depositL, bool repayL) = abi.decode(data, (address, bool, bool));

        fixture.transferTokensTo(fixture.pairAddress(), borrowedLXAssets, borrowedLYAssets);
        fixture.pair().liquidate(
            borrower, fixture.pairAddress(), depositL ? 1 : 0, 0, 0, repayL ? 1 : 0, 0, 0, 0, Liquidation.LEVERAGE
        );

        if (depositL) {
            IERC20 tokenL = fixture.pair().tokens(DEPOSIT_L);
            uint256 sharesL = tokenL.balanceOf(address(this));
            tokenL.transfer(fixture.pairAddress(), sharesL);
            fixture.pair().burn(address(this));
        } else {
            IERC20 tokenX = fixture.pair().tokens(DEPOSIT_X);
            IERC20 tokenY = fixture.pair().tokens(DEPOSIT_Y);
            uint256 sharesX = tokenX.balanceOf(address(this));
            uint256 sharesY = tokenY.balanceOf(address(this));
            tokenX.transfer(fixture.pairAddress(), sharesX);
            tokenY.transfer(fixture.pairAddress(), sharesY);
            fixture.pair().withdraw(address(this));
        }

        fixture.transferTokensTo(fixture.pairAddress(), borrowedLXAssets + 1, borrowedLYAssets + 1);

        if (repayL) {
            fixture.pair().repayLiquidity(liquidator);
        } else {
            fixture.pair().repay(liquidator);
        }

        // send profit to liquidator
        fixture.transferTokensTo(
            liquidator, fixture.tokenX().balanceOf(address(this)), fixture.tokenY().balanceOf(address(this))
        );
    }

    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external {} // noop
}

contract LiquidationTests is Test {
    address immutable random = vm.addr(1);
    address immutable borrower = vm.addr(2);

    ICallback hardLiquidatorContract;
    address liquidator; // == address(hardLiquidatorContract)
    ICallback leverageLiquidatorContract;

    // initialPrice = 4 <=> tranche 70
    uint256 constant initialDepositLXInXAssets = 8e18;
    uint256 constant initialDepositLYInYAssets = 2e18;
    uint256 immutable initialActiveLiquidityInLAssets =
        Math.sqrt(Math.mulDiv(initialDepositLXInXAssets, initialDepositLYInYAssets, 1));
    uint256 constant initialAllowanceForAccountsXInXAssets = 1000e18;
    uint256 constant initialAllowanceForAccountsYInYAssets = 1000e18;

    FactoryPairTestFixture private fixture;
    IPairHarness public pair;
    address public pairAddress;

    IERC20 private tokenX;
    IERC20 private tokenY;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, true);
        pair = fixture.pair();
        pairAddress = address(pair);
        (tokenX, tokenY) = pair.underlyingTokens();
        hardLiquidatorContract = new HardLiquidator(fixture);
        liquidator = address(hardLiquidatorContract);
        leverageLiquidatorContract = new LeverageLiquidator(fixture, liquidator);

        fixture.transferTokensTo(random, initialAllowanceForAccountsXInXAssets, initialAllowanceForAccountsYInYAssets);
        fixture.transferTokensTo(borrower, initialAllowanceForAccountsXInXAssets, initialAllowanceForAccountsYInYAssets);
        fixture.transferTokensTo(
            liquidator, initialAllowanceForAccountsXInXAssets, initialAllowanceForAccountsYInYAssets
        );

        fixture.mintForAndInitializeBlocks(random, initialDepositLXInXAssets, initialDepositLYInYAssets);
    }

    /**
     * Leveraged liquidations
     */
    function testLiquidateLeverageCalcDeltaAndPremiumNegativePremium() public pure {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(100), uint256(0), uint256(0), uint256(80), uint256(0), uint256(0)],
            sqrtPriceMinInQ72: 1 << 72,
            sqrtPriceMaxInQ72: 1 << 72,
            activeLiquidityScalerInQ72: 1 << 72,
            activeLiquidityAssets: 400,
            reservesXAssets: 400,
            reservesYAssets: 400
        });
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);
        assertEq(leveragedLiquidationParams.closeInLAssets, 0);
        assertEq(leveragedLiquidationParams.premiumInLAssets, 0);
    }

    function testLiquidateLeverageCalcDeltaAndPremiumZeroPremiumEdge() public pure {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(1000), uint256(0), uint256(0), uint256(990), uint256(0), uint256(0)],
            sqrtPriceMinInQ72: 1 << 72,
            sqrtPriceMaxInQ72: 1 << 72,
            activeLiquidityScalerInQ72: 1 << 72,
            activeLiquidityAssets: 400,
            reservesXAssets: 400,
            reservesYAssets: 400
        });
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);
        assertEq(leveragedLiquidationParams.closeInLAssets, 0);
        assertEq(leveragedLiquidationParams.premiumInLAssets, 0);
    }

    function testLiquidateLeverageCalcDeltaAndPremiumNormalPremium() public pure {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(10_000), uint256(0), uint256(0), uint256(9910), uint256(0), uint256(0)],
            sqrtPriceMinInQ72: 1 << 72,
            sqrtPriceMaxInQ72: 1 << 72,
            activeLiquidityScalerInQ72: 1 << 72,
            activeLiquidityAssets: 400,
            reservesXAssets: 400,
            reservesYAssets: 400
        });
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);
        assertEq(leveragedLiquidationParams.closeInLAssets, 5950);
        assertEq(leveragedLiquidationParams.premiumInLAssets, 50);
    }

    function testLiquidateLeverageCalcDeltaAndPremiumAboveBorrowEqualDeposit() public pure {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(10_000), uint256(0), uint256(0), uint256(10_000), uint256(0), uint256(0)],
            sqrtPriceMinInQ72: 1 << 72,
            sqrtPriceMaxInQ72: 1 << 72,
            activeLiquidityScalerInQ72: 1 << 72,
            activeLiquidityAssets: 400,
            reservesXAssets: 400,
            reservesYAssets: 400
        });
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);
        assertEq(leveragedLiquidationParams.closeInLAssets, 9500);
        assertEq(leveragedLiquidationParams.premiumInLAssets, 500);
        assertEq(leveragedLiquidationParams.badDebt, true);
    }

    function testLiquidateLeverageCalcDeltaAndPremiumAboveBorrowAboveDeposit() public pure {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(10_000), uint256(0), uint256(0), uint256(10_100), uint256(0), uint256(0)],
            sqrtPriceMinInQ72: 1 << 72,
            sqrtPriceMaxInQ72: 1 << 72,
            activeLiquidityScalerInQ72: 1 << 72,
            activeLiquidityAssets: 400,
            reservesXAssets: 400,
            reservesYAssets: 400
        });
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);
        assertEq(leveragedLiquidationParams.closeInLAssets, 9000);
        assertEq(leveragedLiquidationParams.premiumInLAssets, 1000);
        assertEq(leveragedLiquidationParams.badDebt, true);
    }

    function testLiquidateLeverageCalcDeltaAndPremiumAboveBorrowVeryAboveDeposit() public pure {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(10_000), uint256(0), uint256(0), uint256(12_000), uint256(0), uint256(0)],
            sqrtPriceMinInQ72: 1 << 72,
            sqrtPriceMaxInQ72: 1 << 72,
            activeLiquidityScalerInQ72: 1 << 72,
            activeLiquidityAssets: 400,
            reservesXAssets: 400,
            reservesYAssets: 400
        });
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);
        assertEq(leveragedLiquidationParams.closeInLAssets, 0);
        assertEq(leveragedLiquidationParams.premiumInLAssets, 10_000);
        assertEq(leveragedLiquidationParams.badDebt, true);
    }

    function testLiquidateLeverageBadDebtLAgainstL() public {
        uint256 mintX = 4e18;
        uint256 mintY = 1e18;
        fixture.mintFor(borrower, mintX, mintY);

        // calculate the max borrowable liquidity
        uint256 collateralL = Math.sqrt(mintX * mintY);
        uint256 borrowLiquidity = collateralL - collateralL / ALLOWED_LIQUIDITY_LEVERAGE;
        fixture.borrowLiquidityFor(borrower, borrowLiquidity);

        pair.exposed_accrueInterestToToken(uint256(DEPOSIT_L), 0.1e18);

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        Validation.InputParams memory inputParams = getInputParams(borrower);
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, true, true);

        assertEq(leveragedLiquidationParams.badDebt, true, 'should have bad debt');

        uint256 depositLToBeTransferredInLAssets = 1; // just needs to be > 0
        uint256 closeInLAssets = leveragedLiquidationParams.closeInLAssets;
        uint256 repayLXInXAssets = closeInLAssets * mintX / collateralL;
        uint256 repayLYInYAssets = closeInLAssets * mintY / collateralL;

        // attacker == liquidator liquidate

        vm.startPrank(liquidator);

        tokenX.transfer(pairAddress, repayLXInXAssets);
        tokenY.transfer(pairAddress, repayLYInYAssets);

        pair.liquidate(
            borrower,
            liquidator,
            depositLToBeTransferredInLAssets,
            0,
            0,
            repayLXInXAssets,
            repayLYInYAssets,
            0,
            0,
            Liquidation.LEVERAGE
        );

        assertEq(
            pair.tokens(DEPOSIT_L).balanceOf(liquidator), collateralL, 'liquidator should have borrower collateral'
        );

        verifyBorrowerEmptyPosition();
    }

    function testLiquidateLeverageBadDebtLAgainstXAndY() public {
        uint256 depositX = 4e18;
        uint256 depositY = 1e18;

        // create leveraged position
        fixture.depositFor(borrower, depositX, depositY);

        // calculate the max liquidity that can be borrowed

        {
            (uint256 sqrtPriceMinInQ72, uint256 sqrtPriceMaxInQ72) = getMinAndMaxSqrtPrice();

            uint256 borrowLFromX = Validation.convertXToL(
                depositX - depositX / ALLOWED_LIQUIDITY_LEVERAGE, sqrtPriceMaxInQ72, 1 << 72, false
            );
            uint256 borrowLFromY = Validation.convertYToL(
                depositY - depositY / ALLOWED_LIQUIDITY_LEVERAGE, sqrtPriceMinInQ72, 1 << 72, false
            );

            uint256 borrowLAmount = Math.min(borrowLFromX, borrowLFromY);

            fixture.borrowLiquidityFor(borrower, borrowLAmount);

            // put the position under water
            pair.exposed_accrueInterestToToken(
                uint256(DEPOSIT_L), uint128((Math.sqrt(depositX * depositY) - borrowLAmount))
            );
        }

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        Validation.InputParams memory inputParams = getInputParams(borrower);
        (uint256 _reserveX, uint256 _reserveY,) = pair.getReserves();

        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);

        assertEq(leveragedLiquidationParams.badDebt, true, 'should have bad debt');

        uint256 repayLxAssets;
        uint256 repayLyAssets;

        {
            uint256 l = Math.sqrt(_reserveX * _reserveY);
            // liquidate
            repayLxAssets = Math.ceilDiv(leveragedLiquidationParams.closeInLAssets * _reserveX, l);
            repayLyAssets = Math.ceilDiv(leveragedLiquidationParams.closeInLAssets * _reserveY, l);
        }

        fixture.transferTokensTo(pairAddress, repayLxAssets, repayLyAssets);

        // Just setting booleans, we don't need to get the amounts right.
        pair.liquidate(borrower, address(this), 0, 1, 1, 1, 1, 0, 0, Liquidation.LEVERAGE);

        assertEq(
            pair.tokens(DEPOSIT_X).balanceOf(address(this)),
            leveragedLiquidationParams.closeInXAssets + leveragedLiquidationParams.premiumLInXAssets,
            'liquidator should have premiumLInXAssets'
        );
        assertEq(
            pair.tokens(DEPOSIT_Y).balanceOf(address(this)),
            leveragedLiquidationParams.closeInYAssets + leveragedLiquidationParams.premiumLInYAssets,
            'liquidator should have premiumLInYAssets'
        );

        verifyBorrowerEmptyPosition();
    }

    function testLiquidateLeverageBadDebtXAndYAgainstL() public {
        uint256 mintX = 4e18;
        uint256 mintY = 1e18;
        uint256 mintL = Math.sqrt(mintX * mintY);

        // create leveraged position
        fixture.transferTokensTo(borrower, mintX, mintY);
        fixture.mintFor(borrower, mintX, mintY);

        (uint256 sqrtPriceMinInQ72, uint256 sqrtPriceMaxInQ72) = getMinAndMaxSqrtPrice();

        uint256 borrowX =
            Validation.convertLToX(mintL - mintL / ALLOWED_LIQUIDITY_LEVERAGE, sqrtPriceMinInQ72, 1 << 72, false);
        uint256 borrowY =
            Validation.convertLToY(mintL - mintL / ALLOWED_LIQUIDITY_LEVERAGE, sqrtPriceMaxInQ72, 1 << 72, false);

        fixture.borrowFor(borrower, borrowX, borrowY);

        // put the position into liquidation past the break even point but not underwater.
        pair.exposed_accrueInterestToToken(uint256(DEPOSIT_X), uint128(mintX - borrowX) / 2);
        pair.exposed_accrueInterestToToken(uint256(DEPOSIT_Y), uint128(mintY - borrowY) / 2);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        Validation.InputParams memory inputParams = getInputParams(borrower);

        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);

        assertEq(leveragedLiquidationParams.badDebt, true, 'should have bad debt');

        // liquidate the position
        fixture.transferTokensTo(
            pairAddress, leveragedLiquidationParams.closeInXAssets, leveragedLiquidationParams.closeInYAssets
        );

        pair.liquidate(borrower, address(this), 1, 0, 0, 0, 0, 1, 1, Liquidation.LEVERAGE);

        assertEq(
            pair.tokens(DEPOSIT_L).balanceOf(address(this)),
            leveragedLiquidationParams.closeInLAssets + leveragedLiquidationParams.premiumInLAssets,
            'liquidator should have close amount plus premiumInLAssets'
        );

        verifyBorrowerEmptyPosition();
    }

    function testLiquidateLeverageLAgainstL() public {
        // setup

        uint256 mintX = 4e18;
        uint256 mintY = 1e18;

        fixture.transferTokensTo(borrower, mintX, mintY);
        fixture.mintFor(borrower, mintX, mintY);

        // calculate the max borrowable liquidity
        uint256 collateralL = Math.sqrt(mintX * mintY);
        uint256 borrowLiquidity = collateralL - collateralL / ALLOWED_LIQUIDITY_LEVERAGE;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(borrower, borrowLiquidity + 1);

        fixture.borrowLiquidityFor(borrower, borrowLiquidity);

        fixture.newBlock(10_000);

        // This reverts because the there is no more debt in borrowed L than there is collateral L.
        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        // liquidate

        vm.startPrank(liquidator);

        Validation.InputParams memory inputParams = getInputParams(borrower);
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, true, true);

        bytes memory data = abi.encode(borrower, true, true);

        pair.borrowLiquidity(address(leverageLiquidatorContract), leveragedLiquidationParams.closeInLAssets + 10, data);

        vm.stopPrank();

        // test

        assertGt(
            fixture.tokenX().balanceOf(liquidator),
            initialAllowanceForAccountsXInXAssets,
            'liquidator should have some X profit'
        );
        assertGt(
            fixture.tokenY().balanceOf(liquidator),
            initialAllowanceForAccountsYInYAssets,
            'liquidator should have some Y profit'
        );

        // Because we `borrowLiquidity`, `leveragedLiquidationParams` is not exactly same as at the time of liquidation.
        assertApproxEqRel(
            pair.exposed_getAssets(DEPOSIT_L, borrower),
            collateralL - leveragedLiquidationParams.closeInLAssets - leveragedLiquidationParams.premiumInLAssets,
            1e15,
            'borrower should have remaining DEPOSIT_L'
        );
        assertApproxEqRel(
            pair.exposed_getAssets(BORROW_L, borrower),
            borrowLiquidity - leveragedLiquidationParams.closeInLAssets,
            1e15,
            'borrower should have remaining BORROW_L'
        );
    }

    function testLiquidateLeverageNegativePremiumLAgainstL() public {
        // setup

        uint256 mintX = 4e18;
        uint256 mintY = 1e18;
        fixture.transferTokensTo(borrower, mintX, mintY);
        fixture.mintFor(borrower, mintX, mintY);

        uint256 collateralL = Math.sqrt(mintX * mintY);
        uint256 borrowLiquidity = collateralL / 2;
        fixture.borrowLiquidityFor(borrower, borrowLiquidity);

        uint256 borrowerDepositL = pair.exposed_getAssets(DEPOSIT_L, borrower);
        uint256 borrowerDepositX = pair.exposed_getAssets(DEPOSIT_X, borrower);
        uint256 borrowerDepositY = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        uint256 borrowerBorrowL = pair.exposed_getAssets(BORROW_L, borrower);
        uint256 borrowerBorrowX = pair.exposed_getAssets(BORROW_X, borrower);
        uint256 borrowerBorrowY = pair.exposed_getAssets(BORROW_Y, borrower);

        // try to liquidate

        vm.startPrank(liquidator);

        fixture.transferTokensTo(fixture.pairAddress(), 1, 1);
        pair.liquidate(borrower, liquidator, 1, 0, 0, 1, 1, 0, 0, Liquidation.LEVERAGE);

        vm.stopPrank();

        assertEq(pair.exposed_getAssets(DEPOSIT_L, borrower), borrowerDepositL, 'borrower should have same DEPOSIT_L');
        assertEq(pair.exposed_getAssets(DEPOSIT_X, borrower), borrowerDepositX, 'borrower should have same DEPOSIT_X');
        assertEq(pair.exposed_getAssets(DEPOSIT_Y, borrower), borrowerDepositY, 'borrower should have same DEPOSIT_Y');
        assertLe(pair.exposed_getAssets(BORROW_L, borrower), borrowerBorrowL, 'borrower should have same BORROW_L');
        assertLe(pair.exposed_getAssets(BORROW_X, borrower), borrowerBorrowX, 'borrower should have same BORROW_X');
        assertLe(pair.exposed_getAssets(BORROW_Y, borrower), borrowerBorrowY, 'borrower should have same BORROW_Y');
    }

    function testLiquidateLeverageLAgainstXAndY() public {
        uint256 depositXAmount = 0.04e18;
        uint256 depositYAmount = 0.01e18;

        fixture.transferTokensTo(borrower, depositXAmount, depositYAmount);
        fixture.depositFor(borrower, depositXAmount, depositYAmount);

        // calculate the max borrowable liquidity
        Validation.InputParams memory inputParams = getInputParams(borrower);
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);
        uint256 netDepositInLAssets =
            (checkLtvParams.netDepositedXinLAssets + checkLtvParams.netDepositedYinLAssets) / 2;

        uint256 borrowLiquidity = 99 * netDepositInLAssets / 100;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(borrower, borrowLiquidity + 1);

        fixture.borrowLiquidityFor(borrower, borrowLiquidity);

        fixture.newBlock(1_000_000);

        // This fails because the there is no more debt in borrowed L than there is collateral L.
        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        vm.startPrank(liquidator);

        inputParams = getInputParams(borrower);
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, false, true);

        bytes memory data = abi.encode(borrower, false, true);

        pair.borrowLiquidity(address(leverageLiquidatorContract), leveragedLiquidationParams.closeInLAssets + 1, data);

        vm.stopPrank();

        assertGt(
            fixture.tokenX().balanceOf(liquidator),
            initialAllowanceForAccountsXInXAssets,
            'liquidator should have some X profit'
        );
        assertGt(
            fixture.tokenY().balanceOf(liquidator),
            initialAllowanceForAccountsYInYAssets,
            'liquidator should have some Y profit'
        );

        // Because we `borrowLiquidity`, `leveragedLiquidationParams` is not exactly same as at the time of liquidation.
        assertApproxEqRel(
            pair.exposed_getAssets(DEPOSIT_X, borrower),
            depositXAmount - leveragedLiquidationParams.closeInXAssets - leveragedLiquidationParams.premiumLInXAssets,
            1e15,
            'borrower should have remaining DEPOSIT_X'
        );
        assertApproxEqRel(
            pair.exposed_getAssets(DEPOSIT_Y, borrower),
            depositYAmount - leveragedLiquidationParams.closeInYAssets - leveragedLiquidationParams.premiumLInYAssets,
            1e15,
            'borrower should have remaining DEPOSIT_Y'
        );
        assertApproxEqRel(
            pair.exposed_getAssets(BORROW_L, borrower),
            borrowLiquidity - leveragedLiquidationParams.closeInLAssets,
            1e16,
            'borrower should have remaining BORROW_L'
        );
    }

    function testLiquidateLeverageXAndYAgainstL() public {
        uint256 depositLX = 0.04e18;
        uint256 depositLY = 0.01e18;

        fixture.transferTokensTo(borrower, depositLX, depositLY);
        fixture.mintFor(borrower, depositLX, depositLY);

        // calculate the max borrowable liquidity
        Validation.InputParams memory inputParams = getInputParams(borrower);
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);

        uint256 netDepositInLAssets =
            (checkLtvParams.netDepositedXinLAssets + checkLtvParams.netDepositedYinLAssets) / 2;

        uint256 maxBorrowLiquidity = (99 * netDepositInLAssets) / 100;
        uint256 maxBorrowX = Validation.convertLToX(
            maxBorrowLiquidity,
            inputParams.sqrtPriceMinInQ72, // we inverse the validation calc using the min
            inputParams.activeLiquidityScalerInQ72,
            false
        );
        uint256 maxBorrowY = Validation.convertLToY(
            maxBorrowLiquidity,
            inputParams.sqrtPriceMaxInQ72, // we inverse the validation calc using the max
            inputParams.activeLiquidityScalerInQ72,
            false
        );

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowFor(borrower, maxBorrowX + 1, maxBorrowY + 1);

        fixture.borrowFor(borrower, maxBorrowX, maxBorrowY);

        fixture.newBlock(4_000_000);

        // This fails because the there is no more debt in borrowed L than there is collateral L.
        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        vm.startPrank(liquidator);

        inputParams = getInputParams(borrower);
        Liquidation.LeveragedLiquidationParams memory leveragedLiquidationParams =
            Liquidation.liquidateLeverageCalcDeltaAndPremium(inputParams, true, false);

        uint256 deltaNetDepositX = leveragedLiquidationParams.closeInXAssets;
        uint256 deltaNetDepositY = leveragedLiquidationParams.closeInYAssets;
        bytes memory data = abi.encode(borrower, true, false);
        pair.borrow(address(leverageLiquidatorContract), deltaNetDepositX, deltaNetDepositY, data);

        vm.stopPrank();

        // test

        pair.validateOnUpdate(borrower, borrower, true);

        assertGt(
            fixture.tokenX().balanceOf(liquidator),
            initialAllowanceForAccountsXInXAssets,
            'liquidator should have some X profit'
        );
        assertGt(
            fixture.tokenY().balanceOf(liquidator),
            initialAllowanceForAccountsYInYAssets,
            'liquidator should have some Y profit'
        );

        // Because we `borrowLiquidity`, `leveragedLiquidationParams` is not exactly same as at the time of liquidation.
        assertApproxEqRel(
            pair.exposed_getAssets(DEPOSIT_L, borrower),
            netDepositInLAssets - leveragedLiquidationParams.closeInLAssets
                - leveragedLiquidationParams.premiumInLAssets,
            1e16,
            'borrower should have remaining DEPOSIT_L'
        );
        assertApproxEqRel(
            pair.exposed_getAssets(BORROW_X, borrower),
            maxBorrowX - leveragedLiquidationParams.closeInXAssets,
            1e15,
            'borrower should have remaining BORROW_X'
        );
        assertApproxEqRel(
            pair.exposed_getAssets(BORROW_Y, borrower),
            maxBorrowY - leveragedLiquidationParams.closeInYAssets,
            1e15,
            'borrower should have remaining BORROW_Y'
        );
    }

    /**
     * Soft liquidations
     */
    function testLiquidateSoftYAgainstX() public {
        // borrower borrows
        uint256 depositXInXAssets = 0.004e18;
        fixture.depositFor(borrower, depositXInXAssets, 0);
        uint256 borrowYInYAssets = 0.0001e18;
        fixture.borrowFor(borrower, 0, borrowYInYAssets);

        // increase the debt by 2%
        fixture.pair().exposed_accrueInterestToToken(uint256(DEPOSIT_Y), uint128((borrowYInYAssets * 20) / 1000));

        // can take out a 0.1% to update the sat tree
        address softLiquidator = vm.addr(3);

        uint256 depositToReceiveInXAssets = depositXInXAssets * 10 / 10_000;
        pair.liquidate(borrower, softLiquidator, 0, depositToReceiveInXAssets, 0, 0, 0, 0, 0, Liquidation.SOFT);

        vm.stopPrank();

        assertEq(
            pair.exposed_getAssets(DEPOSIT_X, softLiquidator),
            depositToReceiveInXAssets,
            'softLiquidator should have DEPOSIT_X'
        );
    }

    function testLiquidateSoftXAgainstY() public {
        // borrower borrows with LTV ≈ 0.50
        uint256 depositYInYAssets = 0.0001e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        uint256 borrowXInXAssets = 0.0001e18;
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        // increase the debt by 1.5%
        fixture.pair().exposed_accrueInterestToToken(uint256(DEPOSIT_X), uint128((borrowXInXAssets * 15) / 1000));

        // only time passes

        uint256 duration = 1 days;
        vm.warp(block.timestamp + duration);
        vm.roll(block.number + Math.ceilDiv(duration, 12));
        fixture.pair().sync();

        // can take out a 0.05% to update the sat tree

        address softLiquidator = vm.addr(3);

        vm.startPrank(softLiquidator);

        uint256 depositToReceiveInYAssets = depositYInYAssets * 5 / 10_000;
        pair.liquidate(borrower, softLiquidator, 0, 0, depositToReceiveInYAssets, 0, 0, 0, 0, Liquidation.SOFT);

        vm.stopPrank();

        assertEq(
            pair.exposed_getAssets(DEPOSIT_Y, softLiquidator),
            depositToReceiveInYAssets,
            'liquidator should have DEPOSIT_Y'
        );
    }

    function testHardLiqRepayOnlyLXHack() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.0001e18;
        uint256 borrowXInXAssets = 0.00025e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        // liquidate

        vm.startPrank(liquidator);

        // set repayLX high, all other repays to 0
        vm.expectRevert(AmmalgamPair.InsufficientRepayLiquidity.selector); // should change when liquidations are revisited.
        pair.liquidate(borrower, borrower, 0, 0, 1, type(uint112).max, 0, 0, 0, Liquidation.HARD);
    }

    function testLiquidateSoftWrongDeposit() public {
        // borrower borrows with LTV ≈ 0.50
        uint256 depositYInYAssets = 0.0001e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        uint256 borrowXInXAssets = 0.0001e18;
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        pair.exposed_accrueInterestToToken(uint256(DEPOSIT_X), uint128((borrowXInXAssets * 3) / 10));

        // ask to receive L whilst deposit is Y

        vm.startPrank(liquidator);

        vm.expectRevert(Liquidation.LiquidationPremiumTooHigh.selector);
        pair.liquidate(borrower, borrower, 1, 0, 0, 0, 0, 0, 0, Liquidation.SOFT);

        vm.stopPrank();
    }

    /**
     * Hard liquidations
     */
    function testLiquidateHardRepayOnlyLXHack() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.0001e18;
        uint256 borrowXInXAssets = 0.00025e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        // liquidate

        vm.startPrank(liquidator);

        // set repayLX high, all other repays to 0
        vm.expectRevert(AmmalgamPair.InsufficientRepayLiquidity.selector); // should change when liquidations are revisited.
        pair.liquidate(borrower, borrower, 0, 0, 1, type(uint112).max, 0, 0, 0, Liquidation.HARD);

        vm.stopPrank();

        // borrower's position should be the same
        assertEq(pair.exposed_getAssets(DEPOSIT_Y, borrower), depositYInYAssets, 'borrower should have same DEPOSIT_Y');
        assertEq(pair.exposed_getAssets(BORROW_X, borrower), borrowXInXAssets, 'borrower should have same BORROW_X');
    }

    function testLiquidateHardBadDebtXAgainstY() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.0001e18;
        uint256 borrowXInXAssets = 0.00025e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        setLiquidateTickRange(151, 152); // 137% LTV; minTick = 151, maxTick = 152

        Validation.InputParams memory borrowerUnderWater = getInputParams(borrower);
        uint256 inputParamsSqrtPriceMinInQ72 = borrowerUnderWater.sqrtPriceMinInQ72;
        borrowerUnderWater.sqrtPriceMinInQ72 = borrowerUnderWater.sqrtPriceMaxInQ72;
        borrowerUnderWater.sqrtPriceMaxInQ72 = inputParamsSqrtPriceMinInQ72;
        uint256 maxPremiumInBips = Liquidation.calcHardMaxPremiumInBips(borrowerUnderWater);
        // swap min and max price back
        borrowerUnderWater.sqrtPriceMaxInQ72 = borrowerUnderWater.sqrtPriceMinInQ72;
        borrowerUnderWater.sqrtPriceMinInQ72 = inputParamsSqrtPriceMinInQ72;
        // LTV ≈ 1.37 => maxPremium = 1/LTV ≈ 0.73 => loss
        // repay all to max liq Premium
        // => repayPerc * LTV * maxPremium = maxDepositToReceivePerc == repayPerc
        // => 1 * 1.37 * 0.73 ≈ 1
        // => expect 1 of depositToReceive to work

        vm.expectRevert(Validation.AmmalgamLTV.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        // Reserves after
        (uint256 reserveXInXAssets,,) = pair.getReserves();
        uint128[6] memory totalAssets = pair.totalAssets();

        // repay minimum using sqrtPriceMaxInQ72
        uint256 priceInQ72 =
            Math.ceilDiv(borrowerUnderWater.sqrtPriceMaxInQ72 * borrowerUnderWater.sqrtPriceMaxInQ72, Q72);
        uint256 repayAmountXInXAssets = Math.ceilDiv(
            Math.ceilDiv(borrowerUnderWater.userAssets[DEPOSIT_Y] * priceInQ72, Q72) * BIPS, maxPremiumInBips
        );

        // liquidate
        vm.startPrank(liquidator);

        pair.liquidate(
            borrower,
            liquidator,
            0,
            0,
            borrowerUnderWater.userAssets[DEPOSIT_Y],
            0,
            0,
            repayAmountXInXAssets,
            0,
            Liquidation.HARD
        );

        vm.stopPrank();

        // asserts

        Validation.InputParams memory liquidatorAfter = getInputParams(liquidator);

        assertEq(
            liquidatorAfter.userAssets[DEPOSIT_Y],
            borrowerUnderWater.userAssets[DEPOSIT_Y],
            'liquidator should have the borrowers deposit Y'
        );

        (uint256 reserveXAfterInXAssets,,) = pair.getReserves();

        uint256 totalBurnedAssets = borrowerUnderWater.userAssets[BORROW_X] - repayAmountXInXAssets;

        uint256 expectedBurnedReserves =
            totalBurnedAssets * reserveXInXAssets / (reserveXInXAssets + totalAssets[DEPOSIT_X]);

        assertEq(
            reserveXInXAssets - reserveXAfterInXAssets,
            expectedBurnedReserves,
            'Reserves burnt should equal the bad debt scaled by reserves and deposit'
        );

        uint128[6] memory totalAssetsAfterLiquidation = pair.totalAssets();

        assertEq(
            totalAssets[DEPOSIT_X] - totalAssetsAfterLiquidation[DEPOSIT_X],
            totalBurnedAssets - expectedBurnedReserves,
            'Total assets DEPOSIT_X should be reduced by the bad debt scaled by reserves and deposit'
        );

        // assert borrower's position is gone
        verifyBorrowerEmptyPosition();
    }

    function createBadDebtWithYAgainstLAndX()
        private
        returns (uint256 maxPremiumInBips, Validation.InputParams memory borrowerUnderWater)
    {
        uint256 mintX = uint256(2e18) / 6;
        uint256 mintY = uint256(0.5e18) / 6;
        uint256 depositX = mintX / 10;
        uint256 borrowY = mintY
            + computeExpectedSwapOutAmount(
                (mintX + depositX) * LTVMAX_IN_MAG2 / MAG2, initialDepositLXInXAssets, initialDepositLYInYAssets
            );

        fixture.mintFor(borrower, mintX, mintY);
        fixture.depositFor(borrower, depositX, 0);
        fixture.borrowFor(borrower, 0, borrowY);

        setLiquidateTickRange(409, 410); // 95% LTV; minTick = 409, maxTick = 410

        vm.expectRevert(Validation.AmmalgamLTV.selector);
        pair.validateOnUpdate(borrower, borrower, true);

        borrowerUnderWater = getInputParams(borrower);
        maxPremiumInBips = Liquidation.calcHardMaxPremiumInBips(borrowerUnderWater);

        assertGt(maxPremiumInBips, 10 * BIPS / 9, 'verify we have bad debt');
    }

    function testLiquidateHardBadDebtYAgainstLAndX() public {
        (, Validation.InputParams memory borrowerUnderWater) = createBadDebtWithYAgainstLAndX();

        // Reserves after
        (, uint256 reserveYInYAssets,) = pair.getReserves();
        uint128[6] memory totalAssets = pair.totalAssets();

        uint256 repayBorrowedY = Math.ceilDiv(
            // Double the deposit L because value wise 1 units of L is worth twice the corresponding amount of X & Y
            (
                2 * borrowerUnderWater.userAssets[DEPOSIT_L]
                    + Math.ceilDiv(borrowerUnderWater.userAssets[DEPOSIT_X] * Q72, borrowerUnderWater.sqrtPriceMinInQ72)
            ) * Q72,
            borrowerUnderWater.sqrtPriceMinInQ72
        )
        // ) * BIPS / maxPremiumInBips; // 11350
        * 94 / 100;

        assertLt(repayBorrowedY, borrowerUnderWater.userAssets[BORROW_Y], 'Repay should be less than borrower debt');

        // liquidate by repaying Y only
        vm.startPrank(liquidator);
        pair.liquidate(
            borrower,
            liquidator,
            borrowerUnderWater.userAssets[DEPOSIT_L],
            borrowerUnderWater.userAssets[DEPOSIT_X],
            0,
            0,
            0,
            0,
            repayBorrowedY,
            Liquidation.HARD
        );
        vm.stopPrank();

        // asserts

        Validation.InputParams memory liquidatorAfter = getInputParams(liquidator);

        assertEq(
            liquidatorAfter.userAssets[DEPOSIT_L],
            borrowerUnderWater.userAssets[DEPOSIT_L],
            'liquidator should have the borrowers DEPOSIT_L'
        );
        assertEq(
            liquidatorAfter.userAssets[DEPOSIT_X],
            borrowerUnderWater.userAssets[DEPOSIT_X],
            'liquidator should have the borrowers DEPOSIT_X'
        );

        (, uint256 reserveYAfterInYAssets,) = pair.getReserves();

        uint256 totalBurnedAssets = borrowerUnderWater.userAssets[BORROW_Y] - repayBorrowedY;

        uint256 expectedBurnedReserves =
            totalBurnedAssets * reserveYInYAssets / (reserveYInYAssets + totalAssets[DEPOSIT_Y]);

        assertEq(
            reserveYInYAssets - reserveYAfterInYAssets,
            expectedBurnedReserves,
            'Reserves burnt should equal the bad debt scaled by reserves and deposit'
        );

        uint128[6] memory totalAssetsAfterLiquidation = pair.totalAssets();

        assertEq(
            totalAssets[DEPOSIT_Y] - totalAssetsAfterLiquidation[DEPOSIT_Y],
            totalBurnedAssets - expectedBurnedReserves,
            'Total assets DEPOSIT_Y should be reduced by the bad debt scaled by reserves and deposit'
        );

        // assert borrower's position is gone
        verifyBorrowerEmptyPosition();
    }

    function testLiquidateHardBadDebtLAndXAgainstY() public {
        // deposit Y, borrow L+X, LTV ca. 0.25
        uint256 depositYInYAssets = 0.0001e18;

        {
            fixture.depositFor(borrower, 0, depositYInYAssets);
            uint256 borrowXInXAssets = 0.0002e18;
            fixture.borrowFor(borrower, borrowXInXAssets, 0); // LTV ca. 0.5
            uint256 borrowLInLAssets = 0.00001e18;
            fixture.borrowLiquidityFor(borrower, borrowLInLAssets); // LTV ca. 0.58

            // set tick bring LTV to ca. 1.3 > 1
            setLiquidateTickRange(132, 133); // 137% LTV; minTick = 132, maxTick = 133
        }

        // LTV ≈ 1.3 => maxPremium = 1/LTV ≈ 0.7 => loss
        // repay all to max liq premium
        // => repayPerc * LTV * maxPremium = maxDepositToReceivePerc == repayPerc
        // => 1 * 1.3 * 0.7 ≈ 1
        // => expect 1 of depositToReceive to work

        // liquidate

        uint256 depositToReceiveInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        {
            vm.startPrank(liquidator);

            // repay all of BORROW_X
            uint256 repayAmountLInLAssets = pair.exposed_getAssets(BORROW_L, borrower);

            Validation.InputParams memory borrowerUnderWater = getInputParams(borrower);
            uint256 maxPremiumInBips = Liquidation.calcHardMaxPremiumInBips(borrowerUnderWater);

            // repay minimum using sqrtPriceMaxInQ72
            uint256 priceInQ72 =
                Math.ceilDiv(borrowerUnderWater.sqrtPriceMaxInQ72 * borrowerUnderWater.sqrtPriceMaxInQ72, Q72);
            uint256 repayAmountXFromDepositYInXAssets =
                Math.ceilDiv(Math.ceilDiv(depositToReceiveInYAssets * priceInQ72, Q72) * BIPS, maxPremiumInBips);

            (uint256 repayAmountLInXAssets, uint256 repayAmountLInYAssets) =
                fixture.computeAmountsForRepayLiquidity(repayAmountLInLAssets);

            pair.liquidate(
                borrower,
                liquidator,
                0,
                0,
                depositToReceiveInYAssets,
                repayAmountLInXAssets,
                repayAmountLInYAssets,
                repayAmountXFromDepositYInXAssets,
                0,
                Liquidation.HARD
            );

            vm.stopPrank();
        }

        // asserts
        uint256 liquidatorDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, liquidator);
        uint256 borrowerDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        uint256 borrowerBorrowLBalanceInLAssets = pair.exposed_getAssets(BORROW_L, borrower);
        uint256 borrowerBorrowXBalanceInXAssets = pair.exposed_getAssets(BORROW_X, borrower);

        assertEq(liquidatorDepositYBalanceInYAssets, depositToReceiveInYAssets);
        assertEq(borrowerDepositYBalanceInYAssets, depositYInYAssets - liquidatorDepositYBalanceInYAssets);
        assertEq(borrowerBorrowLBalanceInLAssets, 0);
        assertEq(borrowerBorrowXBalanceInXAssets, 0);
    }

    function testLiquidateHardBadDebtInOneTrancheYAgainstX() public {
        uint256 sqrtPriceLiquidationStartInQ72 = 2_185_871 * Q72 / 1_000_000; // B^4 = 2.18587
        int16 tickAtBadDebtForFirstTranche;
        {
            uint256 borrowY =
                sumOfSeriesOfBorrowY(sqrtPriceLiquidationStartInQ72, 200, initialActiveLiquidityInLAssets, 65);
            uint256 depositX =
                sumOfSeriesOfCollateralX(sqrtPriceLiquidationStartInQ72, 200, initialActiveLiquidityInLAssets, 65);
            fixture.depositFor(borrower, depositX, 0);
            fixture.borrowFor(borrower, 0, borrowY);

            // sqrtPrice = sqrt(x/y) the point at which the debt and collateral are equal ie 100% LTV
            // if we want the sqrt price where the debtY is 95% of the collateralX, then we have
            // badDebtSqrtPrice = sqrt(95% * collateralX / (debtY))

            // use first tranche price to determine where bad debt starts
            uint256 priceAtBadDebtForFirstTrancheInQ128 = (
                95 * sumOfSeriesOfCollateralX(sqrtPriceLiquidationStartInQ72, 100, initialActiveLiquidityInLAssets, 65)
                    / 100
            ) * Q128 / (sumOfSeriesOfBorrowY(sqrtPriceLiquidationStartInQ72, 100, initialActiveLiquidityInLAssets, 65));

            tickAtBadDebtForFirstTranche = TickMath.getTickAtPrice(priceAtBadDebtForFirstTrancheInQ128);

            setLiquidateTickRange(tickAtBadDebtForFirstTranche + 1, tickAtBadDebtForFirstTranche + 2);

            assertLt(
                borrowY * priceAtBadDebtForFirstTrancheInQ128 / depositX,
                90 * Q128,
                'LTV for the hole position should be < 90% and thus not in bad debt'
            );
        }

        Validation.InputParams memory firstTrancheBadDebtParams;
        {
            uint256 sqrtPriceLiquidationBadDebtInQ72 = TickMath.getSqrtPriceAtTick(tickAtBadDebtForFirstTranche + 1);
            firstTrancheBadDebtParams = getInputParams(
                borrower,
                tickAtBadDebtForFirstTranche + 1,
                tickAtBadDebtForFirstTranche + 2,
                initialActiveLiquidityInLAssets * sqrtPriceLiquidationBadDebtInQ72 / Q72,
                initialActiveLiquidityInLAssets * Q72 / sqrtPriceLiquidationBadDebtInQ72
            );
        }

        uint256 borrowYInFirstTranche =
            sumOfSeriesOfBorrowY(sqrtPriceLiquidationStartInQ72, 100, initialActiveLiquidityInLAssets, 65);

        bool badDebt = fixture.saturationAndGeometricTWAPState().liquidationCheckHardPremiums(
            firstTrancheBadDebtParams,
            borrower,
            Liquidation.HardLiquidationParams({
                depositLToBeTransferredInLAssets: 0,
                depositXToBeTransferredInXAssets: sumOfSeriesOfCollateralX(
                    sqrtPriceLiquidationStartInQ72, 100, initialActiveLiquidityInLAssets, 65
                ) * 1_148_148 / 1_000_000, // 14.8148% more than the collateral
                depositYToBeTransferredInYAssets: 0,
                repayLXInXAssets: 0,
                repayLYInYAssets: 0,
                repayXInXAssets: 0,
                repayYInYAssets: borrowYInFirstTranche
            }),
            0
        );

        assertFalse(badDebt, 'should not be in bad debt, since its only the first tranche');
    }

    function testLiquidateHardNegativePremiumFailsXAgainstY() public {
        // borrower borrows with LTV ≈ 0.50
        uint256 depositYInYAssets = 0.0001e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        uint256 borrowXInXAssets = 0.0002e18;
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        // try to liquidate as little as possible, should fail
        vm.startPrank(liquidator);
        uint256 repayAmountXInXAssets = 1;
        uint256 depositToReceiveInYAssets = 1;
        vm.expectRevert(Liquidation.LiquidationPremiumTooHigh.selector);
        pair.liquidate(
            borrower, liquidator, 0, 0, depositToReceiveInYAssets, 0, 0, repayAmountXInXAssets, 0, Liquidation.HARD
        );
        vm.stopPrank();
    }

    function testLiquidateHardNegativePremiumFailsLAgainstY() public {
        // borrower borrows with LTV ≈ 0.36
        uint256 depositYInYAssets = 0.01e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        uint256 borrowLInLAssets = 0.005e18;
        fixture.borrowLiquidityFor(borrower, borrowLInLAssets);

        // try to liquidate as little as possible, should fail
        vm.startPrank(liquidator);
        uint256 repayAmountXInXAssets = 1;
        uint256 repayAmountYInYAssets = 1;
        uint256 depositToReceiveInLAssets = 1;

        vm.expectRevert(Liquidation.LiquidationPremiumTooHigh.selector);
        pair.liquidate(
            borrower,
            liquidator,
            depositToReceiveInLAssets,
            0,
            0,
            repayAmountXInXAssets,
            repayAmountYInYAssets,
            0,
            0,
            Liquidation.HARD
        );
        vm.stopPrank();
    }

    function testLiquidateHardNegativePremiumLAgainstY() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.01e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        uint256 borrowLInLAssets = 0.0077e18;
        fixture.borrowLiquidityFor(borrower, borrowLInLAssets);

        setLiquidateTickRange(357, 358); // 62% LTV; minTick = 357, maxTick = 358

        uint256 afterBorrowerBorrowLBalanceInXAssets = pair.exposed_getAssets(BORROW_L, borrower);

        // repay full
        uint256 repayAmountLInLAssets = pair.exposed_getAssets(BORROW_L, borrower);
        (uint256 repayAmountLInXAssets, uint256 repayAmountLInYAssets) =
            fixture.computeAmountsForRepayLiquidity(repayAmountLInLAssets);

        uint256 depositToReceiveInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);

        {
            // LTV ≈ 0.62 => maxPremium ≈ 0.15 => loss
            // => repayPercentage * LTV * maxPremium = maxDepositToReceivePercentage
            // => 0.62 * 0.15 ≈ 0.09
            // => expect 0.09 of depositToReceive to revert
            // => expect 0.08 of depositToReceive to work

            // liquidate

            vm.startPrank(liquidator);

            // depositToReceive 0.44 is too much
            vm.expectRevert(Liquidation.LiquidationPremiumTooHigh.selector);
            pair.liquidate(
                borrower,
                liquidator,
                0,
                0,
                (depositToReceiveInYAssets * 44) / 100,
                repayAmountLInXAssets,
                repayAmountLInYAssets,
                0,
                0,
                Liquidation.HARD
            );

            // depositToReceive 0.43 is good
            pair.liquidate(
                borrower,
                liquidator,
                0,
                0,
                (depositToReceiveInYAssets * 43) / 100,
                repayAmountLInXAssets,
                repayAmountLInYAssets,
                0,
                0,
                Liquidation.HARD
            );

            vm.stopPrank();
        }

        // asserts

        uint256 liquidatorDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, liquidator);
        uint256 liquidatorBorrowLBalanceInXAssets = pair.exposed_getAssets(BORROW_L, liquidator);
        uint256 borrowerDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        uint256 borrowerBorrowLBalanceInXAssets = pair.exposed_getAssets(BORROW_L, borrower);
        assertEq(
            liquidatorDepositYBalanceInYAssets,
            (depositToReceiveInYAssets * 43) / 100,
            'liquidator should have the expected deposit assets'
        );
        assertEq(liquidatorBorrowLBalanceInXAssets, 0, 'liquidator should have no borrow assets');
        assertEq(
            borrowerDepositYBalanceInYAssets,
            depositYInYAssets - liquidatorDepositYBalanceInYAssets,
            'borrower should have the left over deposit assets'
        );
        assertApproxEqRel(
            borrowerBorrowLBalanceInXAssets,
            afterBorrowerBorrowLBalanceInXAssets - repayAmountLInLAssets,
            1e15,
            'borrower should have the left over borrow assets'
        );
    }

    function testLiquidateHardNegativePremiumXAgainstY() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.0001e18;
        uint256 borrowXInXAssets = 0.00025e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        setLiquidateTickRange(352, 353); // 63% LTV; minTick = 352, maxTick = 353

        Validation.InputParams memory inputParams = getInputParams(borrower);
        uint256 inputParamsSqrtPriceMinInQ72 = inputParams.sqrtPriceMinInQ72;
        inputParams.sqrtPriceMinInQ72 = inputParams.sqrtPriceMaxInQ72;
        inputParams.sqrtPriceMaxInQ72 = inputParamsSqrtPriceMinInQ72;
        uint256 maxPremiumInBips = Liquidation.calcHardMaxPremiumInBips(inputParams);
        // swap min and max price back
        inputParams.sqrtPriceMaxInQ72 = inputParams.sqrtPriceMinInQ72;
        inputParams.sqrtPriceMinInQ72 = inputParamsSqrtPriceMinInQ72;

        // LTV ≈ 0.63 => maxPremium ≈ 0.18 => loss
        // => repayPerc * LTV * maxPremium = maxDepositToReceivePerc
        // => 1 * 0.63 * 0.18 ≈ 0.11
        // => expect 0.11 of depositToReceive to revert
        // => expect 0.10 of depositToReceive to work

        // repay in full
        uint256 repayAmountXInXAssets = pair.exposed_getAssets(BORROW_X, borrower);

        uint256 priceInQ72 = Math.ceilDiv(inputParams.sqrtPriceMaxInQ72 * inputParams.sqrtPriceMaxInQ72, Q72);
        uint256 depositToReceiveInYAssets = (repayAmountXInXAssets * Q72 / priceInQ72) * maxPremiumInBips / BIPS;

        {
            // liquidate

            vm.startPrank(liquidator);

            // depositToReceive 1% extra is too much
            vm.expectRevert(Liquidation.LiquidationPremiumTooHigh.selector);
            pair.liquidate(
                borrower,
                liquidator,
                0,
                0,
                (depositToReceiveInYAssets * 101) / 100,
                0,
                0,
                repayAmountXInXAssets,
                0,
                Liquidation.HARD
            );

            // depositToReceive is good
            pair.liquidate(
                borrower, liquidator, 0, 0, depositToReceiveInYAssets, 0, 0, repayAmountXInXAssets, 0, Liquidation.HARD
            );

            vm.stopPrank();
        }

        // asserts

        uint256 liquidatorDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, liquidator);
        uint256 liquidatorBorrowXBalanceInXAssets = pair.exposed_getAssets(BORROW_X, liquidator);
        uint256 borrowerDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        uint256 borrowerBorrowXBalanceInXAssets = pair.exposed_getAssets(BORROW_X, borrower);
        assertEq(
            liquidatorDepositYBalanceInYAssets,
            depositToReceiveInYAssets,
            'liquidator should have the expected deposit assets'
        );
        assertEq(liquidatorBorrowXBalanceInXAssets, 0, 'liquidator should have no borrow assets');
        assertEq(
            borrowerDepositYBalanceInYAssets,
            depositYInYAssets - liquidatorDepositYBalanceInYAssets,
            'borrower should have the left over deposit assets'
        );
        assertEq(
            borrowerBorrowXBalanceInXAssets,
            borrowXInXAssets - repayAmountXInXAssets,
            'borrower should have the left over borrow assets'
        );
    }

    function testLiquidateHardPositivePremiumXAgainstY() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.0001e18;
        uint256 borrowXInXAssets = 0.00025e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        setLiquidateTickRange(268, 269); // 87% LTV; minTick = 268, maxTick = 269

        Validation.InputParams memory inputParams = getInputParams(borrower);
        uint256 inputParamsSqrtPriceMinInQ72 = inputParams.sqrtPriceMinInQ72;
        inputParams.sqrtPriceMinInQ72 = inputParams.sqrtPriceMaxInQ72;
        inputParams.sqrtPriceMaxInQ72 = inputParamsSqrtPriceMinInQ72;
        uint256 maxPremiumInBips = Liquidation.calcHardMaxPremiumInBips(inputParams);
        // swap min and max price back
        inputParams.sqrtPriceMaxInQ72 = inputParams.sqrtPriceMinInQ72;
        inputParams.sqrtPriceMinInQ72 = inputParamsSqrtPriceMinInQ72;

        uint256 afterBorrowerDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        uint256 afterBorrowerBorrowXBalanceInXAssets = pair.exposed_getAssets(BORROW_X, borrower);

        // LTV ≈ 0.87 => maxPremium ≈ 1.09 => profit
        // => repayPerc * LTV * maxPremium = maxDepositToReceivePerc
        // => 0.87 * 1.09 = 0.95
        // => expect 0.96 of depositToReceive to revert
        // => expect 0.95 of depositToReceive to work

        // repay full
        uint256 repayAmountXInXAssets = pair.exposed_getAssets(BORROW_X, borrower);

        uint256 priceInQ72 = Math.ceilDiv(inputParams.sqrtPriceMaxInQ72 * inputParams.sqrtPriceMaxInQ72, Q72);
        uint256 depositToReceiveInYAssets = (repayAmountXInXAssets * Q72 / priceInQ72) * maxPremiumInBips / BIPS;

        {
            // liquidate

            vm.startPrank(liquidator);

            // depositToReceive 1% extra is too much
            vm.expectRevert(Liquidation.LiquidationPremiumTooHigh.selector);
            pair.liquidate(
                borrower,
                liquidator,
                0,
                0,
                (depositToReceiveInYAssets * 101) / 100,
                0,
                0,
                repayAmountXInXAssets,
                0,
                Liquidation.HARD
            );

            // depositToReceive is good
            pair.liquidate(
                borrower, liquidator, 0, 0, depositToReceiveInYAssets, 0, 0, repayAmountXInXAssets, 0, Liquidation.HARD
            );

            vm.stopPrank();
        }

        uint256 liquidatorDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, liquidator);
        uint256 liquidatorBorrowXBalanceInXAssets = pair.exposed_getAssets(BORROW_X, liquidator);
        uint256 borrowerDepositYBalanceInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);
        uint256 borrowerBorrowXBalanceInXAssets = pair.exposed_getAssets(BORROW_X, borrower);
        assertEq(liquidatorDepositYBalanceInYAssets, depositToReceiveInYAssets, 'liquidator should have deposit assets');
        assertEq(liquidatorBorrowXBalanceInXAssets, 0, 'liquidator should have no borrow assets');
        assertEq(
            borrowerDepositYBalanceInYAssets,
            afterBorrowerDepositYBalanceInYAssets - liquidatorDepositYBalanceInYAssets,
            'borrower should have the left over deposit assets'
        );
        assertEq(
            borrowerBorrowXBalanceInXAssets,
            afterBorrowerBorrowXBalanceInXAssets - repayAmountXInXAssets,
            'borrower should have the left over borrow assets'
        );
    }

    function testLiquidateHardFullPremiumXAgainstY() public {
        // borrower borrows with LTV ≈ 0.63
        uint256 depositYInYAssets = 0.0001e18;
        uint256 borrowXInXAssets = 0.00025e18;
        fixture.depositFor(borrower, 0, depositYInYAssets);
        fixture.borrowFor(borrower, borrowXInXAssets, 0);

        setLiquidateTickRange(260, 261); // 90% LTV; minTick = 260, maxTick = 262
        // maxPremiumInBips = 11118 and premiumInBips = 11098
        // Using `minTick` = 260 and `maxTick` = 261, causes the position to go into `badDebt`

        // liquidate

        vm.startPrank(liquidator);

        // repay all
        uint256 repayAmountXInXAssets = pair.exposed_getAssets(BORROW_X, borrower);

        // depositToReceive all
        uint256 depositToReceiveInYAssets = pair.exposed_getAssets(DEPOSIT_Y, borrower);

        pair.liquidate(
            borrower, liquidator, 0, 0, depositToReceiveInYAssets, 0, 0, repayAmountXInXAssets, 0, Liquidation.HARD
        );

        vm.stopPrank();

        // asserts

        // liquidator should have the expected deposit assets
        assertEq(
            pair.exposed_getAssets(DEPOSIT_Y, liquidator),
            depositToReceiveInYAssets,
            'liquidator should have expected DEPOSIT_Y'
        );

        // borrower should have no position
        verifyBorrowerEmptyPosition();
    }

    function testLiquidateHardPremiumInBips() public pure {
        uint256 ltvBips;
        uint256 premiumInBips;

        ltvBips = 5000; // 0.5;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 0, '0.5 liquidation premium should be 0');

        ltvBips = 5900; // 0.59;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 0, '0.59 liquidation premium should be 0');

        ltvBips = 6000; // 0.6;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 0, '0.6 liquidation premium should be 0');

        ltvBips = 6001; // 0.6001;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 6, '0.6 liquidation premium should be 1');

        ltvBips = 6100; // 0.61;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 666, '0.61 liquidation premium should be ca. 0.0667');

        ltvBips = 7000; // 0.7;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 6666, '0.7 liquidation premium should be ca. 0.6667');

        ltvBips = 7400; // 0.74;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 9333, '0.74 liquidation premium should be ca. 0.9333');

        ltvBips = 7499; // 0.7499;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 9993, '0.7499 liquidation premium should be 0.9993');

        ltvBips = 7500; // 0.75;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, BIPS, '0.75 liquidation premium should be 1');

        ltvBips = 7501; // 0.75;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, BIPS, '0.7501 liquidation premium should be 1.0001');

        ltvBips = 7502; // 0.75;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 10_001, '0.7502 liquidation premium should be 1.0001');

        ltvBips = 7600; // 0.76;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 10_074, '0.76 liquidation premium should be ca. 1.0074');

        ltvBips = 7900; // 0.79;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 10_296, '0.79 liquidation premium should be ca. 1.0296');

        ltvBips = 8900; // 0.89;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 11_037, '0.89 liquidation premium should be ca. 1.1037');

        ltvBips = 9000; // 0.9;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 11_111, '0.9 liquidation premium should be 1/0.9');

        ltvBips = 11_000; // 1.1;
        premiumInBips = Liquidation.convertLtvToPremium(ltvBips);
        assertEq(premiumInBips, 12_592, '1.1 liquidation premium should be 20/27*1.1 + 4/9 = 1.2592');
    }

    function getInputParams(
        address _borrower
    ) internal view returns (Validation.InputParams memory inputParams) {
        (uint256 _reserveXAssets, uint256 _reserveYAssets,) = pair.getReserves();
        (int16 minTick, int16 maxTick) =
            fixture.saturationAndGeometricTWAPState().getTickRange(address(pair), getCurrentTick(), true);
        inputParams = getInputParams(_borrower, minTick, maxTick, _reserveXAssets, _reserveYAssets);
    }

    function getInputParams(
        address _borrower,
        int16 minTick,
        int16 maxTick,
        uint256 _reserveXAssets,
        uint256 _reserveYAssets
    ) internal view returns (Validation.InputParams memory inputParams) {
        inputParams = Validation.getInputParams(
            pair.totalAssets(), pair.exposed_getAssets(_borrower), _reserveXAssets, _reserveYAssets, 0, minTick, maxTick
        );
    }

    function getMinAndMaxSqrtPrice() private view returns (uint256 sqrtPriceMinInQ72, uint256 sqrtPriceMaxInQ72) {
        // calculate the max borrowable liquidity
        (int16 minTick, int16 maxTick) =
            fixture.saturationAndGeometricTWAPState().getTickRange(pairAddress, getCurrentTick(), true);

        sqrtPriceMinInQ72 = TickMath.getSqrtPriceAtTick(minTick);
        sqrtPriceMaxInQ72 = TickMath.getSqrtPriceAtTick(maxTick);
    }

    function getCurrentTick() internal view returns (int16) {
        (uint256 _reserveXAssets, uint256 _reserveYAssets,) = pair.getReserves();
        uint256 priceInQ128 = (_reserveXAssets * Q128) / _reserveYAssets;
        return TickMath.getTickAtPrice(priceInQ128);
    }

    function swapXIn(
        uint256 swapAmountXInInXAssets
    ) private {
        uint256 swapAmountYOutInYAssets;

        (uint256 reserveXAssets, uint256 reserveYAssets,) = pair.getReserves();
        swapAmountYOutInYAssets = computeExpectedSwapOutAmount(swapAmountXInInXAssets, reserveXAssets, reserveYAssets);
        fixture.swapXInYOut(random, swapAmountXInInXAssets, swapAmountYOutInYAssets);
        // loop to get price range small
        for (uint256 i = 0; i < 1000; i++) {
            uint256 duration = 12 seconds;
            vm.warp(block.timestamp + duration);
            vm.roll(block.number + Math.ceilDiv(duration, 12));
            pair.sync();
        }
    }

    function swapYIn(
        uint256 swapAmountYInInYAssets
    ) private {
        uint256 swapAmountXOutInXAssets;
        uint112 tokenOutReserveAmountInAssets;
        uint112 tokenInReserveAmountInAssets;
        (tokenOutReserveAmountInAssets, tokenInReserveAmountInAssets,) = pair.getReserves();
        swapAmountXOutInXAssets = computeExpectedSwapOutAmount(
            swapAmountYInInYAssets, tokenInReserveAmountInAssets, tokenOutReserveAmountInAssets
        );
        fixture.swapYInXOut(random, swapAmountYInInYAssets, swapAmountXOutInXAssets);
        // loop to get price range small
        for (uint256 i = 0; i < 1000; i++) {
            uint256 duration = 1 days;
            vm.warp(block.timestamp + duration);
            vm.roll(block.number + Math.ceilDiv(duration, 12));
            fixture.pair().sync();
        }
    }

    function setLiquidateTickRange(int16 minTick, int16 maxTick) private {
        fixture.saturationAndGeometricTWAPState().setTickRange(minTick, maxTick);
    }

    // Desired LTV for which the liquidation will be triggered
    function setLiquidateTickRange(
        uint256[6] memory userAssets
    ) private {
        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets); // 60% LTV
        int16 liqTick;

        // Check there is not a 0 price
        if (liqSqrtPrice1 > 0) {
            liqTick = TickMath.getTickAtPrice(liqSqrtPrice1 * Q56);
            fixture.saturationAndGeometricTWAPState().setTickRange(liqTick - 1, liqTick);
        } else if (liqSqrtPrice2 > 0) {
            liqTick = TickMath.getTickAtPrice(liqSqrtPrice2 * Q56);
            fixture.saturationAndGeometricTWAPState().setTickRange(liqTick + 1, liqTick + 2);
        } else {
            revert('liqSqrtPrice1 and liqSqrtPrice2 are both 0');
        }
    }

    function decreaseForSlippage(uint256 debtL, uint256 activeLiquidityAssets) private pure returns (uint256) {
        return debtL * activeLiquidityAssets / (activeLiquidityAssets + debtL);
    }

    function verifyBorrowerEmptyPosition() private view {
        Validation.InputParams memory inputParams = getInputParams(borrower);
        assertEq(inputParams.userAssets[DEPOSIT_L], 0, 'borrower should have no DEPOSIT_L');
        assertEq(inputParams.userAssets[DEPOSIT_X], 0, 'borrower should have no DEPOSIT_X');
        assertEq(inputParams.userAssets[DEPOSIT_Y], 0, 'borrower should have no DEPOSIT_Y');
        assertEq(inputParams.userAssets[BORROW_L], 0, 'borrower should have no BORROW_L');
        assertEq(inputParams.userAssets[BORROW_X], 0, 'borrower should have no BORROW_X');
        assertEq(inputParams.userAssets[BORROW_Y], 0, 'borrower should have no BORROW_Y');
    }
}
