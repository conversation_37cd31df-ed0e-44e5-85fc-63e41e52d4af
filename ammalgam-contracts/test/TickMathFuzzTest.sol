// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {Q128} from 'contracts/libraries/constants.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';

contract TickMathFuzzTest is Test {
    // uniqueness and increasing order
    function testGetSqrtPriceAtTick(
        int16 tick
    ) public pure {
        tick = int16(bound(tick, TickMath.MIN_TICK + 1, TickMath.MAX_TICK - 1));
        verifySqrtPrice(tick);
    }

    // the price is always between the returned tick and the returned tick+1
    function testGetTickAtPriceF(
        uint256 priceInQ128
    ) public pure {
        priceInQ128 = bound(priceInQ128, TickMath.MIN_PRICE_IN_Q128, TickMath.MAX_PRICE_IN_Q128 - 1);
        verifyTick(priceInQ128);
    }

    function verifySqrtPrice(
        int16 tick
    ) private pure {
        uint256 sqrtPriceInQ72 = TickMath.getSqrtPriceAtTick(tick);

        assert(TickMath.MIN_SQRT_PRICE_IN_Q72 <= sqrtPriceInQ72);
        assert(sqrtPriceInQ72 <= TickMath.MAX_SQRT_PRICE_IN_Q72);

        if (TickMath.MIN_TICK < tick) assert(TickMath.getSqrtPriceAtTick(tick - 1) < sqrtPriceInQ72);
        if (tick < TickMath.MAX_TICK) assert(sqrtPriceInQ72 < TickMath.getSqrtPriceAtTick(tick + 1));
    }

    function verifyTick(
        uint256 priceInQ128
    ) private pure {
        int16 tick = TickMath.getTickAtPrice(priceInQ128);
        assertLe(TickMath.MIN_TICK, tick, 'tick <= MIN_TICK');
        assertLe(tick, TickMath.MAX_TICK, 'MAX_TICK <= tick');

        uint256 priceAtTickInQ128 = TickMath.getPriceAtTick(tick);
        assertLe(priceAtTickInQ128, priceInQ128, 'priceAtTickInQ128 <= priceInQ128');

        if (tick == TickMath.MAX_TICK) return;

        uint256 priceAtTickPlusOneInQ128 = TickMath.getPriceAtTick(tick + 1);

        assertLt(priceInQ128, priceAtTickPlusOneInQ128, 'priceAtTickPlusOneInQ128 < priceInQ128');
    }
}
