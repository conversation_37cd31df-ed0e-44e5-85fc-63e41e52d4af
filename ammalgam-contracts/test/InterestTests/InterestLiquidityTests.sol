// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {Interest} from 'contracts/libraries/Interest.sol';
import {DEPOSIT_L, BORROW_L} from 'contracts/interfaces/tokens/ITokenController.sol';

import {FactoryPairTestFixture, MAX_TOKEN, IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';
import {
    InterestFixture,
    SharesAndAssets,
    MintAndBurnInputParams,
    BorrowAndRepayLiquidityInputParams,
    RAY
} from 'test/InterestTests/InterestFixture.sol';

import {TOLERANCE_1} from 'test/utils/constants.sol';

contract InterestLiquidityTests is Test {
    FactoryPairTestFixture public fixture;
    InterestFixture public interestFixture;

    address random = address(0xa0);
    address borrower1Addr = address(0xb1);
    address minter1Addr = address(0xd1);
    address minter2Addr = address(0xd2);

    MintAndBurnInputParams user1Params;
    MintAndBurnInputParams user2Params;
    BorrowAndRepayLiquidityInputParams borrower1Params;
    SharesAndAssets states;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        interestFixture = new InterestFixture(fixture);

        // clear states by initializing empty struct and setting it to storage struct
        MintAndBurnInputParams memory emptyMintAndBurnInputParams;
        user1Params = emptyMintAndBurnInputParams;
        user2Params = emptyMintAndBurnInputParams;
        BorrowAndRepayLiquidityInputParams memory emptyBorrowAndRepayLiquidityInputParams;
        borrower1Params = emptyBorrowAndRepayLiquidityInputParams;
        SharesAndAssets memory emptyPairState;
        states = emptyPairState;

        initPool(100e18, 400e18);

        user1Params.userAddress = minter1Addr;
        user1Params.mintedLX = 100e18;
        user1Params.mintedLY = 400e18;
        user2Params.userAddress = minter2Addr;
        user2Params.mintedLX = 200e18;
        user2Params.mintedLY = 800e18;

        borrower1Params.userAddress = borrower1Addr;
    }

    function initPool(uint256 _initialLX, uint256 _initialLY) private {
        fixture.transferTokensTo(random, _initialLX, _initialLY);
        fixture.mintForAndInitializeBlocks(random, _initialLX, _initialLY);
    }

    function testInterestLiquidityExecuteMintAndBurnWorkflow0() public {
        user1Params.burnRate = 100;
        user2Params.burnRate = 100;
        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256[2] memory durations = [uint256(365 days), uint256(365 days)];

        user1Params = interestFixture.mintHelper(user1Params);

        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);

        interestFixture.warpAndSkim(durations[0]);

        user2Params = interestFixture.mintHelper(user2Params);
        interestFixture.warpAndSkim(durations[1]);

        user1Params = interestFixture.burnHelper(user1Params);
        user2Params = interestFixture.burnHelper(user2Params);

        verifyMintAndBurn(user1Params);
        verifyMintAndBurn(user2Params);
    }

    function testInterestLiquidityExecuteMintAndBurnWorkflow2() public {
        user1Params.burnRate = 100;
        user2Params.burnRate = 100;
        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256[2] memory durations = [uint256(36), uint256(36)];

        user1Params = interestFixture.mintHelper(user1Params);
        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);
        interestFixture.warpAndSkim(durations[0]);

        user2Params = interestFixture.mintHelper(user2Params);
        interestFixture.warpAndSkim(durations[1]);

        user1Params = interestFixture.burnHelper(user1Params);
        user2Params = interestFixture.burnHelper(user2Params);

        verifyMintAndBurn(user1Params);
        verifyMintAndBurn(user2Params);
    }

    function testInterestLiquidityExecuteMintAndBurnWorkflow3() public {
        user1Params.burnRate = 100;
        user2Params.burnRate = 100;
        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256[2] memory durations = [uint256(36), uint256(36)];

        user1Params = interestFixture.mintHelper(user1Params);
        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);

        interestFixture.warpAndSkim(durations[0]);

        user1Params = interestFixture.burnHelper(user1Params);

        user2Params = interestFixture.mintHelper(user2Params);
        interestFixture.warpAndSkim(durations[1]);

        user2Params = interestFixture.burnHelper(user2Params);

        verifyMintAndBurn(user1Params);
        verifyMintAndBurn(user2Params);
    }

    function testInterestLiquidityLiquidityInterest_MintAndBurn_InterestWithStartingBlocks() public {
        // test starting block number at 100000 instead of 1, when lastLendingTimestampUpdate = 0
        vm.roll(100_000);
        testInterestLiquidityExecuteMintAndBurnWorkflow3();
    }

    function testInterestLiquidityExecuteMintAndBurnWorkflow4() public {
        user1Params.burnRate = 100;
        user2Params.burnRate = 100;
        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256[2] memory durations = [uint256(36), uint256(36)];

        user1Params = interestFixture.mintHelper(user1Params);
        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);

        user1Params = interestFixture.burnHelper(user1Params);

        interestFixture.warpAndSkim(durations[0]);

        user2Params = interestFixture.mintHelper(user2Params);

        interestFixture.warpAndSkim(durations[1]);

        user2Params = interestFixture.burnHelper(user2Params);

        verifyMintAndBurn(user1Params);
        verifyMintAndBurn(user2Params);
    }

    function testInterestLiquidityPartialBurnMint1AndMint2() public {
        user1Params.burnRate = 50;
        user2Params.burnRate = 50;
        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256[2] memory durations = [uint256(36), uint256(36)];

        user1Params = interestFixture.mintHelper(user1Params);
        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);

        interestFixture.warpAndSkim(durations[0]);

        user2Params = interestFixture.mintHelper(user2Params);

        interestFixture.warpAndSkim(durations[1]);

        user1Params = interestFixture.burnHelper(user1Params);
        user2Params = interestFixture.burnHelper(user2Params);

        verifyMintAndBurn(user1Params);
        verifyMintAndBurn(user2Params);
    }

    function testInterestLiquidityFiveScalersAccrual() public {
        user1Params.burnRate = 100;
        user2Params.burnRate = 100;

        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256 startingBlock = 100;
        uint256[6] memory durations = [startingBlock, uint256(36), uint256(10), uint256(1), uint256(30), uint256(300)];

        // initial durations before initial pool
        interestFixture.warpAndSkim(durations[0]);

        user1Params = interestFixture.mintHelper(user1Params);
        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);

        interestFixture.warpAndSkim(durations[1]);
        interestFixture.warpAndSkim(durations[2]);
        interestFixture.warpAndSkim(durations[3]);

        user2Params = interestFixture.mintHelper(user2Params);

        interestFixture.warpAndSkim(durations[4]);
        interestFixture.warpAndSkim(durations[5]);

        user1Params = interestFixture.burnHelper(user1Params);
        user2Params = interestFixture.burnHelper(user2Params);

        verifyMintAndBurn(user1Params);
        verifyMintAndBurn(user2Params);
    }

    function verifyMintAndBurn(
        MintAndBurnInputParams memory user
    ) private view {
        uint256 actualBurnedLX = fixture.tokenX().balanceOf(user.userAddress);
        uint256 actualBurnedLY = fixture.tokenY().balanceOf(user.userAddress);

        uint256 expectedBurnedLX = (user.mintedLX * user.burnRate / 100) * user.userScalerEnd / user.userScalerStart;
        uint256 expectedBurnedLY = (user.mintedLY * user.burnRate / 100) * user.userScalerEnd / user.userScalerStart;

        assertApproxEqRel(
            actualBurnedLX,
            expectedBurnedLX,
            TOLERANCE_1,
            string.concat('user ', vm.toString(user.userAddress), ' burnLX mismatch')
        );
        assertApproxEqRel(
            actualBurnedLY,
            expectedBurnedLY,
            TOLERANCE_1,
            string.concat('user ', vm.toString(user.userAddress), ' burnLY mismatch')
        );
    }

    function testBorrowLProtocolFee() public {
        user1Params.burnRate = 100;
        user2Params.burnRate = 100;
        borrower1Params.borrowedL = 30e18;
        borrower1Params.collateralY = type(uint112).max;

        uint256[2] memory durations = [uint256(365 days), uint256(365 days)];

        user1Params = interestFixture.mintHelper(user1Params);

        borrower1Params = interestFixture.borrowLiquidityHelper(borrower1Params);

        interestFixture.warpAndSkim(durations[0]);

        uint256 currentBorrowLScaler = fixture.computeScalerHelper(BORROW_L);
        uint256 expectedBorrowLAssets = borrower1Params.borrowedL * currentBorrowLScaler / RAY;
        uint256 expectedBorrowLInterest = expectedBorrowLAssets - borrower1Params.borrowedL;

        uint256 expectedProtocolFeeAssets = expectedBorrowLInterest * Interest.LENDING_FEE_RATE / 100;

        // note: we need to use depositL scaler here to calculate protocol fee shares because protocol fee is minted in depositL
        uint256 currentDepositLScaler = fixture.computeScalerHelper(DEPOSIT_L); // very important not using this exposed_sharesToAssetsScaler(BORROW_L); because it returns old scaler
        uint256 expectedProtocolFeeShares = expectedProtocolFeeAssets * RAY / currentDepositLScaler;

        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(fixture.factory().feeTo()),
            expectedProtocolFeeShares,
            'protocol fee should match'
        );
    }
}
