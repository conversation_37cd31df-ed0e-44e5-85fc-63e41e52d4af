// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {Interest} from 'contracts/libraries/Interest.sol';
import {
    SAT_PERCENTAGE_DELTA_4_WAD,
    SAT_PERCENTAGE_DELTA_5_WAD,
    SAT_PERCENTAGE_DELTA_6_WAD,
    SAT_PERCENTAGE_DELTA_7_WAD,
    SAT_PERCENTAGE_DELTA_8_WAD,
    SAT_PERCENTAGE_DELTA_DEFAULT_WAD
} from 'contracts/libraries/constants.sol';

contract InterestUnitTests is Test {
    function testMutateUtilizationAtPenaltyThreshold() public pure {
        uint256 maxSatInWads = 0.85e18; // PENALTY_SATURATION_PERCENT_IN_WAD
        uint256 utilization = 0.7e18;
        uint256 result = Interest.mutateUtilizationForSaturation(utilization, maxSatInWads);
        assertEq(result, utilization, 'Utilization should remain unchanged below threshold');
    }

    function testMutateUtilizationAbovePenaltyThreshold() public pure {
        uint256 maxSatInWads = 0.9e18;
        uint256 utilization = 0.7e18;
        uint256 result = Interest.mutateUtilizationForSaturation(utilization, maxSatInWads);
        assertEq(result, 0.8e18, 'Utilization should adjust correctly above threshold');
    }

    function testMutateUtilizationAtMaxSaturation() public pure {
        uint256 maxSatInWads = 0.95e18; // MAX_SATURATION_PERCENT_IN_WAD
        uint256 utilization = 0.7e18;
        uint256 result = Interest.mutateUtilizationForSaturation(utilization, maxSatInWads);
        assertEq(result, 0.9e18, 'Utilization should be same as max utilization percentage'); // MAX_UTILIZATION_PERCENT_IN_WAD
    }

    // Test that getUtilizationsInWads properly uses saturation data to adjust utilization
    function testGetUtilizationsInWadsWithSaturation() public pure {
        uint128[6] memory assets = [
            uint128(1000e18), // DEPOSIT_L
            uint128(500e18), // DEPOSIT_X
            uint128(500e18), // DEPOSIT_Y
            uint128(700e18), // BORROW_L
            uint128(200e18), // BORROW_X
            uint128(200e18) // BORROW_Y
        ];

        uint256 reservesX = 100e18;
        uint256 reservesY = 100e18;
        uint256 zeroSaturation = 0; // 0%

        uint256[3] memory utilizationsWithZeroSaturation =
            Interest.getUtilizationsInWads(assets, reservesX, reservesY, zeroSaturation);
        assertEq(utilizationsWithZeroSaturation[0], 0.7e18, 'L utilization should be greater than 0');
        assertEq(utilizationsWithZeroSaturation[1], 333_333_333_333_333_334, 'X utilization should be greater than 0');
        assertEq(utilizationsWithZeroSaturation[2], 333_333_333_333_333_334, 'Y utilization should be greater than 0');

        uint256[6] memory saturationPercentages = [
            SAT_PERCENTAGE_DELTA_4_WAD,
            SAT_PERCENTAGE_DELTA_5_WAD,
            SAT_PERCENTAGE_DELTA_6_WAD,
            SAT_PERCENTAGE_DELTA_7_WAD,
            SAT_PERCENTAGE_DELTA_8_WAD,
            SAT_PERCENTAGE_DELTA_DEFAULT_WAD
        ];

        for (uint256 i = 0; i < saturationPercentages.length; i++) {
            uint256 currentSaturation = saturationPercentages[i];

            uint256[3] memory utilizationsWithSaturation =
                Interest.getUtilizationsInWads(assets, reservesX, reservesY, currentSaturation);
            // L utilization should be higher with saturation
            assertGt(
                utilizationsWithSaturation[0],
                utilizationsWithZeroSaturation[0],
                'L utilization should be higher with saturation'
            );

            // X and Y utilizations should remain the same (saturation only affects L)
            assertEq(
                utilizationsWithSaturation[1],
                utilizationsWithZeroSaturation[1],
                'X utilization should be unchanged with saturation'
            );
            assertEq(
                utilizationsWithSaturation[2],
                utilizationsWithZeroSaturation[2],
                'Y utilization should be unchanged with saturation'
            );
        }
    }
}
