// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.25;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {FIRST_DEBT_TOKEN} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Interest} from 'contracts/libraries/Interest.sol';

import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y,
    BORROW_L,
    BORROW_X,
    BORROW_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Q72} from 'contracts/libraries/constants.sol';

import {getExpectedInterest, getExpectedInterestInWad, getTickAtPrice} from 'test/shared/utilities.sol';

contract InterestXYSpecTests is Test {
    using MathLib for uint256;

    using Interest for uint128[6];

    uint256 constant TOLERANCE_1 = 1;
    uint112 constant DEFAULT_RESERVE = 100e18;

    uint128[6] private startingAssets;
    uint128[6] private expectedAssets;

    int16 private defaultTick;
    uint256 private defaultReserveXAtTick;
    uint256 private defaultReserveYAtTick;

    function setUp() public {
        // zero out the contract state
        startingAssets = [DEFAULT_RESERVE, 0, 0, 0, 0, 0];
        expectedAssets = [uint128(0), 0, 0, 0, 0, 0];
        defaultTick = getTickAtPrice(DEFAULT_RESERVE, DEFAULT_RESERVE);

        (defaultReserveXAtTick, defaultReserveYAtTick) = Interest.getReservesAtTick(DEFAULT_RESERVE, defaultTick);
    }

    function testZeroBorrowSharesInterestAccrual() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 30 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(0), uint112(0), uint112(0), uint112(0), uint112(0), uint112(0)],
            satPercentageInWads: 0
        });

        uint256 interestPortionXForL;
        uint256 interestPortionYForL;
        (startingAssets, interestPortionXForL, interestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);
        assertEq(interestPortionXForL, 0, 'Interest portion should be 0 for zero shares');
        assertEq(interestPortionYForL, 0, 'Interest portion should be 0 for zero shares');
    }

    function testBorrowedXInterestAllocationForL() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        // store current borrowed X assets
        uint256 initialBorrowedXAssets = uint128(Math.mulDiv(params.shares[BORROW_X], 120, 100));
        uint256 initialDepositedXAssets = 0;

        // increase starting borrow assets by 20%
        startingAssets[BORROW_X] = uint128(Math.mulDiv(params.shares[BORROW_X], 120, 100));

        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_X, params, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXInterestAccrualFromXAndLWithInitialScalerRAY() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 100e18, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        // initial Scaler starting from RAY for both borrow and deposit
        uint128 initialBorrowedXAssets = uint128(params.shares[BORROW_X]);
        uint128 initialDepositedXAssets = uint128(params.shares[DEPOSIT_X]);

        startingAssets[BORROW_X] = initialBorrowedXAssets;
        startingAssets[DEPOSIT_X] = initialDepositedXAssets;
        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_X, params, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXInterestAccrualFromXAndLWithInitialScalerIncreased() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 100e18, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedXAssets = uint128(Math.mulDiv(params.shares[BORROW_X], 120, 100));
        uint128 initialDepositedXAssets = uint128(Math.mulDiv(params.shares[DEPOSIT_X], 120, 100));

        startingAssets[BORROW_X] = initialBorrowedXAssets;
        startingAssets[DEPOSIT_X] = initialDepositedXAssets;

        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_X, params, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXInterestFromXAndLGrowthWithInitialScalerRAY() public {
        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 200e18, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        uint128 initialBorrowedXAssets = uint128(interestParams.shares[BORROW_X]);
        uint128 initialDepositedXAssets = uint128(interestParams.shares[DEPOSIT_X]);

        startingAssets[BORROW_X] = initialBorrowedXAssets;
        startingAssets[DEPOSIT_X] = initialDepositedXAssets;

        // Exercise function under test.
        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_X, interestParams, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXInterestFromXAndLGrowthWithInitialScalerIncreased() public {
        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 200e18, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        uint128 initialBorrowedXAssets = uint128(Math.mulDiv(interestParams.shares[BORROW_X], 120, 100));
        uint128 initialDepositedXAssets = uint128(Math.mulDiv(interestParams.shares[DEPOSIT_X], 120, 100));

        startingAssets[BORROW_X] = initialBorrowedXAssets;
        startingAssets[DEPOSIT_X] = initialDepositedXAssets;

        // Exercise function under test.
        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_X, interestParams, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowedXInterestFromLWithZeroDurationAndZeroInterestAccrual() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 0 seconds, // no interest accrual
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        uint256 interestXForLP;
        uint256 interestYForLP;
        (startingAssets, interestXForLP, interestYForLP,) = startingAssets.accrueInterestWithAssets(params);

        uint256 expectedInterestXPortionForLP = 0;
        uint256 expectedInterestYPortionForLP = 0;

        assertEq(interestXForLP, expectedInterestXPortionForLP, 'interestXForLP should match');
        assertEq(interestYForLP, expectedInterestYPortionForLP, 'interestYForLP should match');
    }

    function testBorrowedXInterestAccrualFromLWithZeroDuration() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 0 seconds, // no interest accrual
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        uint256 interestXForLP;
        uint256 interestYForLP;
        (startingAssets, interestXForLP, interestYForLP,) = startingAssets.accrueInterestWithAssets(params);

        uint256 expectedInterestXPortionForLP = 0;
        uint256 expectedInterestYPortionForLP = 0;

        assertEq(interestXForLP, expectedInterestXPortionForLP, 'interestXForLP should match');
        assertEq(interestYForLP, expectedInterestYPortionForLP, 'interestYForLP should match');
    }

    function testZeroInterestWhenBorrowedXAndDepositedL() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 5e18, 0, 0],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        startingAssets[BORROW_L] = 12e18;

        uint256 interestXForLP;
        uint256 interestYForLP;
        (startingAssets, interestXForLP, interestYForLP,) = startingAssets.accrueInterestWithAssets(params);

        assertEq(interestXForLP, 0);
        assertEq(interestYForLP, 0);
    }

    function testBorrowXInterestFromLGrowthWithInitialScalerRAY() public {
        uint256 borrowedXShares = 1.5e18;

        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, uint112(borrowedXShares), 0],
            satPercentageInWads: 0
        });

        uint128 initialBorrowedXAssets = uint128(interestParams.shares[BORROW_X]);
        uint128 initialDepositedXAssets = uint128(interestParams.shares[DEPOSIT_X]);

        startingAssets[BORROW_X] = interestParams.shares[BORROW_X];

        // Exercise function under test.
        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_X, interestParams, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXInterestFromLGrowthWithInitialScalerIncreased() public {
        uint256 borrowedXShares = 1.5e18;

        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, uint112(borrowedXShares), 0],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedXAssets = uint128(Math.mulDiv(interestParams.shares[BORROW_X], 120, 100));
        uint128 initialDepositedXAssets = uint128(Math.mulDiv(interestParams.shares[DEPOSIT_X], 120, 100));

        startingAssets[BORROW_X] = initialBorrowedXAssets;

        // Exercise function under test.
        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_X, interestParams, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXInterestFromLGrowthWithExtremeInterestAmounts() public {
        uint112 EXTREME_RESERVE_X = type(uint112).max;

        Interest.AccrueInterestParams memory params;

        params.duration = 100 * 365 days; // duration 100 years
        params.lendingStateTick = getTickAtPrice(EXTREME_RESERVE_X, DEFAULT_RESERVE);
        params.adjustedActiveLiquidity = DEFAULT_RESERVE;
        params.shares[DEPOSIT_L] = uint112(Math.sqrt(uint256(EXTREME_RESERVE_X) * DEFAULT_RESERVE));
        params.shares[BORROW_X] = EXTREME_RESERVE_X;

        startingAssets[DEPOSIT_L] = params.shares[DEPOSIT_L];
        startingAssets[BORROW_X] = params.shares[BORROW_X];

        (uint128[6] memory newAssets,,,) = Interest.accrueInterestWithAssets(startingAssets, params);
        assertEq(newAssets[BORROW_X], type(uint128).max, 'Only the max of uint128 can be accrued to x');
    }

    function testReserveXAtLendingTickWithInitialScalerRay() public view {
        Interest.AccrueInterestParams memory accrueInterestParams = Interest.AccrueInterestParams({
            duration: 1 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        uint128 depositedLAssets = accrueInterestParams.shares[DEPOSIT_L];
        uint128 borrowedLAssets = accrueInterestParams.shares[BORROW_L];
        int16 lendingStateTick = 2238;

        (uint256 reserveXAtLendingTick,) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        uint256 sqrtPriceAtLendingStateTickInQ72 = TickMath.getSqrtPriceAtTick(lendingStateTick);

        uint256 expectedReserveX =
            Math.mulDiv(accrueInterestParams.shares[DEPOSIT_L], sqrtPriceAtLendingStateTickInQ72, Q72);

        assertGt(reserveXAtLendingTick, defaultReserveXAtTick, 'reserveXAtLendingTick should be greater');
        assertEq(reserveXAtLendingTick, expectedReserveX, 'reserveXAtLendingTick should match');
    }

    function testReserveXAtLendingTickShouldBeGreaterThanReserveXWithInitialRaisedScaler() public view {
        Interest.AccrueInterestParams memory accrueInterestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        uint128 depositedLAssets = uint128(accrueInterestParams.shares[DEPOSIT_L] * 120 / 100);
        uint128 borrowedLAssets = uint128(accrueInterestParams.shares[BORROW_L] * 130 / 100);
        int16 lendingStateTick = 2238;

        (uint256 reserveXAtLendingTick,) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        uint256 sqrtPriceAtLendingStateTickInQ72 = TickMath.getSqrtPriceAtTick(lendingStateTick);

        uint256 activeLiquidityAssets = depositedLAssets - borrowedLAssets;

        uint256 expectedReserveX = Math.mulDiv(activeLiquidityAssets, sqrtPriceAtLendingStateTickInQ72, Q72);

        assertGt(reserveXAtLendingTick, defaultReserveXAtTick, 'reserveXAtLendingTick should be greater');
        assertEq(reserveXAtLendingTick, expectedReserveX, 'reserveXAtLendingTick should match');
    }

    function testReserveXAtLendingTickShouldBeLessThanReserveXWithInitialRaisedScaler() public view {
        Interest.AccrueInterestParams memory accrueInterestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 5e18, 0],
            satPercentageInWads: 0
        });

        uint128 depositedLAssets = uint128(accrueInterestParams.shares[DEPOSIT_L] * 120 / 100);
        uint128 borrowedLAssets = uint128(accrueInterestParams.shares[BORROW_L] * 130 / 100);
        int16 lendingStateTick = -2238;

        (uint256 reserveXAtLendingTick,) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        uint256 sqrtPriceAtLendingStateTickInQ72 = TickMath.getSqrtPriceAtTick(lendingStateTick);

        uint256 activeLiquidityAssets = depositedLAssets - borrowedLAssets;

        uint256 expectedReserveX = Math.mulDiv(activeLiquidityAssets, sqrtPriceAtLendingStateTickInQ72, Q72);

        assertLt(reserveXAtLendingTick, defaultReserveXAtTick, 'reserveXAtLendingTick should be less than reserveX');
        assertEq(reserveXAtLendingTick, expectedReserveX, 'reserveXAtLendingTick should match');
    }

    function testBorrowXAccrueInterestWithReservesAtLendingTick() public {
        Interest.AccrueInterestParams memory params;
        int16 lendingStateTick = 224;

        params.duration = 1 hours;
        params.lendingStateTick = lendingStateTick;
        params.adjustedActiveLiquidity = DEFAULT_RESERVE;
        params.shares[DEPOSIT_L] = 100e18;
        params.shares[DEPOSIT_X] = 0;
        params.shares[DEPOSIT_Y] = 0;
        params.shares[BORROW_L] = 0;
        params.shares[BORROW_X] = 5e18;
        params.shares[BORROW_Y] = 0;

        uint256 initialBorrowedXAssets = uint256(params.shares[BORROW_X]);
        uint256 initialDepositedXAssets = uint256(params.shares[DEPOSIT_X]);

        startingAssets[BORROW_X] = uint128(initialBorrowedXAssets);

        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_X, params, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testBorrowXAccrueInterestWithReservesAtLendingTickWithRaisedScaler() public {
        Interest.AccrueInterestParams memory params;
        int16 lendingStateTick = 224;
        params.duration = 1 hours;
        params.lendingStateTick = lendingStateTick;
        params.adjustedActiveLiquidity = DEFAULT_RESERVE;
        params.shares[DEPOSIT_L] = 100e18;
        params.shares[DEPOSIT_L] = DEFAULT_RESERVE;
        params.shares[DEPOSIT_X] = 0;
        params.shares[DEPOSIT_Y] = 0;
        params.shares[BORROW_L] = 0;
        params.shares[BORROW_X] = 5e18;
        params.shares[BORROW_Y] = 0;

        uint128 initialBorrowedXAssets = uint128(params.shares[BORROW_X] * 130 / 100);
        uint128 initialDepositedXAssets = 0;

        startingAssets[BORROW_X] = initialBorrowedXAssets;
        uint256 actualInterestPortionXForL;
        (startingAssets, actualInterestPortionXForL,,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_X, params, initialBorrowedXAssets, initialDepositedXAssets, actualInterestPortionXForL
        );
    }

    function testZeroBorrowYSharesInterestAccrual() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 30 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 0],
            satPercentageInWads: 0
        });

        uint256 interestPortionXForL;
        uint256 interestPortionYForL;
        (startingAssets, interestPortionXForL, interestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);
        assertEq(interestPortionXForL, 0, 'Interest portion should be 0 for zero shares');
        assertEq(interestPortionYForL, 0, 'Interest portion should be 0 for zero shares');
    }

    function testBorrowedYInterestAllocationForL() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedYAssets = uint128(Math.mulDiv(params.shares[BORROW_Y], 120, 100));
        uint128 initialDepositedYAssets = uint128(Math.mulDiv(params.shares[DEPOSIT_Y], 120, 100));

        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_Y, params, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestAccrualFromYAndLWithInitialScalerRAY() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 100e18, 100e18, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        // initial Scaler starting from RAY for both borrow and deposit
        uint128 initialBorrowedYAssets = uint128(params.shares[BORROW_Y]);
        uint128 initialDepositedYAssets = uint128(params.shares[DEPOSIT_Y]);

        startingAssets[BORROW_Y] = initialBorrowedYAssets;
        startingAssets[DEPOSIT_Y] = initialDepositedYAssets;

        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_Y, params, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestAccrualFromXAndLWithInitialScalerIncreased() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 100e18, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedYAssets = uint128(Math.mulDiv(params.shares[BORROW_Y], 120, 100));
        uint128 initialDepositedYAssets = uint128(Math.mulDiv(params.shares[DEPOSIT_Y], 120, 100));

        startingAssets[BORROW_Y] = initialBorrowedYAssets;
        startingAssets[DEPOSIT_Y] = initialDepositedYAssets;

        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_Y, params, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestFromYAndLGrowthWithInitialScalerRAY() public {
        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 200e18, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        uint128 initialBorrowedYAssets = uint128(interestParams.shares[BORROW_Y]);
        uint128 initialDepositedYAssets = uint128(interestParams.shares[DEPOSIT_Y]);

        startingAssets[DEPOSIT_Y] = initialDepositedYAssets;
        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        // Exercise function under test.
        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_Y, interestParams, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestFromYAndLGrowthWithInitialScalerIncreased() public {
        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 200e18, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedYAssets = uint128(Math.mulDiv(interestParams.shares[BORROW_Y], 120, 100));
        uint128 initialDepositedYAssets = uint128(Math.mulDiv(interestParams.shares[DEPOSIT_Y], 120, 100));

        startingAssets[BORROW_Y] = initialBorrowedYAssets;
        startingAssets[DEPOSIT_Y] = initialDepositedYAssets;

        // Exercise function under test.
        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_Y, interestParams, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestFromL() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedYAssets = uint128(Math.mulDiv(params.shares[BORROW_Y], 120, 100));
        uint128 initialDepositedYAssets = uint128(Math.mulDiv(params.shares[DEPOSIT_Y], 120, 100));

        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);

        verifyBorrowInterestAccrual(
            BORROW_Y, params, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowedYInterestFromLWithZeroDurationAndZeroInterestAccrual() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 0 seconds, // no interest accrual
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });
        uint256 interestXForLP;
        uint256 interestYForLP;
        (startingAssets, interestXForLP, interestYForLP,) = startingAssets.accrueInterestWithAssets(params);

        uint256 expectedInterestXPortionForLP = 0;
        uint256 expectedInterestYPortionForLP = 0;

        assertEq(
            interestXForLP,
            expectedInterestXPortionForLP - protocolFee(expectedInterestXPortionForLP),
            'interestXForLP should match'
        );
        assertEq(
            interestYForLP,
            expectedInterestYPortionForLP - protocolFee(expectedInterestYPortionForLP),
            'interestYForLP should match'
        );
    }

    function testZeroInterestWhenBorrowedYAndDepositedL() public {
        Interest.AccrueInterestParams memory params = Interest.AccrueInterestParams({
            duration: 1 hours,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 5e18, 0, 0],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        startingAssets[BORROW_L] = 12e18;
        uint256 interestXForLP;
        uint256 interestYForLP;
        (startingAssets, interestXForLP, interestYForLP,) = startingAssets.accrueInterestWithAssets(params);

        assertEq(interestXForLP, 0);
        assertEq(interestYForLP, 0);
    }

    function testBorrowYInterestFromLGrowthWithInitialScalerRAY() public {
        uint256 borrowedYShares = 1.5e18;

        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, uint112(borrowedYShares)],
            satPercentageInWads: 0
        });

        uint128 initialBorrowedYAssets = uint128(interestParams.shares[BORROW_Y]);
        uint128 initialDepositedYAssets = uint128(interestParams.shares[DEPOSIT_Y]);

        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        // Exercise function under test.
        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_Y, interestParams, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestFromLGrowthWithInitialScalerIncreased() public {
        uint256 borrowedYShares = 1.5e18;

        Interest.AccrueInterestParams memory interestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: defaultTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, uint112(borrowedYShares)],
            satPercentageInWads: 0
        });

        // Increase liquidity scaler by 20%.
        uint128 initialBorrowedYAssets = uint128(Math.mulDiv(interestParams.shares[BORROW_Y], 120, 100));
        uint128 initialDepositedYAssets = uint128(Math.mulDiv(interestParams.shares[DEPOSIT_Y], 120, 100));

        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        // Exercise function under test.
        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(interestParams);

        verifyBorrowInterestAccrual(
            BORROW_Y, interestParams, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYInterestFromLGrowthWithExtremeInterestAmounts() public {
        uint112 EXTREME_RESERVE_Y = type(uint112).max;

        Interest.AccrueInterestParams memory params;

        params.duration = 100 * 365 days; // duration 100 years
        params.lendingStateTick = getTickAtPrice(DEFAULT_RESERVE, EXTREME_RESERVE_Y);
        params.adjustedActiveLiquidity = Math.sqrt(uint256(EXTREME_RESERVE_Y) * DEFAULT_RESERVE);
        params.shares[DEPOSIT_L] = uint112(Math.sqrt(uint256(EXTREME_RESERVE_Y) * DEFAULT_RESERVE));
        params.shares[BORROW_Y] = EXTREME_RESERVE_Y;

        uint128 initialBorrowYAssets = uint128(params.shares[BORROW_Y]);

        startingAssets[DEPOSIT_L] = params.shares[DEPOSIT_L];
        startingAssets[BORROW_Y] = initialBorrowYAssets;

        (uint128[6] memory newAssets,,,) = Interest.accrueInterestWithAssets(startingAssets, params);
        assertEq(newAssets[BORROW_Y], type(uint128).max, 'Only the max of uint128 can be accrued to y');
    }

    function testReserveYAtLendingTickWithInitialScalerRay() public pure {
        int16 lendingStateTick = 2238;

        Interest.AccrueInterestParams memory accrueInterestParams = Interest.AccrueInterestParams({
            duration: 1 days,
            lendingStateTick: lendingStateTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        uint128 depositedLAssets = accrueInterestParams.shares[DEPOSIT_L];
        uint128 borrowedLAssets = accrueInterestParams.shares[BORROW_L];

        (, uint256 reserveYAtLendingTick) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        uint256 maxSqrtPrice = TickMath.getSqrtPriceAtTick(lendingStateTick + 1);
        uint256 minSqrtPrice = TickMath.getSqrtPriceAtTick(lendingStateTick);

        uint256 lowBoundExpectedReserveYWithMaxPrice =
            Math.mulDiv(depositedLAssets - borrowedLAssets, Q72, maxSqrtPrice);
        uint256 highBoundExpectedReserveYWithMinPrice =
            Math.mulDiv(depositedLAssets - borrowedLAssets, Q72, minSqrtPrice);
        assertGe(
            reserveYAtLendingTick,
            lowBoundExpectedReserveYWithMaxPrice,
            'reserveYAtLendingTick should be greater than the low bound'
        );
        assertLe(
            reserveYAtLendingTick,
            highBoundExpectedReserveYWithMinPrice,
            'reserveYAtLendingTick should be less than the high bound'
        );

        assertApproxEqRel(
            reserveYAtLendingTick, lowBoundExpectedReserveYWithMaxPrice, 100, 'reserveYAtLendingTick should match'
        );
    }

    function testReserveYAtLendingTickShouldBeGreaterThanReserveYWithInitialRaisedScaler() public pure {
        int16 lendingStateTick = 2238;
        Interest.AccrueInterestParams memory accrueInterestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: lendingStateTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });
        uint128 depositedLAssets = uint128(accrueInterestParams.shares[DEPOSIT_L] * 120 / 100);
        uint128 borrowedLAssets = uint128(accrueInterestParams.shares[BORROW_L] * 130 / 100);

        (, uint256 reserveYAtLendingTick) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        uint256 maxSqrtPrice = TickMath.getSqrtPriceAtTick(lendingStateTick + 1);
        uint256 minSqrtPrice = TickMath.getSqrtPriceAtTick(lendingStateTick);

        uint256 lowBoundExpectedReserveYWithMaxPrice =
            Math.mulDiv(depositedLAssets - borrowedLAssets, Q72, maxSqrtPrice);
        uint256 highBoundExpectedReserveYWithMinPrice =
            Math.mulDiv(depositedLAssets - borrowedLAssets, Q72, minSqrtPrice);
        assertGe(
            reserveYAtLendingTick,
            lowBoundExpectedReserveYWithMaxPrice,
            'reserveYAtLendingTick should be greater than the low bound'
        );
        assertLe(
            reserveYAtLendingTick,
            highBoundExpectedReserveYWithMinPrice,
            'reserveYAtLendingTick should be less than the high bound'
        );
        assertApproxEqRel(
            reserveYAtLendingTick, lowBoundExpectedReserveYWithMaxPrice, 100, 'reserveYAtLendingTick should match'
        );
    }

    function testReserveYAtLendingTickShouldBeLessThanReserveYWithInitialRaisedScaler() public pure {
        int16 lendingStateTick = -2238;
        Interest.AccrueInterestParams memory accrueInterestParams = Interest.AccrueInterestParams({
            duration: 365 days,
            lendingStateTick: lendingStateTick,
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [uint112(100e18), 0, 0, 0, 0, 5e18],
            satPercentageInWads: 0
        });

        uint128 depositedLAssets = uint128(accrueInterestParams.shares[DEPOSIT_L] * 120 / 100);
        uint128 borrowedLAssets = uint128(accrueInterestParams.shares[BORROW_L] * 130 / 100);

        (, uint256 reserveYAtLendingTick) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        uint256 maxSqrtPrice = TickMath.getSqrtPriceAtTick(lendingStateTick + 1);
        uint256 minSqrtPrice = TickMath.getSqrtPriceAtTick(lendingStateTick);

        uint256 lowBoundExpectedReserveYWithMaxPrice =
            Math.mulDiv(depositedLAssets - borrowedLAssets, Q72, maxSqrtPrice);
        uint256 highBoundExpectedReserveYWithMinPrice =
            Math.mulDiv(depositedLAssets - borrowedLAssets, Q72, minSqrtPrice);
        assertGe(
            reserveYAtLendingTick,
            lowBoundExpectedReserveYWithMaxPrice,
            'reserveYAtLendingTick should be greater than the low bound'
        );
        assertLe(
            reserveYAtLendingTick,
            highBoundExpectedReserveYWithMinPrice,
            'reserveYAtLendingTick should be less than the high bound'
        );

        assertApproxEqRel(
            reserveYAtLendingTick, lowBoundExpectedReserveYWithMaxPrice, 100, 'reserveYAtLendingTick should match'
        );
    }

    function testBorrowYAccrueInterestWithReservesAtLendingTick() public {
        Interest.AccrueInterestParams memory params;

        int16 lendingStateTick = 224;

        params.duration = 1 hours;
        params.lendingStateTick = lendingStateTick;
        params.shares[DEPOSIT_L] = 100e18;
        params.adjustedActiveLiquidity = DEFAULT_RESERVE;
        params.shares[DEPOSIT_L] = DEFAULT_RESERVE;
        params.shares[DEPOSIT_X] = 0;
        params.shares[DEPOSIT_Y] = 0;
        params.shares[BORROW_L] = 0;
        params.shares[BORROW_X] = 0;
        params.shares[BORROW_Y] = 5e18;

        uint128 initialBorrowedYAssets = uint128(params.shares[BORROW_Y]);
        uint128 initialDepositedYAssets = uint128(params.shares[DEPOSIT_Y]);

        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);
        verifyBorrowInterestAccrual(
            BORROW_Y, params, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function testBorrowYAccrueInterestWithReservesAtLendingTickWithRaisedScaler() public {
        Interest.AccrueInterestParams memory params;
        int16 lendingStateTick = 224;

        params.duration = 1 hours;
        params.lendingStateTick = lendingStateTick;
        params.adjustedActiveLiquidity = DEFAULT_RESERVE;
        params.shares[BORROW_X] = 0;
        params.shares[BORROW_Y] = 5e18;
        params.shares[BORROW_L] = 0;
        params.shares[DEPOSIT_X] = 0;
        params.shares[DEPOSIT_Y] = 0;
        params.shares[DEPOSIT_L] = DEFAULT_RESERVE;

        uint128 initialBorrowedYAssets = uint128(params.shares[BORROW_Y] * 130 / 100);
        uint128 initialDepositedYAssets = 0;

        startingAssets[BORROW_Y] = initialBorrowedYAssets;

        uint256 actualInterestPortionYForL;
        (startingAssets,, actualInterestPortionYForL,) = startingAssets.accrueInterestWithAssets(params);
        verifyBorrowInterestAccrual(
            BORROW_Y, params, initialBorrowedYAssets, initialDepositedYAssets, actualInterestPortionYForL
        );
    }

    function verifyBorrowInterestAccrual(
        uint256 borrowTokenType,
        Interest.AccrueInterestParams memory interestParams,
        uint256 initialBorrowedAssets,
        uint256 initialDepositedAssets,
        uint256 actualInterestPortionForL
    ) private view {
        uint256 expectedInterestInWad;
        {
            (uint256 averageReserveX, uint256 averageReserveY) = Interest.getReservesAtTick(
                interestParams.shares[DEPOSIT_L] - interestParams.shares[BORROW_L], interestParams.lendingStateTick
            );
            uint256 averageReserves = (borrowTokenType == BORROW_X) ? averageReserveX : averageReserveY;
            expectedInterestInWad = getExpectedInterestInWad(
                interestParams.duration, initialBorrowedAssets, initialDepositedAssets + averageReserves
            );
        }

        uint256 actualBorrowInterestGrowth = startingAssets[borrowTokenType] - initialBorrowedAssets;

        // verify borrow interest
        assertEq(actualBorrowInterestGrowth, expectedInterestInWad / WAD, 'expectedBorrowInterest should match');

        uint256 _protocolFee = protocolFee(expectedInterestInWad / WAD);

        uint256 actualDepositInterestInWad =
            (startingAssets[borrowTokenType - FIRST_DEBT_TOKEN] - initialDepositedAssets) * WAD;

        uint256 expectedInterestPortionForLPInWad = expectedInterestInWad - actualDepositInterestInWad;

        //verify portion of L interest
        assertEq(
            actualInterestPortionForL,
            expectedInterestPortionForLPInWad / WAD - _protocolFee,
            'Interest for portion L should match'
        );
    }

    function protocolFee(
        uint256 interest
    ) public pure returns (uint256) {
        return interest * Interest.LENDING_FEE_RATE / 100;
    }
}
