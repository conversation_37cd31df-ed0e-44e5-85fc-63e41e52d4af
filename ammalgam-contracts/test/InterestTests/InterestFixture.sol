// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {Validation} from 'contracts/libraries/Validation.sol';
import {Interest} from 'contracts/libraries/Interest.sol';

import {TickMath} from 'contracts/libraries/TickMath.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {ITokenController} from 'contracts/interfaces/tokens/ITokenController.sol';
import {
    <PERSON><PERSON><PERSON><PERSON>_<PERSON>,
    BOR<PERSON><PERSON>_X,
    BOR<PERSON>W_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';

import {TOLERANCE_1} from 'test/utils/constants.sol';
import {getExpectedInterestInWad} from 'test/shared/utilities.sol';

struct SharesAndAssets {
    uint256 depositLShares;
    uint256 depositXShares;
    uint256 depositYShares;
    uint256 borrowLShares;
    uint256 borrowXShares;
    uint256 borrowYShares;
    uint256 reserveXAssets;
    uint256 reserveYAssets;
    uint256 depositLAssets;
    uint256 depositXAssets;
    uint256 depositYAssets;
    uint256 borrowLAssets;
    uint256 borrowXAssets;
    uint256 borrowYAssets;
}

struct UserPosition {
    address userAddress;
    uint256 mintLXAssets;
    uint256 mintLYAssets;
    uint256 depositXAssets;
    uint256 depositYAssets;
    uint256 borrowLAssets;
    uint256 borrowXAssets;
    uint256 borrowYAssets;
}

struct RepayWithdrawPosition {
    address userAddress;
    uint256 repayBorrowXAssets;
    uint256 repayBorrowYAssets;
    uint256 repayBorrowLXAssets;
    uint256 repayBorrowLYAssets;
    uint256 withdrawXShares;
    uint256 withdrawYShares;
    uint256 burnLShares;
}

struct MintAndBurnInputParams {
    address userAddress;
    uint256 mintedL;
    uint256 mintedLX;
    uint256 mintedLY;
    uint256 burnRate;
    uint256 burnedL;
    uint256 burnedLX;
    uint256 burnedLY;
    uint256 userScalerStart;
    uint256 userScalerEnd;
}

struct DepositAndWithdrawInputParams {
    address userAddress;
    uint256 depositedXShares;
    uint256 depositedYShares;
    uint256 depositedXAssets;
    uint256 depositedYAssets;
    uint256 withdrawRate;
    uint256 withdrawXShares;
    uint256 withdrawYShares;
    uint256 userScalerStart;
    uint256 userScalerEnd;
}

struct BorrowAndRepayLiquidityInputParams {
    address userAddress;
    uint256 borrowedL;
    uint256 borrowedLX;
    uint256 borrowedLY;
    uint256 collateralX;
    uint256 collateralY;
    uint256 repayRate;
    uint256 repaidL;
    uint256 repaidLAssets;
    uint256 repaidLX;
    uint256 repaidLY;
}

struct BorrowAndRepayXYInputParams {
    address userAddress;
    uint256 borrowedL;
    uint256 borrowedXShares;
    uint256 borrowedYShares;
    uint256 borrowedXAssets;
    uint256 borrowedYAssets;
    uint256 borrowXScaler;
    uint256 borrowYScaler;
    uint256 collateralX;
    uint256 collateralY;
    uint256 repayRate;
    uint256 repaidXAssets;
    uint256 repaidYAssets;
    uint256 userScalerStart;
    uint256 userScalerEnd;
}

uint256 constant RAY = 10 ** 27;

contract InterestFixture is Test {
    using MathLib for uint256;

    FactoryPairTestFixture public fixture;
    IAmmalgamPair public pair;

    SharesAndAssets private pairExpectedState;

    constructor(
        FactoryPairTestFixture _fixture
    ) {
        fixture = _fixture;
        pair = _fixture.pair();
    }

    function createUserPosition(
        UserPosition memory position
    ) public {
        if (position.depositXAssets > 0 || position.depositYAssets > 0) {
            fixture.transferTokensTo(position.userAddress, position.depositXAssets, position.depositYAssets);
            fixture.depositFor(position.userAddress, position.depositXAssets, position.depositYAssets);
        }
        if (position.mintLXAssets > 0 && position.mintLYAssets > 0) {
            fixture.transferTokensTo(position.userAddress, position.mintLXAssets, position.mintLYAssets);
            fixture.mintFor(position.userAddress, position.mintLXAssets, position.mintLYAssets);
        }

        if (position.borrowLAssets > 0) {
            fixture.borrowLiquidityFor(position.userAddress, position.borrowLAssets);
        }
        if (position.borrowXAssets > 0 || position.borrowYAssets > 0) {
            fixture.borrowFor(position.userAddress, position.borrowXAssets, position.borrowYAssets);
        }
    }

    function repayWithdrawUserPosition(
        RepayWithdrawPosition memory position
    ) public {
        if (position.repayBorrowXAssets > 0 || position.repayBorrowYAssets > 0) {
            adjustTransferForCurrentBalance(
                position.userAddress, position.repayBorrowXAssets, position.repayBorrowYAssets
            );
            fixture.repayFor(position.userAddress, position.repayBorrowXAssets, position.repayBorrowYAssets);
        }

        if (position.repayBorrowLXAssets > 0 || position.repayBorrowLYAssets > 0) {
            adjustTransferForCurrentBalance(
                position.userAddress, position.repayBorrowLXAssets, position.repayBorrowLYAssets
            );
            fixture.repayLiquidityForNoEvent(
                position.userAddress, position.repayBorrowLXAssets, position.repayBorrowLYAssets
            );
        }
        if (position.withdrawXShares > 0 || position.withdrawYShares > 0) {
            fixture.withdrawFor(position.userAddress, position.withdrawXShares, position.withdrawYShares);
        }
        if (position.burnLShares > 0) {
            fixture.burnFor(position.userAddress, position.burnLShares);
        }
    }

    function adjustTransferForCurrentBalance(address userAddress, uint256 transferX, uint256 transferY) private {
        uint256 currentBalanceX = fixture.tokenX().balanceOf(userAddress);
        uint256 currentBalanceY = fixture.tokenY().balanceOf(userAddress);
        (transferX, transferY) = (
            (transferX > currentBalanceX ? transferX - currentBalanceX : 0),
            (transferY > currentBalanceY ? transferY - currentBalanceY : 0)
        );
        fixture.transferTokensTo(userAddress, transferX, transferY);
    }

    function mintHelper(
        MintAndBurnInputParams memory user
    ) public returns (MintAndBurnInputParams memory) {
        fixture.transferTokensTo(user.userAddress, user.mintedLX, user.mintedLY);
        user.mintedL = fixture.mintFor(user.userAddress, user.mintedLX, user.mintedLY);
        user.userScalerStart = fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_L);
        return (user);
    }

    function depositHelper(
        DepositAndWithdrawInputParams memory user
    ) public returns (DepositAndWithdrawInputParams memory) {
        fixture.transferTokensTo(user.userAddress, user.depositedXAssets, user.depositedYAssets);

        require(
            user.depositedXShares == 0 && user.depositedYShares == 0,
            'depositHelper: depositedShares must be 0 before deposit'
        );

        fixture.depositFor(user.userAddress, user.depositedXAssets, user.depositedYAssets);

        user.depositedXShares = fixture.pair().tokens(DEPOSIT_X).balanceOf(user.userAddress);
        user.depositedYShares = fixture.pair().tokens(DEPOSIT_Y).balanceOf(user.userAddress);

        return user;
    }

    function withdrawHelper(
        DepositAndWithdrawInputParams memory user
    ) public {
        user.withdrawXShares = user.depositedXShares * user.withdrawRate / 100;
        user.withdrawYShares = user.depositedYShares * user.withdrawRate / 100;

        fixture.withdrawForNoEvent(user.userAddress, user.withdrawXShares, user.withdrawYShares);
    }

    function burnHelper(
        MintAndBurnInputParams memory params
    ) public returns (MintAndBurnInputParams memory) {
        params.burnedL = params.mintedL * params.burnRate / 100;
        (params.burnedLX, params.burnedLY) = fixture.burnFor(params.userAddress, params.burnedL);

        params.userScalerEnd = fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_L);

        return (params);
    }

    function borrowHelper(
        BorrowAndRepayXYInputParams memory params
    ) public {
        if (params.collateralX > 0) {
            fixture.transferTokensTo(params.userAddress, params.collateralX, 0);
            fixture.depositFor(params.userAddress, params.collateralX, 0);
        }

        if (params.collateralY > 0) {
            fixture.transferTokensTo(params.userAddress, 0, params.collateralY);
            fixture.depositFor(params.userAddress, 0, params.collateralY);
        }

        fixture.borrowFor(params.userAddress, params.borrowedXAssets, params.borrowedYAssets);
    }

    function borrowLiquidityHelper(
        BorrowAndRepayLiquidityInputParams memory params
    ) public returns (BorrowAndRepayLiquidityInputParams memory) {
        if (params.collateralX > 0) {
            fixture.transferTokensTo(params.userAddress, params.collateralX, 0);
            fixture.depositFor(params.userAddress, params.collateralX, 0);
        }

        if (params.collateralY > 0) {
            fixture.transferTokensTo(params.userAddress, 0, params.collateralY);
            fixture.depositFor(params.userAddress, 0, params.collateralY);
        }

        (params.borrowedLX, params.borrowedLY) = fixture.borrowLiquidityFor(params.userAddress, params.borrowedL);

        return (params);
    }

    function repayHelper(
        BorrowAndRepayXYInputParams memory params
    ) public returns (uint256, uint256) {
        uint256 borrowXScaler = fixture.pair().exposed_sharesToAssetsScaler(BORROW_X);
        uint256 borrowYScaler = fixture.pair().exposed_sharesToAssetsScaler(BORROW_Y);

        uint256 borrowedXShares = fixture.pair().tokens(BORROW_X).balanceOf(params.userAddress);
        uint256 borrowedYShares = fixture.pair().tokens(BORROW_Y).balanceOf(params.userAddress);

        uint256 repayXShares = borrowedXShares * params.repayRate / 100;
        uint256 repayYShares = borrowedYShares * params.repayRate / 100;

        uint256 repaidXAssets = repayXShares * borrowXScaler / RAY;
        uint256 repaidYAssets = repayYShares * borrowYScaler / RAY;

        if (repayXShares > 0) {
            require(
                repaidXAssets >= params.borrowedXAssets,
                'repayXHelper: repaidXAssets should be greater than borrowedXAssets'
            );
        }

        if (repayYShares > 0) {
            require(
                repaidYAssets >= params.borrowedYAssets,
                'repayYHelper: repaidYAssets should be greater than borrowedYAssets'
            );
        }

        fixture.transferTokensTo(
            params.userAddress, repaidXAssets - params.borrowedXAssets, repaidYAssets - params.borrowedYAssets
        );
        fixture.repayForNoEvent(params.userAddress, repaidXAssets, repaidYAssets);

        return (repaidXAssets, repaidYAssets);
    }

    function repayAssetsHelper(
        BorrowAndRepayXYInputParams memory params
    ) public returns (uint256, uint256) {
        uint256 borrowedXShares = fixture.pair().tokens(BORROW_X).balanceOf(params.userAddress);
        uint256 borrowedYShares = fixture.pair().tokens(BORROW_Y).balanceOf(params.userAddress);

        uint256 repayXShares = borrowedXShares * params.repayRate / 100;
        uint256 repayYShares = borrowedYShares * params.repayRate / 100;

        IERC4626 ammXToken = IERC4626(address(fixture.pair().tokens(BORROW_X)));
        IERC4626 ammYToken = IERC4626(address(fixture.pair().tokens(BORROW_Y)));
        uint256 repaidXAssets = ammXToken.convertToAssets(repayXShares);
        uint256 repaidYAssets = ammYToken.convertToAssets(repayYShares);

        if (repayXShares > 0) {
            require(
                repaidXAssets >= params.borrowedXAssets,
                'repayXHelper: repaidXAssets should be greater than borrowedXAssets'
            );
        }

        if (repayYShares > 0) {
            require(
                repaidYAssets >= params.borrowedYAssets,
                'repayYHelper: repaidYAssets should be greater than borrowedYAssets'
            );
        }

        fixture.transferTokensTo(
            params.userAddress, repaidXAssets - params.borrowedXAssets, repaidYAssets - params.borrowedYAssets
        );
        fixture.repayForNoEvent(params.userAddress, repaidXAssets, repaidYAssets);

        return (repaidXAssets, repaidYAssets);
    }

    function repayLiquidityHelper(
        BorrowAndRepayLiquidityInputParams memory params
    ) public returns (BorrowAndRepayLiquidityInputParams memory) {
        uint256 borrowLScaler = fixture.pair().exposed_sharesToAssetsScaler(BORROW_L);
        uint256 scalerL = fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_L);

        (uint256 reserveX, uint256 reserveY,) = fixture.pair().getReserves();

        params.repaidL = params.borrowedL * params.repayRate / 100;
        uint256 repaidLAssets = params.repaidL * borrowLScaler / RAY;

        uint256 activeLiquidityAssets = getActiveLiquidityAssets(borrowLScaler, scalerL);
        params.repaidLX = repaidLAssets * reserveX / activeLiquidityAssets;
        params.repaidLY = repaidLAssets * reserveY / activeLiquidityAssets;

        fixture.transferTokensTo(params.userAddress, params.repaidLX, params.repaidLY);

        fixture.repayLiquidityFor(params.userAddress, params.repaidLX, params.repaidLY, params.repaidL);

        return (params);
    }

    function repayLiquidityAssetsHelper(
        BorrowAndRepayLiquidityInputParams memory params
    ) public returns (BorrowAndRepayLiquidityInputParams memory) {
        params.repaidL = params.borrowedL * params.repayRate / 100;

        (params.repaidLAssets, params.repaidLX, params.repaidLY) = getBorrowedLiquidityAssets(params.repaidL);

        fixture.transferTokensTo(params.userAddress, params.repaidLX, params.repaidLY);

        //fixture.repayLiquidityForNoEvent(params.userAddress, params.repaidLX, params.repaidLY, params.repaidL);
        fixture.repayLiquidityFor(params.userAddress, params.repaidLX, params.repaidLY, params.repaidL);

        return (params);
    }

    function convertBorrowedLSharesToAssets(
        uint256 shares
    ) public view returns (uint256) {
        return Math.mulDiv(shares, fixture.computeScalerHelper(BORROW_L), RAY);
    }

    function getBorrowedLiquidityAssets(
        uint256 repayLShares
    ) public view returns (uint256, uint256, uint256) {
        uint256 repayingLAssets = convertBorrowedLSharesToAssets(repayLShares);

        (uint256 _reserveX, uint256 _reserveY,) = fixture.pair().getReserves();

        uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();

        uint256 repayingLX = repayingLAssets * _reserveX / activeLiquidityAssets;
        uint256 repayingLY = repayingLAssets * _reserveY / activeLiquidityAssets;

        return (repayingLAssets, repayingLX, repayingLY);
    }

    function getActiveLiquidityAssets(uint256 borrowLScaler, uint256 scalerL) public view returns (uint256) {
        return uint112(
            (
                fixture.pair().tokens(DEPOSIT_L).totalSupply() * scalerL
                    - fixture.pair().tokens(BORROW_L).totalSupply() * borrowLScaler
            ) / RAY
        );
    }

    function getPairStates() public view returns (SharesAndAssets memory) {
        SharesAndAssets memory state;

        state.depositLShares = fixture.pair().tokens(DEPOSIT_L).totalSupply();
        state.depositXShares = fixture.pair().tokens(DEPOSIT_X).totalSupply();
        state.depositYShares = fixture.pair().tokens(DEPOSIT_Y).totalSupply();
        state.borrowLShares = fixture.pair().tokens(BORROW_L).totalSupply();
        state.borrowXShares = fixture.pair().tokens(BORROW_X).totalSupply();
        state.borrowYShares = fixture.pair().tokens(BORROW_Y).totalSupply();

        (state.reserveXAssets, state.reserveYAssets,) = fixture.pair().getReserves();
        uint128[6] memory totalAssets = fixture.pair().totalAssets();
        state.depositLAssets = totalAssets[DEPOSIT_L];
        state.depositXAssets = totalAssets[DEPOSIT_X];
        state.depositYAssets = totalAssets[DEPOSIT_Y];
        state.borrowLAssets = totalAssets[BORROW_L];
        state.borrowXAssets = totalAssets[BORROW_X];
        state.borrowYAssets = totalAssets[BORROW_Y];
        return state;
    }

    function warpForwardBy(
        uint256 duration
    ) public {
        fixture.mineBlock(block.number + Math.ceilDiv(duration, 12), block.timestamp + duration);
    }

    address empty = address(666);

    function warpAndSkim(
        uint256 duration
    ) public {
        (IERC20 x, IERC20 y) = fixture.pair().underlyingTokens();
        uint256 xBalance = x.balanceOf(address(pair));
        uint256 yBalance = y.balanceOf(address(pair));
        warpForwardBy(duration);
        fixture.pair().skim(empty);
        assertEq(x.balanceOf(empty), 0, 'empty address has x balance.');
        assertEq(y.balanceOf(empty), 0, 'empty address has y balance.');
        assertEq(xBalance, x.balanceOf(address(pair)), 'pair x balance has changed.');
        assertEq(yBalance, y.balanceOf(address(pair)), 'pair y balance has changed.');
    }

    function warpAndComputeInterestAssets(
        uint256 duration
    ) public returns (uint256) {
        SharesAndAssets memory currentPairState = getPairStates();
        warpAndSkim(duration);
        return getExpectedInterestInWad(duration, currentPairState.borrowLAssets, currentPairState.depositLAssets) / WAD;
    }

    function computeInterestAssetsGivenRate(
        uint256 prevInterestAssets,
        uint256 interestInWad
    ) public pure returns (uint256) {
        return (interestInWad == 0) ? prevInterestAssets : prevInterestAssets + interestInWad / WAD;
    }

    function verifyRepayAmountsAndRepay(
        address borrowerAddress,
        uint256 borrowLAssets,
        uint256 expectedBorrowLXAssets,
        uint256 expectedBorrowLYAssets
    ) public {
        verifyRepayAmountsAndRepay(borrowerAddress, borrowLAssets, expectedBorrowLXAssets, expectedBorrowLYAssets, 0);
    }

    function verifyRepayAmountsAndRepay(
        address borrowerAddress,
        uint256 borrowLAssets,
        uint256 expectedBorrowLXAssets,
        uint256 expectedBorrowLYAssets,
        uint256 expectedRemainingShares
    ) public {
        SharesAndAssets memory latestPairState = getPairStates();

        RepayWithdrawPosition memory repayBorrowPosition;
        repayBorrowPosition.userAddress = borrowerAddress;
        repayBorrowPosition.repayBorrowLXAssets = Math.ceilDiv(
            borrowLAssets * latestPairState.reserveXAssets,
            latestPairState.depositLAssets - latestPairState.borrowLAssets
        );
        repayBorrowPosition.repayBorrowLYAssets = Math.ceilDiv(
            borrowLAssets * latestPairState.reserveYAssets,
            latestPairState.depositLAssets - latestPairState.borrowLAssets
        );

        assertApproxEqAbs(
            repayBorrowPosition.repayBorrowLXAssets,
            expectedBorrowLXAssets,
            TOLERANCE_1,
            'borrowLX balance should match user balance'
        );
        assertApproxEqAbs(
            repayBorrowPosition.repayBorrowLYAssets,
            expectedBorrowLYAssets,
            TOLERANCE_1,
            'borrowLY balance should match user balance'
        );

        repayWithdrawUserPosition(repayBorrowPosition);

        assertApproxEqAbs(
            pair.tokens(BORROW_L).balanceOf(borrowerAddress),
            expectedRemainingShares,
            TOLERANCE_1,
            'borrow should have the expected remaining shares'
        );
    }
}
