// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {Interest} from 'contracts/libraries/Interest.sol';

import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {getCompoundRate, getTickAtPrice} from 'test/shared/utilities.sol';
import {LIQUIDITY_INTEREST_RATE_MAGNIFICATION} from 'contracts/libraries/constants.sol';

struct InterestInputParams {
    uint8 borrowTokenType;
    uint8 depositTokenType;
    uint256 duration;
    uint112 reserve_;
    uint256 borrowedShares;
    uint256 depositedShares;
}

contract InterestSpecTests is Test {
    using MathLib for uint256;
    using Math<PERSON>ib for uint128;

    uint256 constant ONE_YEAR_SECONDS = 31_536_000;
    uint256 constant TOLERANCE_1 = 1;

    using Interest for uint128[6];

    uint112 DEFAULT_RESERVE = 100e18;

    uint128[6] private startingAssets;

    Interest.AccrueInterestParams private accrueInterestParams;

    function setUp() public {
        accrueInterestParams = Interest.AccrueInterestParams({
            duration: 0,
            lendingStateTick: getTickAtPrice(DEFAULT_RESERVE, DEFAULT_RESERVE),
            adjustedActiveLiquidity: DEFAULT_RESERVE,
            shares: [DEFAULT_RESERVE, 0, 0, 0, 0, 0],
            satPercentageInWads: 0
        });

        startingAssets = [DEFAULT_RESERVE, 0, 0, 0, 0, 0];
    }

    function testOptimalBaseRateEqualsConstant() public pure {
        assertEq(
            Interest.OPTIMAL_UTILIZATION.wMulDown(Interest.SLOPE1),
            Interest.BASE_OPTIMAL_UTILIZATION,
            'Base rate match optimal utilization * SLOPE1'
        );
        assertEq(Interest.BASE_OPTIMAL_UTILIZATION, 0.08e18, 'Base rate should equal 8%');
    }

    function testDangerOptimalRateEqualsConstant() public pure {
        assertEq(
            Interest.BASE_OPTIMAL_UTILIZATION
                + (Interest.DANGER_UTILIZATION - Interest.OPTIMAL_UTILIZATION).wMulDown(Interest.SLOPE2),
            Interest.BASE_DANGER_UTILIZATION,
            'Base rate should equal optimal utilization + (danger utilization - optimal utilization) * SLOPE2'
        );
        assertEq(Interest.BASE_DANGER_UTILIZATION, 0.33e18, 'Base rate should equal 33%');
    }

    function testAccrueInterestNoDuration() public view {
        uint128[6] memory resultingAssets;
        uint128[6] memory expectedAssets;
        expectedAssets = [uint128(DEFAULT_RESERVE), 0, 0, 0, 0, 0];

        (resultingAssets,,,) = Interest.accrueInterestWithAssets(startingAssets, accrueInterestParams);
        verifyAssets(resultingAssets, expectedAssets);
    }

    function testInterestAfter1YearSecondsWhenSharesEqualAssets() public {
        uint112 borrowedInitialAssets = uint112(DEFAULT_RESERVE / 2);
        accrueInterestParams.duration = 365 days;
        uint128 depositedInitialAssets = DEFAULT_RESERVE;

        // calculate ALA_0 from DL_0 and BL_0
        accrueInterestParams.adjustedActiveLiquidity = depositedInitialAssets - borrowedInitialAssets;
        startingAssets[DEPOSIT_L] = depositedInitialAssets;
        startingAssets[BORROW_L] = borrowedInitialAssets;

        // Exercise function under test.
        uint256[3] memory protocolFeeAssets;
        (startingAssets,,, protocolFeeAssets) = Interest.accrueInterestWithAssets(startingAssets, accrueInterestParams);

        uint256 growthOfBorrowedLAsset = uint256(startingAssets[BORROW_L]) - borrowedInitialAssets;
        uint256 expectedGrowthOfBorrowedLAssets = LIQUIDITY_INTEREST_RATE_MAGNIFICATION
            * getExpectedBorrowAssetsGrowth(accrueInterestParams.duration, borrowedInitialAssets, depositedInitialAssets);
        uint256 growthOfDepositedLAssets = uint256(startingAssets[DEPOSIT_L]) - depositedInitialAssets;

        assertEq(growthOfBorrowedLAsset, expectedGrowthOfBorrowedLAssets, 'BorrowedL assets growth mismatch');
        assertEq(
            growthOfBorrowedLAsset - protocolFeeAssets[DEPOSIT_L],
            growthOfDepositedLAssets,
            'BorrowedL and DepositedL should match'
        );
    }

    function testInterestAfterOneYearWhenAssetsAreBiggerThanShares() public {
        // Increase liquidity scaler by 20%.
        uint128 initialLiquidityAssets = uint128(DEFAULT_RESERVE * 6 / 5);
        uint128 initialBorrowAssets = initialLiquidityAssets / 2;

        // calculate ALA_1 from DL_0 and BL_0
        accrueInterestParams.adjustedActiveLiquidity = initialLiquidityAssets - initialBorrowAssets;
        startingAssets[DEPOSIT_L] = initialLiquidityAssets;
        startingAssets[BORROW_L] = initialBorrowAssets;

        // assets are 25% larger than the shares
        accrueInterestParams.shares[BORROW_L] = uint112(Math.mulDiv(initialBorrowAssets, 4, 5, Math.Rounding.Ceil));

        accrueInterestParams.duration = 365 days;

        // Exercise function under test.
        uint256[3] memory protocolFeeAssets;
        (startingAssets,,, protocolFeeAssets) = Interest.accrueInterestWithAssets(startingAssets, accrueInterestParams);

        uint256 growthOfBorrowedLAsset = uint256(startingAssets[BORROW_L]) - initialBorrowAssets;
        uint256 expectedGrowthOfBorrowedLAssets = LIQUIDITY_INTEREST_RATE_MAGNIFICATION
            * getExpectedBorrowAssetsGrowth(accrueInterestParams.duration, initialBorrowAssets, initialLiquidityAssets);
        uint256 growthOfDepositedLAssets = uint256(startingAssets[DEPOSIT_L]) - initialLiquidityAssets;

        assertEq(growthOfBorrowedLAsset, expectedGrowthOfBorrowedLAssets, 'BorrowedL scaler mismatch');
        assertEq(
            growthOfBorrowedLAsset - protocolFeeAssets[DEPOSIT_L],
            growthOfDepositedLAssets,
            'BorrowedL and DepositedL should match'
        );
    }

    function verifyAssets(uint128[6] memory actualAssets, uint128[6] memory _expectedAssets) private pure {
        assertEq(actualAssets[DEPOSIT_L], _expectedAssets[DEPOSIT_L], 'Assets for DEPOSIT_L should match');
        assertEq(actualAssets[DEPOSIT_X], _expectedAssets[DEPOSIT_X], 'Assets for DEPOSIT_X should match');
        assertEq(actualAssets[DEPOSIT_Y], _expectedAssets[DEPOSIT_Y], 'Assets for DEPOSIT_Y should match');
        assertEq(actualAssets[BORROW_X], _expectedAssets[BORROW_X], 'Assets for BORROW_X should match');
        assertEq(actualAssets[BORROW_Y], _expectedAssets[BORROW_Y], 'Assets for BORROW_Y should match');
        assertEq(actualAssets[BORROW_L], _expectedAssets[BORROW_L], 'Assets for BORROW_L should match');
    }

    function testInterestSpecs_UTILIZATION_50_PERCENT() public pure {
        uint256 expectedAssetsD;
        uint256 expectedAssetsB;

        uint128 prevDepositAssets;
        uint128 prevBorrowAssets;
        uint256 actualInterest;

        uint256 depositedShares;
        uint256 borrowedShares;
        {
            uint256 duration = ONE_YEAR_SECONDS;
            uint256 totalDepositedAssets = 100e18;
            uint256 utilization = 50;
            uint256 totalBorrowedAssets = totalDepositedAssets * utilization / 100;

            depositedShares = totalDepositedAssets;
            borrowedShares = totalBorrowedAssets;

            prevDepositAssets = uint128(totalDepositedAssets);
            prevBorrowAssets = uint128(totalBorrowedAssets);

            actualInterest = Interest.computeInterestAssets(
                duration, utilization * WAD / 100, totalBorrowedAssets, totalDepositedAssets
            );

            uint256 expectedRate = Interest.getAnnualInterestRatePerSecondInWads(utilization * WAD / 100);

            uint256 expectedInterest = totalBorrowedAssets * MathLib.wTaylorCompounded(expectedRate, duration) / 1e18;

            expectedAssetsD = prevDepositAssets + expectedInterest;
            expectedAssetsB = prevBorrowAssets + expectedInterest;
        }

        uint256 actualAssetsD = Interest.addInterestToAssets(prevDepositAssets, actualInterest);
        uint256 actualAssetsB = Interest.addInterestToAssets(prevBorrowAssets, actualInterest);

        assertEq(actualAssetsD, expectedAssetsD, 'AssetsD mismatch');
        assertEq(actualAssetsB, expectedAssetsB, 'AssetsB mismatch');
    }

    function testInterestSpecs_UTILIZATION_90_PERCENT() public pure {
        uint256 expectedAssetsD;
        uint256 expectedAssetsB;

        uint128 prevDepositAssets;
        uint128 prevBorrowAssets;
        uint256 actualInterest;

        uint256 depositedShares;
        uint256 borrowedShares;
        {
            uint256 duration = ONE_YEAR_SECONDS;
            uint256 totalDepositedAssets = 300e18;
            uint256 utilization = 90;
            uint256 totalBorrowedAssets = totalDepositedAssets * utilization / 100;

            depositedShares = totalDepositedAssets;
            borrowedShares = totalBorrowedAssets;

            prevDepositAssets = uint128(totalDepositedAssets);
            prevBorrowAssets = uint128(totalBorrowedAssets);

            actualInterest = Interest.computeInterestAssets(
                duration, utilization * WAD / 100, totalBorrowedAssets, totalDepositedAssets
            );

            uint256 expectedRate = Interest.getAnnualInterestRatePerSecondInWads(utilization * WAD / 100);

            uint256 expectedInterest = totalBorrowedAssets * MathLib.wTaylorCompounded(expectedRate, duration) / 1e18;

            expectedAssetsD = prevDepositAssets + expectedInterest;
            expectedAssetsB = prevBorrowAssets + expectedInterest;
        }

        uint256 actualAssetsD = Interest.addInterestToAssets(prevDepositAssets, actualInterest);
        uint256 actualAssetsB = Interest.addInterestToAssets(prevBorrowAssets, actualInterest);

        assertEq(actualAssetsD, expectedAssetsD, 'AssetsD mismatch');
        assertEq(actualAssetsB, expectedAssetsB, 'AssetsB mismatch');
    }

    function testInterestSpecs_UTILIZATION_1_PERCENT() public pure {
        uint256 expectedAssetsD;
        uint256 expectedAssetsB;

        uint128 prevDepositAssets;
        uint128 prevBorrowAssets;
        uint256 actualInterest;

        uint256 depositedShares;
        uint256 borrowedShares;
        {
            uint256 duration = 1;
            uint256 totalDepositedAssets = 3e18;
            uint256 utilization = 1;
            uint256 totalBorrowedAssets = totalDepositedAssets * utilization / 100;

            depositedShares = totalDepositedAssets;
            borrowedShares = totalBorrowedAssets;

            prevDepositAssets = uint128(totalDepositedAssets);
            prevBorrowAssets = uint128(totalBorrowedAssets);

            actualInterest = Interest.computeInterestAssets(
                duration, utilization * WAD / 100, totalBorrowedAssets, totalDepositedAssets
            );

            uint256 expectedRate = Interest.getAnnualInterestRatePerSecondInWads(utilization * WAD / 100);

            uint256 expectedInterest = totalBorrowedAssets * MathLib.wTaylorCompounded(expectedRate, duration) / 1e18;

            expectedAssetsD = prevDepositAssets + expectedInterest;
            expectedAssetsB = prevBorrowAssets + expectedInterest;
        }

        uint256 actualAssetsD = Interest.addInterestToAssets(prevDepositAssets, actualInterest);
        uint256 actualAssetsB = Interest.addInterestToAssets(prevBorrowAssets, actualInterest);

        assertEq(actualAssetsD, expectedAssetsD, 'AssetsD mismatch');
        assertEq(actualAssetsB, expectedAssetsB, 'AssetsB mismatch');
    }

    function getExpectedBorrowAssetsGrowth(
        uint256 duration,
        uint256 borrowedAssets,
        uint256 depositedAssets
    ) private pure returns (uint256 expectedGrowth) {
        uint256 compoundRate = getCompoundRate(duration, borrowedAssets, depositedAssets);
        expectedGrowth = compoundRate * borrowedAssets / WAD;
    }

    function testInterestSpecs_Saturation_94_PERCENT() public {
        accrueInterestParams.satPercentageInWads = 941_795_538_580_338_563; // delta = 4

        uint256 expectedAssetsD;
        uint256 expectedAssetsB;

        uint128 prevDepositAssets;
        uint128 prevBorrowAssets;
        uint256 actualInterest;

        uint256 depositedShares;
        uint256 borrowedShares;
        {
            uint256 duration = ONE_YEAR_SECONDS;
            uint256 totalDepositedAssets = 300e18;
            uint256 utilizationWads = accrueInterestParams.satPercentageInWads;
            uint256 totalBorrowedAssets = totalDepositedAssets * utilizationWads / WAD;

            depositedShares = totalDepositedAssets;
            borrowedShares = totalBorrowedAssets;

            prevDepositAssets = uint128(totalDepositedAssets);
            prevBorrowAssets = uint128(totalBorrowedAssets);

            actualInterest =
                Interest.computeInterestAssets(duration, utilizationWads, totalBorrowedAssets, totalDepositedAssets);

            uint256 expectedRate = Interest.getAnnualInterestRatePerSecondInWads(utilizationWads);

            uint256 expectedInterest = totalBorrowedAssets * MathLib.wTaylorCompounded(expectedRate, duration) / 1e18;

            expectedAssetsD = prevDepositAssets + expectedInterest;
            expectedAssetsB = prevBorrowAssets + expectedInterest;
        }

        uint256 actualAssetsD = Interest.addInterestToAssets(prevDepositAssets, actualInterest);
        uint256 actualAssetsB = Interest.addInterestToAssets(prevBorrowAssets, actualInterest);

        assertEq(actualAssetsD, expectedAssetsD, 'AssetsD mismatch');
        assertEq(actualAssetsB, expectedAssetsB, 'AssetsB mismatch');
    }
}
