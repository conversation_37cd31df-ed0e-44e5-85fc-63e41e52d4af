// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {Validation} from 'contracts/libraries/Validation.sol';
import {Convert} from 'contracts/libraries/Convert.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';

contract ConvertSharesAndAssetsUnitTests is Test {
    function testToAssets_Fuzz(uint112 shares, uint128 totalAssets, uint112 totalShares) public pure {
        vm.assume(totalShares > 0);
        vm.assume(shares <= totalShares);

        // Rounding Down Test
        bool roundingUp = false;
        uint256 expectedAssetsDown = uint256(shares) * totalAssets / totalShares;
        uint256 calculatedAssetsDown = Convert.toAssets(shares, totalAssets, totalShares, roundingUp);
        assertEq(calculatedAssetsDown, expectedAssetsDown, 'Assets calculation with rounding down should match');

        // Rounding Up Test
        roundingUp = true;
        uint256 expectedAssetsUp = (uint256(shares) * totalAssets + totalShares - 1) / totalShares; // Rounding up formula
        uint256 calculatedAssetsUp = Convert.toAssets(shares, totalAssets, totalShares, roundingUp);
        assertEq(calculatedAssetsUp, expectedAssetsUp, 'Assets calculation with rounding up should match');
    }

    function testToShares_Fuzz(uint112 assets, uint128 totalAssets, uint112 totalShares) public pure {
        vm.assume(totalAssets > 0);
        vm.assume(assets <= totalAssets);

        // Rounding Down Test
        bool roundingUp = false;
        uint256 expectedSharesDown = uint256(assets) * totalShares / totalAssets;
        uint256 calculatedSharesDown = Convert.toShares(assets, totalAssets, totalShares, roundingUp);
        assertEq(calculatedSharesDown, expectedSharesDown, 'Shares calculation with rounding down should match');

        // Rounding Up Test
        roundingUp = true;
        uint256 expectedSharesUp = (uint256(assets) * totalShares + totalAssets - 1) / totalAssets; // Rounding up formula
        uint256 calculatedSharesUp = Convert.toShares(assets, totalAssets, totalShares, roundingUp);
        assertEq(calculatedSharesUp, expectedSharesUp, 'Shares calculation with rounding up should match');
    }

    function testToAssets_ZeroTotalSharesAndAssets() public pure {
        uint256 shares = 100;
        uint256 totalAssets = 0;
        uint256 totalShares = 0;
        bool roundingUp = false;

        uint256 calculatedAssets = Convert.toAssets(shares, totalAssets, totalShares, roundingUp);

        assertEq(calculatedAssets, shares, 'Assets calculation with zero total shares should match');
    }

    function testToShares_ZeroTotalAssetsAndShares() public pure {
        uint256 assets = 100;
        uint256 totalAssets = 0;
        uint256 totalShares = 0;
        bool roundingUp = false;

        uint256 calculatedShares = Convert.toShares(assets, totalAssets, totalShares, roundingUp);

        assertEq(calculatedShares, assets, 'Shares calculation with zero total assets should match');
    }
}
