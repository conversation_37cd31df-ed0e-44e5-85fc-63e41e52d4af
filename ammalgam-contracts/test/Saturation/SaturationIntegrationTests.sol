// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {SafeCast} from '@openzeppelin/contracts/utils/math/SafeCast.sol';

import {Validation} from 'contracts/libraries/Validation.sol';
import {Uint16Set} from 'contracts/libraries/Uint16Set.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Saturation} from 'contracts/libraries/Saturation.sol';
import {
    MAG2, Q72, LTVMAX_IN_MAG2, TRANCHE_B_IN_Q72, SAT_PERCENTAGE_DELTA_8_WAD
} from 'contracts/libraries/constants.sol';
import {FactoryPairTestFixture, MAX_TOKEN, PairHarness, IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';
import {
    LXY,
    createLXYAssetsYDLB,
    createLXYAssetsXDLB,
    createLXYAssetsYDXB,
    sumOfSeriesOfBorrowX,
    sumOfSeriesOfBorrowY,
    sumOfSeriesOfCollateralY,
    sumOfSeriesOfCollateralX,
    getSatPerTrancheOrZero
} from 'test/Saturation/SaturationTestUtils.sol';
import {BORROW_L, BORROW_X, BORROW_Y, DEPOSIT_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {ISaturationAndGeometricTWAPState} from 'contracts/interfaces/ISaturationAndGeometricTWAPState.sol';

using SafeCast for uint256;
using SafeCast for int256;

contract SaturationIntegrationTests is Test {
    FactoryPairTestFixture private fixture;
    IPairHarness private pair;
    uint256 private constant N_TESTERS = 30;
    address[N_TESTERS] private testers;
    uint256 private constant testerBalance = 100e40;
    uint256 private constant initialLDX = 8e18;
    uint256 private constant initialLDY = 2e18;
    uint256 private initialSqrtPriceXInQ72 = Math.sqrt(Math.mulDiv(initialLDX, 1 << 72, initialLDY)) << 64;
    uint256 private activeActiveLiquidity = uint256(Math.sqrt(initialLDX * initialLDY));

    uint256 internal constant SatBufferMag2 = Saturation.SATURATION_TIME_BUFFER_IN_MAG2;

    // Add satStruct as a state variable
    Saturation.SaturationStruct satStruct;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();

        for (uint256 i = 0; i < N_TESTERS; i++) {
            testers[i] = vm.addr(1000 + i);
            fixture.transferTokensTo(testers[i], testerBalance, testerBalance);
        }

        fixture.mintForAndInitializeBlocks(testers[0], initialLDX, initialLDY);
    }

    function testTrancheLocationAcrossOneTranchesXAgainstYAtPenaltyThreshold() public {
        uint256 trancheStartSqrtPriceQ72 = Q72 - 1;
        uint256 tranches = 1;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 expectedSaturation = activeActiveLiquidity * desiredSaturationThresholdMag2 / MAG2;

        uint256 borrowX = sumOfSeriesOfBorrowX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[1]);

        assertEq(account.exists, true, 'account should exist');
        assertEq(account.lastTranche, -1);

        uint256 leaf = Saturation.satToLeaf(expectedSaturation);
        (Saturation.SaturationPair memory actualTreeSaturation,,) =
            fixture.saturationAndGeometricTWAPState().getLeafDetails(fixture.pairAddress(), true, leaf);
        assertEq(actualTreeSaturation.satRelativeToL, expectedSaturation, 'tree should have the expected saturation.');
        assertEq(
            account.satPairPerTranche[0].satRelativeToL,
            actualTreeSaturation.satRelativeToL,
            'user account should have the expected saturation in the first tranche.'
        );
        assertApproxEqAbs(account.satPairPerTranche[1].satRelativeToL, 0, 5, 'The second tranche should be empty.');
    }

    function testTrancheLocationAcrossOneTrancheYAgainstXAtPenaltyThreshold() public {
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000; // B^4 = 2.1859
        uint256 tranches = 1;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 expectedSaturation = activeActiveLiquidity * desiredSaturationThresholdMag2 / MAG2;

        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(account.exists, true, 'account should exist');
        assertEq(account.lastTranche, 5, 'account should be at the 5 tranche');
        assertApproxEqRel(
            account.satPairPerTranche[0].satRelativeToL,
            expectedSaturation,
            0.0014e16, // 0.0014%
            'we should have taken the liquidity in the first tranche'
        );
        assertEq(getSatPerTrancheOrZero(account.satPairPerTranche, 1).satRelativeToL, 0);
    }

    function testTrancheLocationAcrossTwoTranchesXAgainstYAtPenaltyThreshold() public {
        uint256 trancheStartSqrtPriceQ72 = 121_593 * Q72 / 100_000; // B^1 = 1.21593
        uint256 tranches = 2;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 expectedSaturation = activeActiveLiquidity * desiredSaturationThresholdMag2 / MAG2;

        uint256 borrowX = sumOfSeriesOfBorrowX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[1]);

        assertEq(account.exists, true, 'account should exist');
        assertEq(account.lastTranche, -1);
        assertEq(
            account.satPairPerTranche[0].satRelativeToL,
            expectedSaturation,
            'first tranche should have expected saturation'
        );
        assertEq(
            account.satPairPerTranche[1].satRelativeToL,
            expectedSaturation,
            'second tranche should have expected saturation'
        );

        assertLt(
            account.satPairPerTranche[2].satRelativeToL,
            expectedSaturation / 100_000,
            'third tranche should be empty, as we are at the penalty threshold'
        );
    }

    function testTrancheLocationAcrossThreeTranchesYAgainstXAtPenaltyThreshold() public {
        uint256 trancheStartSqrtPriceQ72 = 265_789 * Q72 / 100_000; // B^5 = 2.65789
        uint256 tranches = 3;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 expectedSaturation = activeActiveLiquidity * desiredSaturationThresholdMag2 / MAG2;

        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(account.exists, true, 'account should exist');
        assertEq(account.lastTranche, 8);
        assertEq(
            account.satPairPerTranche[0].satRelativeToL,
            expectedSaturation,
            'first tranche should have expected saturation'
        );
        assertEq(
            account.satPairPerTranche[1].satRelativeToL,
            expectedSaturation,
            'second tranche should have expected saturation'
        );
        assertApproxEqRel(
            account.satPairPerTranche[2].satRelativeToL, expectedSaturation, 0.0038e16, 'third tranche should be empty'
        );
        assertEq(
            getSatPerTrancheOrZero(account.satPairPerTranche, 4).satRelativeToL, 0, 'fourth tranche should be empty'
        );
    }

    function testTrancheLocationPartialTrancheUseXAgainstY() public {
        uint256 trancheStartSqrtPriceQ72 = 1158 * Q72 / 1000; // B^(3/4)
        uint256 tranches = 1;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 quarterTrancheSaturation = activeActiveLiquidity * desiredSaturationThresholdMag2 / MAG2 / 4; // 1/4 of the saturation
        uint256 threeQuarterTrancheSaturation =
            activeActiveLiquidity * Q72 / TRANCHE_B_IN_Q72 * desiredSaturationThresholdMag2 / MAG2 * 3 / 4; // 3/4 of the saturation

        uint256 borrowX = sumOfSeriesOfBorrowX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[1]);

        assertEq(account.exists, true, 'account should exist');
        assertEq(
            account.satPairPerTranche[0].satRelativeToL,
            quarterTrancheSaturation,
            'first tranche should have expected saturation'
        );
        assertApproxEqRel(
            account.satPairPerTranche[1].satRelativeToL,
            threeQuarterTrancheSaturation,
            0.009e16, // 0.009%
            'second tranche should have expected saturation'
        );
        assertEq(
            getSatPerTrancheOrZero(account.satPairPerTranche, 3).satRelativeToL, 0, 'The third tranche should be empty'
        );
    }

    // interest on penalties

    function testPenaltyGivesBorrowLShares() public {
        fixture.mintFor(testers[2], initialLDX, initialLDY);

        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        uint256 borrowX =
            sumOfSeriesOfBorrowX(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);

        // Set saturation ratio for the borrower
        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(
            fixture.pairAddress(), desiredSaturationRatioInMAG2
        );
        vm.stopPrank();

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        fixture.burnFor(testers[2], Math.sqrt(initialLDX * initialLDY) * 10 / 200);

        // testers[1] has no BORROW_L shares
        assertEq(pair.tokens(BORROW_L).balanceOf(testers[1]), 0, 'testers[1] has no BORROW_L tokens');

        // time passes
        uint256 duration = 10 days;
        vm.warp(block.timestamp + duration);
        vm.roll(block.number + Math.ceilDiv(duration, 12));
        fixture.pair().skim(testers[1]);

        // testers[1] has BORROW_L shares from penalties
        assertGt(pair.tokens(BORROW_L).balanceOf(testers[1]), 0, 'testers[1] has some BORROW_L tokens');
    }

    function testPenaltyIncreasesInterest() public {
        fixture.mintFor(testers[2], initialLDX, initialLDY);

        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        uint256 borrowX =
            sumOfSeriesOfBorrowX(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);

        // Set saturation ratio for the borrower
        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(
            fixture.pairAddress(), desiredSaturationRatioInMAG2
        );
        vm.stopPrank();

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        fixture.burnFor(testers[2], Math.sqrt(initialLDX * initialLDY) * 10 / 200);

        uint128[6] memory assetsBefore = pair.totalAssets();
        assertEq(assetsBefore[BORROW_L], 0, 'no BORROW_L tokens in the beginning');

        // Verify we're in penalty
        ISaturationAndGeometricTWAPState satState = fixture.pair().exposed_saturationAndGeometricTWAPState();
        (uint16 netXHighestLeaf,) = satState.getTreeDetails(address(fixture.pair()), true);
        uint256 activeLiquidity = assetsBefore[DEPOSIT_L] - assetsBefore[BORROW_L];
        uint256 thresholdLeaf =
            Saturation.satToLeaf(activeLiquidity * Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 / MAG2);
        require(netXHighestLeaf >= thresholdLeaf, 'Position must be in penalty for test to be valid');

        // time passes
        uint256 duration = 10 days;
        vm.warp(block.timestamp + duration);
        vm.roll(block.number + Math.ceilDiv(duration, 12));
        fixture.pair().sync();

        // Check that penalties were applied - both LP compensation and borrower debt
        uint128[6] memory assetsAfter = pair.totalAssets();
        assertGt(
            assetsAfter[DEPOSIT_L], assetsBefore[DEPOSIT_L], 'LPs should receive penalty compensation via DEPOSIT_L'
        );
        assertGt(assetsAfter[BORROW_L], assetsBefore[BORROW_L], 'BORROW_L should increase from penalty debt');

        // The penalty should be meaningful
        uint256 penaltyAccrued = assetsAfter[DEPOSIT_L] - assetsBefore[DEPOSIT_L];
        uint256 minMeaningfulPenalty = assetsBefore[DEPOSIT_L] / 1000; // 0.1% over 10 days
        assertGt(penaltyAccrued, minMeaningfulPenalty, 'Penalty should be economically meaningful');
    }

    // state getters

    /**
     * @notice different tranches, same leaf
     */
    function testGetLeafTranchesKeyPointers() public {
        uint256 targetSaturationInLAssets = 0.0001e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);
        targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72
        accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[2], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[2], uint256(accountLXYShares.XB), 0);
        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        (uint16 highestSetLeaf,) = saturationAndGeometricTWAPState.getTreeDetails(address(pair), true);
        (,, uint16[] memory keyList) =
            saturationAndGeometricTWAPState.getLeafDetails(address(pair), true, highestSetLeaf);
        assertEq(keyList.length, 2);
    }

    function testGetLeafTranchesKeyList() public {
        uint256 targetSaturationInLAssets = 0.0001e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);
        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        (uint16 highestSetLeaf,) = saturationAndGeometricTWAPState.getTreeDetails(address(pair), true);
        (,, uint16[] memory keyList) =
            saturationAndGeometricTWAPState.getLeafDetails(address(pair), true, highestSetLeaf);

        assertGt(keyList[0], 0);
        assertEq(keyList.length, 1);
    }

    function testSetNewPositionSaturationTooHigh() public {
        uint256 tooHighSaturationMag2 = Saturation.MAX_INITIAL_SATURATION_MAG2 + 1;
        ISaturationAndGeometricTWAPState saturationState = fixture.saturationAndGeometricTWAPState();

        vm.expectRevert(ISaturationAndGeometricTWAPState.InvalidUserConfiguration.selector);
        saturationState.setNewPositionSaturation(address(pair), tooHighSaturationMag2);

        vm.expectRevert(ISaturationAndGeometricTWAPState.InvalidUserConfiguration.selector);
        saturationState.setNewPositionSaturation(address(pair), 0);
    }

    function testGetLeafPenaltyInLAssetsPerSatInQ72() public {
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;

        uint256 targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72

        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(address(pair), desiredSaturationRatioInMAG2);

        // use three tranches to make sure one is full.
        uint256 borrowX =
            sumOfSeriesOfBorrowX(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);

        // Set user-specific saturation ratio for testers[1]
        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(address(pair), desiredSaturationRatioInMAG2);
        vm.stopPrank();

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        uint256 activeLiquidityInLAssets = Math.mulDiv(
            activeActiveLiquidity, Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2, MAG2, Math.Rounding.Floor
        );

        uint256 thresholdLeaf = Saturation.satToLeaf(activeLiquidityInLAssets);

        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        (, uint256 leafPenaltyInLAssetsPerSatInQ72,) =
            saturationAndGeometricTWAPState.getLeafDetails(address(pair), true, thresholdLeaf);

        assertEq(leafPenaltyInLAssetsPerSatInQ72, 0, 'leaf penalty should be 0 at threshold leaf');

        vm.warp(block.timestamp + 1 days);
        vm.roll(block.number + Math.ceilDiv(1 days, 12));
        fixture.pair().sync();

        (, leafPenaltyInLAssetsPerSatInQ72,) =
            saturationAndGeometricTWAPState.getLeafDetails(address(pair), true, thresholdLeaf);

        assertGt(leafPenaltyInLAssetsPerSatInQ72, 0, 'leaf penalty should be > 0 after time passes');
    }

    function testGetTreeDetails() public {
        uint256 targetSaturationInLAssets = 0.0001e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);
        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        (uint16 highestSetLeaf,) = saturationAndGeometricTWAPState.getTreeDetails(address(pair), true);
        assertGt(highestSetLeaf, 0);
    }

    function testGetLeafSatInLAssets() public {
        uint256 targetSaturationInLAssets = 0.45e18;
        uint256 targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72
        LXY memory accountLXYAssets = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, accountLXYAssets.YD);
        fixture.borrowFor(testers[1], accountLXYAssets.XB, 0);

        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        (uint16 highestSetLeaf,) = saturationAndGeometricTWAPState.getTreeDetails(address(pair), true);
        (Saturation.SaturationPair memory leafSaturation,,) =
            saturationAndGeometricTWAPState.getLeafDetails(address(pair), true, highestSetLeaf);
        assertGt(leafSaturation.satInLAssets, 0);
    }

    function testGetTrancheToLeaf() public {
        uint256 targetSaturationInLAssets = 0.1e18;
        uint256 targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72
        LXY memory accountLXYAssets = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, accountLXYAssets.YD);
        fixture.borrowFor(testers[1], accountLXYAssets.XB, 0);

        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        Saturation.Account memory account = saturationAndGeometricTWAPState.getAccount(address(pair), true, testers[1]);
        (uint16 leaf,) = saturationAndGeometricTWAPState.getTrancheDetails(address(pair), true, account.lastTranche);
        (uint16 highestSetLeaf,) = saturationAndGeometricTWAPState.getTreeDetails(address(pair), true);
        assertEq(leaf, highestSetLeaf);
    }

    function testGetTrancheToSaturationPair() public {
        uint256 targetSaturationInLAssets = 0.1e18;
        uint256 targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72
        LXY memory accountLXYAssets = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, accountLXYAssets.YD);
        fixture.borrowFor(testers[1], accountLXYAssets.XB, 0);

        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        Saturation.Account memory account = saturationAndGeometricTWAPState.getAccount(address(pair), true, testers[1]);
        uint256 satInLAssets;
        (, Saturation.SaturationPair memory saturationPair) =
            saturationAndGeometricTWAPState.getTrancheDetails(address(pair), true, account.lastTranche);
        (, saturationPair) = saturationAndGeometricTWAPState.getTrancheDetails(address(pair), true, account.lastTranche);
        satInLAssets = saturationPair.satInLAssets;
        assertGt(satInLAssets, 0);
        (, saturationPair) =
            saturationAndGeometricTWAPState.getTrancheDetails(address(pair), true, account.lastTranche - 1);
        satInLAssets = saturationPair.satInLAssets;

        assertEq(satInLAssets, 0);
        (, saturationPair) =
            saturationAndGeometricTWAPState.getTrancheDetails(address(pair), true, account.lastTranche + 1);
        satInLAssets = saturationPair.satInLAssets;

        assertEq(satInLAssets, 0);
    }

    function testGetNode() public {
        uint256 targetSaturationInLAssets = 0.1e18;
        uint256 targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72
        LXY memory accountLXYAssets = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, accountLXYAssets.YD);
        fixture.borrowFor(testers[1], accountLXYAssets.XB, 0);

        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        Saturation.Account memory account = saturationAndGeometricTWAPState.getAccount(address(pair), true, testers[1]);
        (uint256 satFromNodeInLAssets,) = pair.exposed_totalSat();
        (, Saturation.SaturationPair memory saturationPair) =
            saturationAndGeometricTWAPState.getTrancheDetails(address(pair), true, account.lastTranche);
        assertEq(satFromNodeInLAssets, saturationPair.satInLAssets);
    }

    function testGetAccount() public {
        uint256 targetSaturationInLAssets = 0.1e18;
        uint256 targetLiqSqrtPriceXInQ72 = 15 * Q72 / 10; // 1.5<<72
        LXY memory accountLXYAssets = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, accountLXYAssets.YD);
        fixture.borrowFor(testers[1], accountLXYAssets.XB, 0);

        ISaturationAndGeometricTWAPState saturationAndGeometricTWAPState =
            pair.exposed_saturationAndGeometricTWAPState();
        Saturation.Account memory account = saturationAndGeometricTWAPState.getAccount(address(pair), true, testers[1]);
        assert(account.exists);
        account = saturationAndGeometricTWAPState.getAccount(address(pair), true, testers[2]);
        assert(!account.exists);
    }

    // sat updates

    function testSetMaxHealthySat() public {
        uint256 targetSaturationInLAssets = 1e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));

        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(fixture.pairAddress(), 1);
        vm.stopPrank();

        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(fixture.pairAddress(), 10);
        vm.stopPrank();
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);
    }

    function testSatUpdateOnMint() public {
        uint256 targetSaturationInLAssets = 0.6e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);

        (uint256 netXTotalSaturation,) = pair.exposed_totalSat();

        fixture.mintFor(testers[1], 0.0001e18, 0.0001e18);
        (uint256 netXTotalSaturationAfterMint,) = pair.exposed_totalSat();

        // check that mint reduces sat
        assertLt(netXTotalSaturationAfterMint, netXTotalSaturation);
    }

    function testSatUpdateOnDeposit() public {
        uint256 targetSaturationInLAssets = 0.0001e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);

        (uint256 netXTotalSaturation,) = pair.exposed_totalSat();

        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        (uint256 netXTotalSaturationAfterDeposit,) = pair.exposed_totalSat();

        // check that deposit changes sat (might be larger because tranche changes to further away)
        assertTrue(netXTotalSaturationAfterDeposit != netXTotalSaturation);
    }

    function testSatUpdateOnRepay() public {
        uint256 targetSaturationInLAssets = 0.0001e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);

        (uint256 netXTotalSaturation,) = pair.exposed_totalSat();

        fixture.repayFor(testers[1], uint256(accountLXYShares.XB) / 2, 0);
        (uint256 netXTotalSaturationAfterRepay,) = pair.exposed_totalSat();

        // check that repay reduces sat
        assertLt(netXTotalSaturationAfterRepay, netXTotalSaturation);
    }

    function testSatUpdateOnRepayLiquidity() public {
        uint256 targetSaturationInLAssets = 0.0006e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDLB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowLiquidityFor(testers[1], uint256(accountLXYShares.LB));

        (uint256 netXTotalSaturation,) = pair.exposed_totalSat();

        fixture.repayLiquidityForNoEvent(testers[1], 0.0001e18, 0.0001e18);
        (uint256 netXTotalSaturationAfterRepay,) = pair.exposed_totalSat();

        // check that repay reduces sat
        assertLt(netXTotalSaturationAfterRepay, netXTotalSaturation);
    }

    function testSatUpdateOnRepayLiquidityXD() public {
        uint256 targetSaturationInLAssets = 0.0006e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsXDLB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], uint256(accountLXYShares.XD), 0);
        fixture.borrowLiquidityFor(testers[1], uint256(accountLXYShares.LB));

        (, uint256 netYTotalSaturation) = pair.exposed_totalSat();

        fixture.repayLiquidityForNoEvent(testers[1], 0.0001e18, 0.0001e18);
        (, uint256 netYTotalSaturationAfterRepay) = pair.exposed_totalSat();

        // check that repay reduces sat
        assertLt(netYTotalSaturationAfterRepay, netYTotalSaturation);
    }

    // basic tests

    function testBorrowMeansSaturationIncrease() public {
        uint256 targetSaturationInLAssets = 0.0001e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, uint256(accountLXYShares.YD));
        fixture.borrowFor(testers[1], uint256(accountLXYShares.XB), 0);
        (uint256 netXTotalSaturation, uint256 netYTotalSaturation) = pair.exposed_totalSat();
        assertGt(netXTotalSaturation, 0);
        assertEq(netYTotalSaturation, 0);
    }

    function testAccrueSaturationPenalties() public {
        fixture.mintFor(testers[2], initialLDX, initialLDY);

        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(
            fixture.pairAddress(), desiredSaturationRatioInMAG2
        );
        vm.stopPrank();

        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72

        // use three tranches to make sure one is full.
        uint256 borrowX =
            sumOfSeriesOfBorrowX(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);
        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        // burn liquidity to move the position into penalty.
        fixture.burnFor(testers[2], Math.sqrt(initialLDX * initialLDY) * 10 / 200);

        uint256 assetsBefore = uint256(fixture.pair().totalAssets()[DEPOSIT_L]);

        // time passes
        vm.warp(block.timestamp + 1 days);
        vm.roll(block.number + Math.ceilDiv(1 days, 12));
        fixture.pair().sync();

        // LP should have more assets due to penalty
        uint256 assetsAfter = uint256(fixture.pair().totalAssets()[DEPOSIT_L]);

        assertGt(assetsAfter, assetsBefore);
    }

    function testBorrowerShouldOweSaturationPenaltiesYDXB() public {
        // borrow into penalty
        uint256 targetSaturationInLAssets = 0.07e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        LXY memory accountLXYAssets = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);
        fixture.depositFor(testers[1], 0, accountLXYAssets.YD);
        fixture.borrowFor(testers[1], accountLXYAssets.XB, 0);

        // borrow without penalty
        uint256 targetSaturationInLAssets2 = 0.0012e18;
        uint256 targetLiqSqrtPriceX2InQ72 = 135 * Q72 / 100; // 1.35<<72
        LXY memory accountLXYAssets2 = createLXYAssetsYDXB(targetSaturationInLAssets2, targetLiqSqrtPriceX2InQ72);
        fixture.depositFor(testers[2], 0, accountLXYAssets2.YD);
        fixture.borrowFor(testers[2], accountLXYAssets2.XB, 0);

        // time passes
        uint256 duration = 1 days;
        vm.warp(block.timestamp + duration);
        vm.roll(block.number + Math.ceilDiv(duration, 12));
        fixture.pair().sync();

        // repay
        fixture.repayForNoEvent(testers[1], accountLXYAssets.XB, 0);
        fixture.repayForNoEvent(testers[2], accountLXYAssets2.XB, 0);

        // how much does each still owe
        uint256 borrowXBalance1 = pair.tokens(BORROW_X).balanceOf(testers[1]);
        uint256 borrowXBalance2 = pair.tokens(BORROW_X).balanceOf(testers[2]);

        // both have at least interest un-repayed
        assertGt(borrowXBalance1, 0);
        assertGt(borrowXBalance2, 0);

        // both testers pays for interest, testers[1] also for sat penalty
        // if both xor neither had penalty, their left over balances ratio after repay without interest, would be similar to the ratio of the borrowed assets
        // else the ratio will be larger
        assertGt(borrowXBalance1, borrowXBalance2 * accountLXYAssets.XB / accountLXYAssets2.XB);
    }

    function testSaturationLeafDelta() public {
        Saturation.initializeSaturationStruct(satStruct);
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95<<72
        uint256 borrowX = sumOfSeriesOfBorrowX(
            targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2
        );

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        fixture.pair().sync();

        // Get saturation state
        ISaturationAndGeometricTWAPState satState = fixture.pair().exposed_saturationAndGeometricTWAPState();
        (uint16 netXHighestLeaf,) = satState.getTreeDetails(address(fixture.pair()), true);
        (uint16 netYHighestLeaf,) = satState.getTreeDetails(address(fixture.pair()), false);
        uint16 maxLeaf = uint16(Saturation.satToLeaf(activeActiveLiquidity));
        uint16 highestLeaf = uint16(Math.max(netXHighestLeaf, netYHighestLeaf));
        assertEq(maxLeaf - highestLeaf, 8, 'delta should be 8');

        satStruct.netXTree.highestSetLeaf = netXHighestLeaf;
        satStruct.netYTree.highestSetLeaf = netYHighestLeaf;
        satStruct.maxLeaf = maxLeaf;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_8_WAD,
            'should return ~86.94% when delta is 8'
        );
    }

    function testDynamicSaturationArray_RemovalClearsArray() public {
        uint256 tranches = 10;
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;
        uint256 desiredSaturationThresholdMag2 = 32;

        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        assertEq(
            pair.tokens(BORROW_Y).balanceOf(testers[1]),
            borrowY,
            "Borrower's BORROW_Y balance should match borrow amount"
        );

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertGt(account.satPairPerTranche.length, 0, 'Initial tranche count should be 10');

        fixture.repayFor(testers[1], 0, fixture.pair().tokens(BORROW_Y).balanceOf(testers[1])); // should remove sat

        account = fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(
            pair.tokens(BORROW_Y).balanceOf(testers[1]),
            0,
            "Borrower's BORROW_Y balance should be zero after full repayment"
        );

        for (uint256 i = 0; i < account.satPairPerTranche.length; i++) {
            assertEq(
                account.satPairPerTranche[i].satRelativeToL, 0, 'All tranches should be cleared after full removal'
            );
        }

        assertFalse(account.exists, 'Account should not exist after full removal');
    }

    function testDynamicSaturationArray_LengthMatchesAddedTranches() public {
        uint256 trancheCount = 30;
        uint256 trancheSpanInTicks = trancheCount * 100; // 30 tranches of 100 ticks each
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(account.satPairPerTranche.length, trancheCount, 'Incorrect tranche count in dynamic array');
    }

    function testDynamicSaturationArray_AccessingBeyondLengthShouldFail() public {
        uint256 trancheCount = 5;
        uint256 trancheSpanInTicks = trancheCount * 100; // 5 tranches of 100 ticks each
        uint256 desiredSaturationThresholdMag2 = 85;
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(account.satPairPerTranche.length, trancheCount, 'Incorrect tranche count in dynamic array');

        vm.expectRevert('panic: array out-of-bounds access (0x32)');
        account.satPairPerTranche[trancheCount];
    }

    function testDynamicSaturationArray_ZeroNotAutoFilled() public {
        uint256 trancheCount = 3;
        uint256 trancheSpanInTicks = trancheCount * 100; // 3 tranches of 100 ticks each
        uint256 desiredSaturationThresholdMag2 = 85;
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        for (uint256 i = 0; i < account.satPairPerTranche.length; i++) {
            assertGt(account.satPairPerTranche[i].satRelativeToL, 0, 'Expected non-zero saturation');
        }
    }

    function testSparseArrayProblem_ArrayGrowsButNeverShrinksWithFullRepay() public {
        uint256 trancheCount = 5;
        uint256 trancheSpanInTicks = trancheCount * 100;
        uint256 desiredSaturationThresholdMag2 = 85;
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;

        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory accountBeforeRepay =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(accountBeforeRepay.satPairPerTranche.length, trancheCount);

        // Repay 100%
        fixture.repayFor(testers[1], 0, borrowY);

        Saturation.Account memory accountAfterRepay =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        // Assert all values are 0
        for (uint256 i = 0; i < accountAfterRepay.satPairPerTranche.length; ++i) {
            assertEq(accountAfterRepay.satPairPerTranche[i].satRelativeToL, 0, 'All values should be zeroed');
        }

        assertEq(
            accountAfterRepay.satPairPerTranche.length,
            0,
            'Array length should be 0 after full repay if account was deleted'
        );

        // borrow same amount again
        fixture.borrowFor(testers[1], 0, borrowY);
        Saturation.Account memory accountAfterReAdd =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(
            accountAfterReAdd.satPairPerTranche.length,
            trancheCount,
            'Array should remain at initial length after borrowing again'
        );
    }

    function testBorrowYTreeRepayShrinksAndReBorrowCleanly() public {
        uint256 trancheCount = 5;
        uint256 trancheSpanInTicks = trancheCount * 100;
        uint256 desiredSaturationThresholdMag2 = 85;
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;

        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);

        Saturation.Account memory accountAfterFirstBorrow =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(
            accountAfterFirstBorrow.satPairPerTranche.length,
            trancheCount,
            'Initial tranche count should match the number of tranches'
        );

        uint256 repayAmount = borrowY * 60 / 100; // Repay 60% of debt
        fixture.repayFor(testers[1], 0, repayAmount);

        Saturation.Account memory accountAfterRepay =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertLt(
            accountAfterRepay.satPairPerTranche.length, trancheCount, 'Array should shrink after partial repayment'
        );

        uint256 newBorrowY = borrowY * 60 / 100; // Borrow back 60%
        fixture.borrowFor(testers[1], 0, newBorrowY);

        Saturation.Account memory accountAfterSecondBorrow =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(
            accountAfterSecondBorrow.satPairPerTranche.length,
            trancheCount,
            'Array should remain at initial length after borrowing again'
        );

        uint256 finalZeros = _countZeros(accountAfterSecondBorrow.satPairPerTranche);
        assertEq(finalZeros, 0, 'Array should not accumulate more zeros after borrowing again');
    }

    function testBorrowXTreeRepayShrinksAndReBorrowCleanly() public {
        uint256 trancheCount = 6;
        uint256 trancheSpanInTicks = trancheCount * 100;
        uint256 desiredSaturationThresholdMag2 = 85;
        uint256 trancheStartSqrtPriceQ72 = Q72;

        uint256 depositY = sumOfSeriesOfCollateralY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 borrowX = sumOfSeriesOfBorrowX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        Saturation.Account memory accountAfterFirstBorrow =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[1]);

        assertEq(
            accountAfterFirstBorrow.satPairPerTranche.length,
            trancheCount,
            'Initial tranche count should match the number of tranches'
        );

        uint256 repayAmount = borrowX * 60 / 100; // Repay 60% of debt
        fixture.repayFor(testers[1], repayAmount, 0);

        Saturation.Account memory accountAfterRepay =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[1]);

        assertLt(
            accountAfterRepay.satPairPerTranche.length, trancheCount, 'Array should shrink after partial repayment'
        );

        uint256 newBorrowX = borrowX * 60 / 100; // Borrow back 60%
        fixture.borrowFor(testers[1], newBorrowX, 0);

        Saturation.Account memory accountAfterSecondBorrow =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[1]);

        assertEq(
            accountAfterSecondBorrow.satPairPerTranche.length,
            trancheCount,
            'Array should remain at initial length after borrowing again'
        );

        uint256 finalZeros = _countZeros(accountAfterSecondBorrow.satPairPerTranche);
        assertEq(finalZeros, 0, 'Array should not accumulate more zeros after borrowing again');
    }

    function testSatToBeRecalculatedByAddingMoreDeposit() public {
        uint256 trancheCount = 5;
        uint256 trancheSpanInTicks = trancheCount * 100;
        uint256 desiredSaturationThresholdMag2 = 85;
        uint256 trancheStartSqrtPriceQ72 = 21_859 * Q72 / 10_000;

        uint256 depositX = sumOfSeriesOfCollateralX(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 borrowY = sumOfSeriesOfBorrowY(
            trancheStartSqrtPriceQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[1], depositX, 0);
        fixture.borrowFor(testers[1], 0, borrowY);
        Saturation.Account memory accountAfterFirstBorrow =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertEq(
            accountAfterFirstBorrow.satPairPerTranche.length,
            trancheCount,
            'Initial tranche count should match the number of tranches'
        );

        // add more deposits and check for unnecessary growth
        fixture.depositFor(testers[1], depositX * 10, 0);

        Saturation.Account memory accountAfterReAdd =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), false, testers[1]);

        assertGt(
            accountAfterReAdd.satPairPerTranche.length,
            trancheCount,
            'Array should be recalculated and greater after adding more deposit'
        );
    }

    function _countZeros(
        Saturation.SaturationPair[] memory arr
    ) private pure returns (uint256 count) {
        for (uint256 i = 0; i < arr.length; i++) {
            if (arr[i].satRelativeToL == 0) count++;
        }
    }

    function testRelativeAndAbsoluteSaturation() public {
        // similar to testTrancheLocationAcrossTwoTranchesXAgainstYAtPenaltyThreshold
        uint256 trancheStartSqrtPriceQ72 = 121_593 * Q72 / 100_000; // B^1 = 1.21593
        uint256 tranches = 2;
        uint256 desiredSaturationThresholdMag2 = 85;

        uint256 expectedRelativeSaturationPerTranche = activeActiveLiquidity * desiredSaturationThresholdMag2 / MAG2;

        uint256 borrowX = sumOfSeriesOfBorrowX(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            trancheStartSqrtPriceQ72, 100 * tranches, activeActiveLiquidity, desiredSaturationThresholdMag2
        );

        fixture.depositFor(testers[3], 0, depositY);
        fixture.borrowFor(testers[3], borrowX, 0);

        Saturation.Account memory account =
            fixture.saturationAndGeometricTWAPState().getAccount(fixture.pairAddress(), true, testers[3]);

        assertTrue(account.exists, 'account should exist');

        uint256 satArrayLength = account.satPairPerTranche.length;

        assertEq(satArrayLength, 3, 'account should have 3 tranches');

        uint256 totalAccountAbsoluteSat = 0;
        // temp arrays to store absolute and relative saturation for verification against account values
        // make sure array has length of 3 to match the number of tranches
        uint256[] memory tempAbsoluteSat = new uint256[](satArrayLength);
        uint256[] memory tempRelativeSat = new uint256[](satArrayLength);

        for (uint256 i = 0; i < satArrayLength; i++) {
            tempAbsoluteSat[i] = account.satPairPerTranche[i].satInLAssets;
            tempRelativeSat[i] = account.satPairPerTranche[i].satRelativeToL;
            totalAccountAbsoluteSat += tempAbsoluteSat[i];
        }

        // Both tranches should have the same relative saturation
        // because they can each hold up to expectedRelativeSaturationPerTranche
        assertEq(
            tempRelativeSat[0],
            expectedRelativeSaturationPerTranche,
            'First tranche relative saturation should equal expected'
        );
        assertEq(
            tempRelativeSat[1],
            expectedRelativeSaturationPerTranche,
            'Second tranche relative saturation should equal expected'
        );

        // third tranche should have much less saturation as we're at the penalty threshold
        assertLt(
            tempRelativeSat[2],
            expectedRelativeSaturationPerTranche / 100_000,
            'Third tranche should have minimal saturation'
        );

        uint256 totalTempAbsoluteSat = 0;
        for (uint256 i = 0; i < satArrayLength; i++) {
            totalTempAbsoluteSat += tempAbsoluteSat[i];
        }

        assertEq(
            totalTempAbsoluteSat,
            totalAccountAbsoluteSat,
            'Total temp absolute sat should equal total account absolute sat'
        );
    }

    function testMultipleBorrowCauseOverflow() public {
        // Add extra liquidity by tester2
        fixture.mintFor(testers[2], initialLDX * 10, initialLDY * 10);

        // Create a position that will be in penalty (same approach as testAccrueSaturationPenalties)
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95
        uint256 trancheSpanInTicks = 300; // 3 tranches of 100 ticks each

        uint256 depositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationRatioInMAG2
        );
        uint256 borrowX = sumOfSeriesOfBorrowX(
            targetLiqSqrtPriceXInQ72, trancheSpanInTicks, activeActiveLiquidity, desiredSaturationRatioInMAG2
        );

        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowForNoEvent(testers[1], borrowX, 0);

        // second borrow by tester2
        uint256 borrowAmountForBorrower2X = 40e18;
        uint256 borrowAmountForBorrower2Y = 10e18;

        fixture.borrowForNoEvent(testers[2], borrowAmountForBorrower2X, borrowAmountForBorrower2Y);

        assertEq(
            pair.tokens(BORROW_X).balanceOf(testers[2]),
            borrowAmountForBorrower2X,
            'Borrower 2 should have borrowed the expected amount of BORROW_X'
        );

        assertEq(
            pair.tokens(BORROW_Y).balanceOf(testers[2]),
            borrowAmountForBorrower2Y,
            'Borrower 2 should have borrowed the expected amount of BORROW_Y'
        );

        assertEq(
            pair.tokens(BORROW_X).balanceOf(testers[1]),
            borrowX,
            'Borrower 1 should have borrowed the expected amount of BORROW_X'
        );
    }

    /**
     * @notice Simple integration test for the new dynamic penalty system
     * @dev Validates that penalties are calculated and applied correctly when positions are over-saturated
     */
    function testDynamicPenaltySystem() public {
        // Add extra liquidity like other penalty tests do
        fixture.mintFor(testers[2], initialLDX, initialLDY);

        // Create a position that will be in penalty
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        // Use the same approach as testAccrueSaturationPenalties - using series functions
        uint256 borrowX =
            sumOfSeriesOfBorrowX(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(targetLiqSqrtPriceXInQ72, 300, activeActiveLiquidity, desiredSaturationRatioInMAG2);

        // Set saturation ratio
        vm.startPrank(testers[1]);
        fixture.saturationAndGeometricTWAPState().setNewPositionSaturation(
            fixture.pairAddress(), desiredSaturationRatioInMAG2
        );
        vm.stopPrank();

        // Create the position
        fixture.depositFor(testers[1], 0, depositY);
        fixture.borrowFor(testers[1], borrowX, 0);

        // burn liquidity to move the position into penalty (like testAccrueSaturationPenalties)
        fixture.burnFor(testers[2], Math.sqrt(initialLDX * initialLDY) * 10 / 200);

        // Verify we're in penalty
        ISaturationAndGeometricTWAPState satState = fixture.pair().exposed_saturationAndGeometricTWAPState();
        (uint16 netXHighestLeaf,) = satState.getTreeDetails(address(fixture.pair()), true);

        uint128[6] memory assets = pair.totalAssets();
        uint256 activeLiquidity = assets[DEPOSIT_L] - assets[BORROW_L];
        uint256 thresholdLeaf =
            Saturation.satToLeaf(activeLiquidity * Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 / MAG2);

        require(netXHighestLeaf >= thresholdLeaf, 'Position must be in penalty for test to be valid');

        // Record initial state
        uint256 initialDepositL = assets[DEPOSIT_L];

        // Time passes - this triggers penalty accrual
        vm.warp(block.timestamp + 1 days);
        vm.roll(block.number + Math.ceilDiv(1 days, 12));

        // Trigger penalty calculation through sync
        fixture.pair().sync();

        // Check final state
        uint128[6] memory finalAssets = pair.totalAssets();
        uint256 finalDepositL = finalAssets[DEPOSIT_L];
        uint256 finalBorrowL = finalAssets[BORROW_L];
        uint256 borrowUtilization = finalBorrowL / (finalDepositL);
        assertEq(borrowUtilization, 0, 'Borrow utilization should be 0');

        // Calculate penalty
        uint256 penaltyAccrued = finalDepositL - initialDepositL;

        // Basic assertions
        assertGt(finalDepositL, initialDepositL, 'LPs should receive penalty compensation');
        assertGt(penaltyAccrued, 0, 'Penalty should be positive');

        // Penalty should be meaningful (more than 0.01% per day)
        uint256 minMeaningfulPenalty = initialDepositL / 10_000; // 0.01%
        assertGt(penaltyAccrued, minMeaningfulPenalty, 'Penalty should be economically meaningful');
    }
}
