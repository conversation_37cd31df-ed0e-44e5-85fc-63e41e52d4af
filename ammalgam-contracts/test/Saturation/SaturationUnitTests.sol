// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {BitLib} from '@mangrovedao/mangrove-core/lib/core/BitLib.sol';

import {RAY} from 'test/InterestTests/InterestFixture.sol';
import {Interest} from 'contracts/libraries/Interest.sol';
import {WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';
import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {Liquidation} from 'contracts/libraries/Liquidation.sol';
import {Uint16Set} from 'contracts/libraries/Uint16Set.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Saturation} from 'contracts/libraries/Saturation.sol';
import {
    TRANCHE_B_IN_Q72,
    TRANCHE_QUARTER_B_IN_Q72,
    MAG1,
    MAG2,
    MINIMUM_LIQUIDITY,
    Q72,
    Q144,
    ZERO_ADDRESS,
    SAT_PERCENTAGE_DELTA_DEFAULT_WAD,
    SAT_PERCENTAGE_DELTA_4_WAD,
    SAT_PERCENTAGE_DELTA_5_WAD,
    SAT_PERCENTAGE_DELTA_6_WAD,
    SAT_PERCENTAGE_DELTA_7_WAD,
    SAT_PERCENTAGE_DELTA_8_WAD,
    SAT_PERCENTAGE_DELTA_DEFAULT_WAD,
    MAX_SATURATION_PERCENT_IN_WAD,
    MAX_UTILIZATION_PERCENT_IN_WAD,
    LIQUIDITY_INTEREST_RATE_MAGNIFICATION,
    SECONDS_IN_YEAR
} from 'contracts/libraries/constants.sol';
import {
    LXY,
    createLXYAssetsLDYB,
    createLXYAssetsYDLB,
    createLXYAssetsLDXB,
    createLXYAssetsXDLB,
    createLXYAssetsLDXBYBNetX,
    createLXYAssetsLDXDYB,
    createLXYAssetsXDYDLBNetX,
    createLXYAssetsYDLBXB,
    createLXYAssetsYDXB,
    createLXYAssetsXDYB,
    sumOfSeriesOfBorrowX,
    sumOfSeriesOfBorrowY,
    sumOfSeriesOfCollateralX,
    sumOfSeriesOfCollateralY,
    sumOfSeriesOfCollateralL,
    sumOfSeriesOfBorrowL,
    getSatPerTrancheOrZero
} from 'test/Saturation/SaturationTestUtils.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {FactoryPairTestFixture} from 'test/shared/FactoryPairTestFixture.sol';

contract SaturationUnitTests is Test {
    FactoryPairTestFixture private fixture;
    uint256 constant DEFAULT_MAX_UTILIZATION_IN_WAD = 1e17;

    address alice = vm.addr(456);

    Saturation.SaturationStruct satStruct;
    Saturation.SaturationStruct satStruct2;
    uint256 reserveX = 8e18;
    uint256 reserveY = 2e18;
    uint256 activeLiquidityAssets = 4e18;

    uint256 TRANCHE_0;
    uint256 TRANCHE_0_25;
    uint256 TRANCHE_0_5;
    uint256 TRANCHE_1;
    uint256 TRANCHE_2;
    uint256 TRANCHE_3;

    uint256 TRANCHE_393;
    uint256 TRANCHE_394;
    uint256 TRANCHE_395;
    uint256 TRANCHE_395_5;
    uint256 TRANCHE_395_75;
    uint256 TRANCHE_396;

    uint256 constant START_LIQ_IN_MAG2 = Saturation.EXPECTED_SATURATION_LTV_MAG2;
    uint256 constant START_LIQ_PLUS_ONE_IN_MAG2 = Saturation.EXPECTED_SATURATION_LTV_PLUS_ONE_MAG2;
    uint256 internal constant MAX_SATURATION_DISTRIBUTION_TRANCHES = 10;

    function setUp() public {
        Saturation.initializeSaturationStruct(satStruct);

        TRANCHE_0 = TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100) * 100);
        TRANCHE_0_25 = TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 25 + 3) * 25);
        TRANCHE_0_5 = TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 50 + 2) * 50);
        TRANCHE_1 = TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 1) * 100);
        TRANCHE_2 = TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 2) * 100);
        TRANCHE_3 = TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 3) * 100);

        TRANCHE_393 = TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 3) * 100);
        TRANCHE_394 = TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 2) * 100);
        TRANCHE_395 = TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 1) * 100);
        TRANCHE_395_5 = TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 50 - 2) * 50);
        TRANCHE_395_75 = TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 25 - 3) * 25);
        TRANCHE_396 = TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100) * 100);
    }

    function testTrancheLocationMinTrancheCountForOneTrancheXAgainstY() public view {
        uint256 sqrtPriceQ72 = 1784 * Q72 / 1000;
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2),
            100,
            'Min tranche count should be 1'
        );

        assertGe(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            200,
            'logb(1.50) = 2'
        );

        // add debt here we push it to the tranche towards the price
        borrowX = borrowX * 101 / 100;
        uint256 tranchePercent =
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2);

        assertApproxEqAbs(tranchePercent, 101, 1, 'Min tranche count should be 2 with a bit of added collateral');

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            301,
            'log_B(1.5) = 2 and with two tranches we end at 3'
        );
    }

    function testTrancheLocationMinTrancheCountXAgainstYMoveMakeDebtMoveTowardsPrice() public view {
        uint256 sqrtPriceQ72 = 9999 * Q72 / 10_000; // 0.9999
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 50; // 50%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2),
            100,
            'Min tranche count should be 1'
        );

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            -1,
            'logb(0.9999) =  -1'
        );

        // make debt move towards the price

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX * 101 / 100, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            5,
            'logb(1.01) = 5'
        );
    }

    function testTrancheLocationNegativeFourTranche() public view {
        uint256 sqrtPriceQ72 = 5 * Q72 / 10; // 0.5
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrow = sumOfSeriesOfBorrowX(sqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);

        // Test tranche 2 for various tranche counts and net debt X Left edge
        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(borrow, activeLiquidityAssets, 100, desiredThresholdMag2, true),
            -354,
            'logb(0.5) = -354 no tranche subtraction'
        );
    }

    function testTrancheLocationLastTrancheOneTranche() public view {
        uint256 sqrtPriceQ72 = 13_406 * Q72 / 10_000; // b^149.9
        verifyStartOfLiquidation(sqrtPriceQ72, true, 149, 'logb( b^149.5) = 149 netXDebt left of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, 149, '-logb( b^149.5) = 149 netYDebt left of edge');

        sqrtPriceQ72 = 13_408 * Q72 / 10_000; // b^150.1
        verifyStartOfLiquidation(sqrtPriceQ72, true, 150, 'logb(b^150.1) = 150.1 netXDebt right of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, 150, 'logb(b^150.1) = 150.1 netYDebt right of edge');

        sqrtPriceQ72 = 90_685 * Q72 / 100_000; // b^-50.1
        verifyStartOfLiquidation(sqrtPriceQ72, true, -50, 'logb(b^-50.1) = -50 netXDebt');
        verifyStartOfLiquidation(sqrtPriceQ72, false, -50, 'logb(b^-50.1) = -50 netYDebt');

        sqrtPriceQ72 = 907 * Q72 / 1000; // b^-49.9
        verifyStartOfLiquidation(sqrtPriceQ72, true, -49, 'logb(b^-50) = -49 netXDebt');
        verifyStartOfLiquidation(sqrtPriceQ72, false, -49, 'logb(b^-50) = -49 netYDebt');

        // test edge of tranche 0 and 1
        sqrtPriceQ72 = 1215 * Q72 / 1000; // 1.215
        verifyStartOfLiquidation(sqrtPriceQ72, true, 99, 'logb(1.215) = 99 netXDebt left of edge edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, 99, 'logb(1.215) = 99 netYDebt left of edge');
        sqrtPriceQ72 = 1216 * Q72 / 1000; // 1.216
        verifyStartOfLiquidation(sqrtPriceQ72, true, 100, 'logb(1.216) = 100 netXDebt right of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, 100, 'logb(1.216) = 100 netYDebt right of edge');

        // test edge of -3 and -2
        sqrtPriceQ72 = 67_635 * Q72 / 100_000; // 0.67635
        verifyStartOfLiquidation(sqrtPriceQ72, true, -200, 'logb(0.67635) = -200 netXDebt left of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, -200, 'logb(0.67635) = -200 netYDebt left of edge');
        sqrtPriceQ72 = 6764 * Q72 / 10_000; // 0.6764
        verifyStartOfLiquidation(sqrtPriceQ72, true, -199, 'logb(0.6764) = -199 netXDebt right of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, -199, 'logb(0.6764) = -199 netYDebt right of edge');

        /// test edge of -1 and 0
        sqrtPriceQ72 = 9999 * Q72 / 10_000; // 0.9999
        verifyStartOfLiquidation(sqrtPriceQ72, true, -1, 'logb(0.9999) = -1 netXDebt left of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, -1, 'logb(0.9999) = -1 netYDebt left of edge');
        sqrtPriceQ72 = Q72; // 1.0
        verifyStartOfLiquidation(sqrtPriceQ72, true, 0, 'logb(1.0) = 0 netXDebt right of edge');
        verifyStartOfLiquidation(sqrtPriceQ72, false, 0, 'logb(1.0) = 0 netYDebt right of edge');
    }

    function testOnceExampleNegY() public view {
        uint256 sqrtPriceQ72 = 6763 * Q72 / 10_000; // 0.6763
        verifyStartOfLiquidation(sqrtPriceQ72, false, -200, 'logb(0.6763) = -2 netYDebt left of edge');
    }

    function verifyStartOfLiquidation(
        uint256 sqrtPriceQ72,
        bool netXDebt,
        int16 expectedStartOfLiquidation,
        string memory message
    ) internal view {
        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                netXDebt
                    ? sumOfSeriesOfBorrowX(sqrtPriceQ72, 100, activeLiquidityAssets, 75)
                    : sumOfSeriesOfBorrowY(sqrtPriceQ72, 100, activeLiquidityAssets, 75),
                activeLiquidityAssets,
                100,
                75,
                netXDebt
            ),
            expectedStartOfLiquidation,
            message
        );
    }

    function testTrancheLocationMinTrancheCountXAgainstYMoveMakeDebtAwayFromPrice() public view {
        uint256 sqrtPriceQ72 = 11 * Q72 / 10; // 1.1
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 50; // 50%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2),
            100,
            'Min tranche count should be 1'
        );

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            48,
            'logb(1.1) = 48'
        );

        // make debt move towards the price

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX * 10 / 11, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            -1,
            'logb(1) = 0'
        );
    }

    function testTrancheLocationMinTrancheCountForOneTrancheXAgainstYTrancheSpan() public view {
        uint256 sqrtPriceQ72 = 1450 * Q72 / 1000;
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2),
            100,
            'Min tranche count should be 1'
        );

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            190,
            'logb(1.45) = 190'
        );

        // add a bit of collateral here we push it to the tranche towards the price
        collateralY = collateralY * 102 / 100;
        borrowX = borrowX * 102 / 100;

        uint256 newTranchePercent =
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(newTranchePercent, 102, 'Min tranche count should be 2 with a bit of added collateral');

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, newTranchePercent, desiredThresholdMag2, true
            ),
            200,
            'Since span does not fit in one tranche, we move to towards the price.'
        );
    }

    function testTrancheLocationMinTrancheCountForOneTrancheYAgainstX() public view {
        uint256 sqrtPriceQ72 = 32 * Q72 / 10; // 3.2
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowY =
            sumOfSeriesOfBorrowY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralX =
            sumOfSeriesOfCollateralX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcMinTrancheSpanInTicks(collateralX, borrowY, activeLiquidityAssets, desiredThresholdMag2),
            100,
            'Min tranche count should be 1'
        );

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowY, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, false
            ),
            594,
            'log_B(3.22) = 594'
        );

        borrowY = borrowY * 105 / 100;
        collateralX = collateralX * 105 / 100;

        uint256 tranchePercentage =
            Saturation.calcMinTrancheSpanInTicks(collateralX, borrowY, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(tranchePercentage, 105, 'Min tranche count should be 2 with a bit of added collateral');

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowY, activeLiquidityAssets, tranchePercentage, desiredThresholdMag2, false
            ),
            569,
            'log_B(3.22) = 596 with move towards the price due to increase.'
        );
    }

    function testTrancheLocationMinTrancheCountForSeriesOf4() public view {
        uint256 sqrtPriceQ72 = 15 * Q72 / 10;
        uint256 trancheCount = 4;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag2),
            400,
            'Min tranche count should be 4'
        );
    }

    function testTrancheLocationLastTrancheGivenCount4() public view {
        uint256 sqrtPriceQ72 = 1479 * Q72 / 1000; // 1.479 just barley starts at 2 and ends at -2
        uint256 trancheCount = 4;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            200,
            'First Tranche should be 2'
        );
    }

    function testTrancheLocationLastTrancheGivenCount4BackOne() public view {
        uint256 sqrtPriceQ72 = 1479 * Q72 / 1000; // 1.478 just barley starts at 2 and ends at -2
        uint256 trancheCount = 4;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        assertEq(
            Saturation.calcTrancheAtStartOfLiquidation(
                borrowX, activeLiquidityAssets, trancheCount * 100, desiredThresholdMag2, true
            ),
            200,
            'Tranche should be 2, since we start at 2 and subtract the count - 1'
        );
    }

    function testTrancheLocationMinTrancheCountTrancheFuzz(
        uint256 sqrtPriceQ72,
        uint8 desiredThresholdMag2,
        uint8 trancheCountUint8
    ) public view {
        // edges at extremes start to go higher due to rounding in the function.
        // the sooner you round up, the better your edge accuracy, but the worse
        // your edge accuracy in the middle cases. This will add more risk at the
        // tails. 1% accuracy within the 18th to 362nd tranche
        sqrtPriceQ72 = bound(
            sqrtPriceQ72,
            TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 17) * 100),
            TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 17) * 100)
        );
        uint256 trancheCount = bound(trancheCountUint8, 1, 20);
        uint256 desiredThresholdMag256 = bound(desiredThresholdMag2, 50, 95); // 75%

        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256);

        assertApproxEqAbs(
            Saturation.calcMinTrancheSpanInTicks(collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag256),
            trancheCount * 100,
            1,
            'Result should be the expected tranche count return'
        );
    }

    function testTrancheLocationMinTrancheCountTrancheFuzzOuterRange(
        uint128 sqrtPriceQ72,
        uint8 desiredThresholdMag2,
        uint8 trancheCountUint8
    ) public view {
        // 7th tranche to 18th tranche over estimate by 15%
        uint256 sqrtPriceQ72_256 = sqrtPriceQ72 > Q72
            ? bound(
                sqrtPriceQ72,
                TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 17) * 100),
                TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 6) * 100)
            )
            : bound(
                sqrtPriceQ72,
                TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 6) * 100),
                TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 17) * 100)
            );
        uint256 trancheCount = bound(trancheCountUint8, 1, 20);
        uint256 desiredThresholdMag256 = bound(desiredThresholdMag2, 50, 95); // 75%

        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72_256, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256);
        uint256 collateralY = sumOfSeriesOfCollateralY(
            sqrtPriceQ72_256, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256
        );

        assertApproxEqRel(
            uint256(
                Saturation.calcMinTrancheSpanInTicks(
                    collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag256
                )
            ),
            trancheCount * 100,
            2e16, // 2%
            'Result should be the expected tranche count return'
        );
    }

    function testTrancheLocationMinTrancheCountTrancheFuzzExtremeOuterRange(
        uint256 sqrtPriceQ72,
        uint8 desiredThresholdMag2,
        uint8 trancheCountUint8
    ) public view {
        // 7th tranche to 18th tranche over estimate by 15%
        uint256 sqrtPriceQ72_256 = sqrtPriceQ72 > Q72
            ? bound(sqrtPriceQ72, TickMath.getSqrtPriceAtTick((TickMath.MAX_TICK / 100 - 6) * 100), TRANCHE_393)
            : bound(sqrtPriceQ72, TRANCHE_3, TickMath.getSqrtPriceAtTick((TickMath.MIN_TICK / 100 + 6) * 100));
        uint256 trancheCount = bound(trancheCountUint8, 1, 3);
        uint256 desiredThresholdMag256 = bound(desiredThresholdMag2, 50, 95); // 75%

        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72_256, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256);
        uint256 collateralY = sumOfSeriesOfCollateralY(
            sqrtPriceQ72_256, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256
        );

        assertApproxEqRel(
            uint256(
                Saturation.calcMinTrancheSpanInTicks(
                    collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag256
                )
            ),
            trancheCount * 100,
            4e16, // 4%
            'Result should be the expected tranche count return'
        );
    }

    function testTrancheLocationMinTrancheCountTrancheFuzzEnd(
        uint256 sqrtPriceQ72,
        uint256 _activeLiquidityAssets,
        uint8 desiredThresholdMag2,
        uint8 trancheCountUint8
    ) public view {
        uint256 sqrtPriceQ72_256 = sqrtPriceQ72 > Q72
            ? bound(sqrtPriceQ72, TRANCHE_393, TRANCHE_396)
            : bound(sqrtPriceQ72, TRANCHE_0, TRANCHE_3);
        _activeLiquidityAssets = bound(_activeLiquidityAssets, 1000, uint256(type(uint112).max) ** 2);
        uint256 trancheCount = bound(trancheCountUint8, 1, 3);
        uint256 desiredThresholdMag256 = bound(desiredThresholdMag2, 50, 95); // 75%

        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72_256, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256);
        uint256 collateralY = sumOfSeriesOfCollateralY(
            sqrtPriceQ72_256, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag256
        );

        assertApproxEqRel(
            uint256(
                Saturation.calcMinTrancheSpanInTicks(
                    collateralY, borrowX, activeLiquidityAssets, desiredThresholdMag256
                )
            ),
            trancheCount * 100,
            8e16, // 8%
            'Result should be the expected tranche count return'
        );
    }

    function testTrancheLocationTrancheAndNetDebtXOneTranches() public view {
        uint256 sqrtPriceQ72 = Q72 / 2; // 0.5
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        uint256 mutatedSqrtPriceQ72 = sqrtPriceQ72 * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, collateralY, 0, borrowX, 0],
            sqrtPriceMinInQ72: mutatedSqrtPriceQ72, // ignored
            sqrtPriceMaxInQ72: mutatedSqrtPriceQ72, // ignored
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0,
            reservesYAssets: 0
        });

        // calc sat
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);
        (uint256 debt, uint256 collateral,) = Validation.calcDebtAndCollateral(checkLtvParams);

        assertEq(Saturation.EXPECTED_SATURATION_LTV_MAG2, debt * MAG2 / collateral, 'ltv should match expected');

        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, true);

        assertEq(trancheAtStartOfLiquidation, -454, 'logb(0.5)=355 move back 1');

        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * desiredThresholdMag2 / MAG2,
            0.11e16, // 0.11%
            'relative saturation should match the active liquidity assets * desired threshold'
        );
    }

    function testTrancheLocationTrancheAndNetDebtXTwoTranches() public view {
        uint256 sqrtPriceQ72 = 9999 * Q72 / 10_000; // 0.9999
        uint256 trancheCount = 2;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, collateralY, 0, borrowX, 0],
            sqrtPriceMinInQ72: sqrtPriceQ72 * Q72 / TRANCHE_B_IN_Q72,
            sqrtPriceMaxInQ72: sqrtPriceQ72 * Q72 / TRANCHE_B_IN_Q72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });

        (int256 lastTranche, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, true);

        assertEq(lastTranche, -201, 'log(.9999) = -1 less 2 tranches');
        assertApproxEqRel(
            saturationPair.satRelativeToL,
            // first tranche furthest from the price, plus the second that has an extra factor of B
            (activeLiquidityAssets + activeLiquidityAssets * TRANCHE_B_IN_Q72 / Q72) * desiredThresholdMag2 / MAG2,
            0.186e16, // 0.186%
            'saturation should match expected active liquidity for two tranches with an extra factor of B.'
        );
    }

    function testTrancheLocationTrancheAndNetDebtXThreeTranches() public view {
        uint256 sqrtPriceQ72 = Q72;
        uint256 trancheCount = 3;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, collateralY, 0, borrowX, 0],
            sqrtPriceMinInQ72: sqrtPriceQ72 * Math.sqrt(TRANCHE_B_IN_Q72 * Q72) / Q72 * TRANCHE_B_IN_Q72 / Q72, // 0.2 / B
            sqrtPriceMaxInQ72: sqrtPriceQ72 * Math.sqrt(TRANCHE_B_IN_Q72 * Q72) / Q72 * TRANCHE_B_IN_Q72 / Q72, // 0.2 * B
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });

        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, true);

        assertEq(
            trancheAtStartOfLiquidation, -300, 'trancheAtStartOfLiquidation should be 0 less the span of 3 tranches'
        );
        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * (Q72 + TRANCHE_B_IN_Q72 + TRANCHE_B_IN_Q72 * TRANCHE_B_IN_Q72 / Q72) / Q72
                * desiredThresholdMag2 / MAG2,
            1,
            'saturation should match three tranches with an extra factor of B in each iteration.'
        );
    }

    function testTrancheLocationTrancheAndNetDebtYHalfTranches() public view {
        uint256 sqrtPriceQ72 = 30_776 * Q72 / 10_000; // 3.0776
        uint256 trancheCountPercent = 50; // cut the debt in half since the helpers don't support partial tranches.
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowY =
            sumOfSeriesOfBorrowY(sqrtPriceQ72, trancheCountPercent, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralX =
            sumOfSeriesOfCollateralX(sqrtPriceQ72, trancheCountPercent, activeLiquidityAssets, desiredThresholdMag2);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), collateralX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: sqrtPriceQ72 * Math.sqrt(Math.sqrt(TRANCHE_B_IN_Q72 * Q72) * TRANCHE_B_IN_Q72) / Q72,
            sqrtPriceMaxInQ72: sqrtPriceQ72 * Math.sqrt(Math.sqrt(TRANCHE_B_IN_Q72 * Q72) * TRANCHE_B_IN_Q72) / Q72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });

        (int256 lastTranche, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, false);

        assertEq(lastTranche, 625, 'logb(3.0813)=575, and we move away from the price by 50%.');
        assertApproxEqRel(
            saturationPair.satRelativeToL,
            (activeLiquidityAssets / 2 * Q72 / TRANCHE_QUARTER_B_IN_Q72) * desiredThresholdMag2 / MAG2,
            0.12e16, // 0.12%
            'The price used here is sqrtPrice / B^(1/2) so we multiply L * B^(1/2) *(B^(1/2)^(1/2)) which is L B ^ (3/4)'
        );
    }

    function testTrancheLocationTrancheAndNetDebtYTwoTranches() public view {
        uint256 sqrtPriceQ72 = Q72;
        uint256 trancheCount = 2;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowY =
            sumOfSeriesOfBorrowY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralX =
            sumOfSeriesOfCollateralX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), collateralX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: sqrtPriceQ72 * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72), // 0.2 / B
            sqrtPriceMaxInQ72: sqrtPriceQ72 * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72), // 0.2 / B
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });
        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, false);

        assertEq(trancheAtStartOfLiquidation, 200, 'logb(1) = 0 less 2 tranches');
        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * (Q72 + TRANCHE_B_IN_Q72) / Q72 * desiredThresholdMag2 / MAG2,
            0, // 0.186%
            'relative saturation should match expected active liquidity for two tranches with an extra factor of B.'
        );
    }

    function testTrancheLocationTrancheAndNetDebtYFourTranches() public view {
        uint256 sqrtPriceQ72 = 4574 * Q72 / 10_000; // 0.4575
        uint256 trancheCount = 4;
        uint256 desiredThresholdMag2 = 50; // 75%
        uint256 borrowY =
            sumOfSeriesOfBorrowY(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralX =
            sumOfSeriesOfCollateralX(sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), collateralX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: sqrtPriceQ72 * TRANCHE_B_IN_Q72 / Q72 * TRANCHE_B_IN_Q72 / Q72,
            sqrtPriceMaxInQ72: sqrtPriceQ72 * TRANCHE_B_IN_Q72 / Q72 * TRANCHE_B_IN_Q72 / Q72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) =
            Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, false);

        assertEq(trancheAtStartOfLiquidation, 0, 'logb(0.4575)=-400 less 4 tranches');
        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets
                * (
                    Q72 + TRANCHE_B_IN_Q72 + TRANCHE_B_IN_Q72 * TRANCHE_B_IN_Q72 / Q72
                        + TRANCHE_B_IN_Q72 * TRANCHE_B_IN_Q72 / Q72 * TRANCHE_B_IN_Q72 / Q72
                ) / Q72 * desiredThresholdMag2 / MAG2,
            0.0185e16, // 0.0185%
            'relative saturation should match the active liquidity assets * desired threshold'
        );
    }

    function testTrancheLocationTrancheAndNetDebtXAgainstL() public view {
        uint256 sqrtPriceQ72 = Q72; // B^0
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75; // 75%

        uint256 borrowX = sumOfSeriesOfBorrowX(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2
        ) * (MAG2 + Saturation.EXPECTED_SATURATION_LTV_MAG2) / Saturation.EXPECTED_SATURATION_LTV_MAG2;

        uint256 collateralL = sumOfSeriesOfCollateralL(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2, true
        ) * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72); // convert to L

        uint256 mutatedSqrtPriceQ72 = sqrtPriceQ72 * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [collateralL, 0, 0, 0, borrowX, 0],
            sqrtPriceMinInQ72: mutatedSqrtPriceQ72, // 0.2 / B
            sqrtPriceMaxInQ72: mutatedSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        // calc sat
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);
        (uint256 debt, uint256 collateral,) = Validation.calcDebtAndCollateral(checkLtvParams);

        assertApproxEqAbs(
            Saturation.EXPECTED_SATURATION_LTV_MAG2, debt * MAG2 / collateral, 1, 'ltv should match expected'
        );

        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, true);

        assertEq(trancheAtStartOfLiquidation, -100, 'log(1)=0 and move back 1');
        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * desiredThresholdMag2 / MAG2,
            0.0029e16,
            'relative saturation should match the active liquidity assets * desired threshold'
        );
    }

    function testTrancheLocationTrancheAndNetDebtYAgainstL() public view {
        uint256 sqrtPriceQ72 = Q72; // 1
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75;
        // The net debt is smaller since L is half Y, we scale it up by the needed proportion to
        // scale to a full tranche by making the threshold bigger.
        uint256 borrowY = sumOfSeriesOfBorrowY(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2
        ) * (MAG2 + Saturation.EXPECTED_SATURATION_LTV_MAG2) / Saturation.EXPECTED_SATURATION_LTV_MAG2; // adjust for borrowed L $$ debt = collateral * \frac{tlv}{1-ltv}

        uint256 collateralL = sumOfSeriesOfCollateralL(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2, false
        ) * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72);

        uint256 mutatedSqrtPriceQ72 = sqrtPriceQ72 * Math.sqrt(TRANCHE_B_IN_Q72 * Q72) / Q72;

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [collateralL, 0, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: mutatedSqrtPriceQ72, // ignored
            sqrtPriceMaxInQ72: mutatedSqrtPriceQ72, // ignored
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        // calc sat
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);
        (uint256 debt, uint256 collateral,) = Validation.calcDebtAndCollateral(checkLtvParams);

        assertApproxEqAbs(
            debt * MAG2 / collateral, Saturation.EXPECTED_SATURATION_LTV_MAG2, 1, 'ltv should match expected'
        );

        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, false);

        assertEq(trancheAtStartOfLiquidation, 100, 'logb(1) = 0, less one tranche');

        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * desiredThresholdMag2 / MAG2,
            0.0029e16, // 0.0029%
            'relative saturation should match the active liquidity assets * desired threshold'
        );
    }

    function testTrancheLocationTrancheAndNetDebtLAgainstY() public view {
        uint256 sqrtPriceQ72 = 11 * Q72 / 10; // 1.1
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowL = sumOfSeriesOfBorrowL(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2
        ) * Math.sqrt(TRANCHE_B_IN_Q72 * Q72) / Q72; // convert to L from x and increase for ltv diff.
        uint256 collateralY = sumOfSeriesOfCollateralY(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2
        ) * (MAG2 + Saturation.EXPECTED_SATURATION_LTV_MAG2) / MAG2; // adjust for borrowed L $$ debt = collateral * \frac{tlv}{1-ltv}

        uint256 mutatedSqrtPriceQ72 = sqrtPriceQ72 * Q72 / Math.sqrt(TRANCHE_B_IN_Q72 * Q72);

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, collateralY, borrowL, 0, 0],
            sqrtPriceMinInQ72: mutatedSqrtPriceQ72,
            sqrtPriceMaxInQ72: mutatedSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        // calc sat
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);
        (uint256 debt, uint256 collateral,) = Validation.calcDebtAndCollateral(checkLtvParams);
        assertApproxEqAbs(
            debt * MAG2 / collateral, Saturation.EXPECTED_SATURATION_LTV_MAG2, 1, 'ltv should match expected'
        );

        (int256 trancheAtStartOfLiquidation, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, true);

        assertApproxEqAbs(trancheAtStartOfLiquidation, -52, 3, 'log(1.1) = 48 move back 1');

        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * desiredThresholdMag2 / MAG2,
            0.147e16, // 0.147%
            'relative saturation should match the active liquidity assets * desired threshold'
        );
    }

    function testTrancheLocationTrancheAndNetDebtLAgainstX() public view {
        uint256 sqrtPriceQ72 = 25 * Q72 / 10; // 2.5
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 20; // 20%
        uint256 borrowL = sumOfSeriesOfBorrowL(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2
        ) * Math.sqrt(TRANCHE_B_IN_Q72 * Q72) / Q72; // convert to L scale by diff to ltv
        uint256 collateralX = sumOfSeriesOfCollateralX(
            sqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2
        ) * (MAG2 + Saturation.EXPECTED_SATURATION_LTV_MAG2) / MAG2; // adjust for borrowed L $$ debt = collateral * \frac{tlv}{1-ltv}

        uint256 mutatedSqrtPriceQ72 = sqrtPriceQ72 * Math.sqrt(TRANCHE_B_IN_Q72 * Q72) / Q72;

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, collateralX, 0, borrowL, 0, 0],
            sqrtPriceMinInQ72: mutatedSqrtPriceQ72,
            sqrtPriceMaxInQ72: mutatedSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        // calc sat
        Validation.CheckLtvParams memory checkLtvParams = Validation.getCheckLtvParams(inputParams);
        (uint256 debt, uint256 collateral,) = Validation.calcDebtAndCollateral(checkLtvParams);
        assertApproxEqAbs(
            debt * MAG2 / collateral, Saturation.EXPECTED_SATURATION_LTV_MAG2, 1, 'ltv should match expected'
        );

        (int256 lastTranche, Saturation.SaturationPair memory saturationPair) = // use relative sat
         Saturation.calcLastTrancheAndSaturation(inputParams, sqrtPriceQ72, desiredThresholdMag2, false);

        assertApproxEqAbs(lastTranche, 568, 3, 'log(2.5) = 468 + 100 = 568');

        assertApproxEqRel(
            saturationPair.satRelativeToL,
            activeLiquidityAssets * desiredThresholdMag2 / MAG2,
            0.145e16,
            'relative saturation should match the active liquidity assets * desired threshold'
        );
    }

    function testTrancheLocationRatioOfSeriesBorrowXCollateralY() public view {
        uint256 sqrtPriceQ72 = 3 * Q72 / 10; // 3 << 72;

        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowX = sumOfSeriesOfBorrowX(sqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralY = sumOfSeriesOfCollateralY(sqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 sqrtPrice2Q72 = sqrtPriceQ72 * Q72 / TRANCHE_B_IN_Q72; // 1.5 / B

        assertApproxEqRel(
            borrowX,
            Math.mulDiv(
                Math.mulDiv(collateralY * Saturation.EXPECTED_SATURATION_LTV_MAG2, sqrtPriceQ72, MAG2 * Q72),
                sqrtPrice2Q72,
                Q72
            ),
            10,
            'ratio between borrowX and collateralY should match the expected ltv '
        );
    }

    function testTrancheLocationRatioOfSeriesBorrowYCollateralX() public view {
        uint256 sqrtPriceQ72 = 3 * Q72; // 1.5 << 72;

        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 borrowY = sumOfSeriesOfBorrowY(sqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 collateralX = sumOfSeriesOfCollateralX(sqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 sqrtPrice2Q72 = sqrtPriceQ72 * TRANCHE_B_IN_Q72 / Q72; // 1.5 * B

        assertApproxEqRel(
            borrowY,
            Math.mulDiv(collateralX, Saturation.EXPECTED_SATURATION_LTV_MAG2 * Q72, MAG2 * sqrtPriceQ72) * Q72
                / sqrtPrice2Q72,
            10,
            'ratio between borrowY and collateralX should match the expected ltv '
        );
    }

    // test helpers

    function userPositionFromLXY(
        LXY memory position
    ) internal pure returns (uint256[6] memory userPosition) {
        userPosition = [position.LD, position.XD, position.YD, position.LB, position.XB, position.YB];
    }

    function testSaturationUpdateYAgainstX() public {
        uint256 sqrtPriceXInQ72 = Q72; // 1
        uint256 desiredThresholdMag2 = 75; // 75%
        uint256 depositX = sumOfSeriesOfCollateralX(sqrtPriceXInQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 borrowY = sumOfSeriesOfBorrowY(sqrtPriceXInQ72, 100, activeLiquidityAssets, desiredThresholdMag2);

        uint256 expectedLiquidity = activeLiquidityAssets * desiredThresholdMag2 / MAG2;

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), depositX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: sqrtPriceXInQ72,
            sqrtPriceMaxInQ72: sqrtPriceXInQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });

        Saturation.update(satStruct, inputParams, vm.addr(1), desiredThresholdMag2);

        Saturation.Account memory account = satStruct.netYTree.accountData[vm.addr(1)];
        assertEq(account.exists, true);
        assertEq(account.lastTranche, 1, 'logB(1) = 0 plus 1 tranche');
        assertApproxEqRel(account.satPairPerTranche[0].satRelativeToL, expectedLiquidity, 1);
        assertEq(
            getSatPerTrancheOrZero(account.satPairPerTranche, 1).satRelativeToL, 0, 'secondTranche should be empty'
        );
    }

    function testSaturationUpdateXAgainstYTwoTranches() public {
        uint256 sqrtPriceXInQ72 = 121_593 * Q72 / 100_000; // B^1 = 1.21593
        uint256 trancheCount = 2;
        uint256 desiredThresholdMag2 = 84; // 75%
        uint256 depositY =
            sumOfSeriesOfCollateralY(sqrtPriceXInQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 borrowX =
            sumOfSeriesOfBorrowX(sqrtPriceXInQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        uint256 expectedLiquidity = activeLiquidityAssets * desiredThresholdMag2 / MAG2;

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: 0, // ignored
            sqrtPriceMaxInQ72: 0, // ignored
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: reserveX,
            reservesYAssets: reserveY
        });

        Saturation.update(satStruct, inputParams, vm.addr(1), desiredThresholdMag2);

        Saturation.Account memory account = satStruct.netXTree.accountData[vm.addr(1)];
        assertEq(account.exists, true);
        assertEq(account.lastTranche, -1, 'logB(12159) = 1 less 2 goes to the -1 tranche.');
        assertEq(account.satPairPerTranche[0].satRelativeToL, expectedLiquidity, 'firstTranche should match expected');
        assertEq(account.satPairPerTranche[1].satRelativeToL, expectedLiquidity, 'secondTranche should match expected');
        assertLt(
            account.satPairPerTranche[2].satRelativeToL, expectedLiquidity / 100_000, 'thirdTranche should be empty'
        );
    }

    function testMutateInputParamsForPartialLiquidation_XAgainstYOneTranche() public {
        uint256 startSqrtPriceQ72 = 45_748 * Q72 / 100_000; // B^-4
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 90;
        bool netDebtX = true;

        uint256 borrowX =
            sumOfSeriesOfBorrowX(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 exchangeRateSqrtPriceQ72 =
            Math.sqrt(Math.mulDiv(borrowX * MAG2, Q72 ** 2, depositY * Saturation.EXPECTED_SATURATION_LTV_MAG2));

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        Saturation.update(satStruct, inputParams, alice, desiredThresholdMag2);

        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertXToL(borrowX, exchangeRateSqrtPriceQ72, Q72, false),
            netDebtX
        );

        assertEq(inputParams.userAssets[BORROW_X], borrowX, 'borrowX should be the same');
        assertEq(inputParams.userAssets[DEPOSIT_Y], depositY, 'depositY should be the same');
    }

    function testMutateInputParamsForPartialLiquidation_YAgainstXOneTranche() public {
        uint256 startSqrtPriceQ72 = 477_803 * Q72 / 100_000; // B^8
        uint256 trancheCount = 1;
        uint256 desiredThresholdMag2 = 90;
        bool netDebtX = false;

        uint256 borrowY =
            sumOfSeriesOfBorrowY(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 depositX =
            sumOfSeriesOfCollateralX(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 exchangeRateSqrtPriceQ72 =
            Math.sqrt(Math.mulDiv(depositX * Saturation.EXPECTED_SATURATION_LTV_MAG2, Q72 ** 2, borrowY * MAG2));

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), depositX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        Saturation.update(satStruct, inputParams, alice, desiredThresholdMag2);

        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertYToL(borrowY, exchangeRateSqrtPriceQ72, Q72, true),
            netDebtX
        );

        assertEq(inputParams.userAssets[BORROW_Y], borrowY, 'borrowY should be the same');
        assertEq(inputParams.userAssets[DEPOSIT_X], depositX, 'depositX should be the same');
    }

    function testMutateInputParamsForPartialLiquidation_XAgainstYFourTranches() public {
        uint256 startSqrtPriceQ72 = 45_748 * Q72 / 100_000; // B^-4
        uint256 trancheCount = 4;
        uint256 desiredThresholdMag2 = 50;
        bool netDebtX = true;
        uint256 borrowX =
            sumOfSeriesOfBorrowX(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 depositY =
            sumOfSeriesOfCollateralY(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        uint256 exchangeRateSqrtPriceQ72 =
            Math.sqrt(Math.mulDiv(borrowX * MAG2, Q72 ** 2, depositY * Saturation.EXPECTED_SATURATION_LTV_MAG2));

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        Saturation.update(satStruct, inputParams, alice, desiredThresholdMag2);

        // one tranche
        uint256 trancheBorrowX =
            sumOfSeriesOfBorrowX(startSqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 trancheDepositY =
            sumOfSeriesOfCollateralY(startSqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);

        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertXToL(trancheBorrowX, exchangeRateSqrtPriceQ72, Q72, false),
            netDebtX
        );

        assertApproxEqRel(
            inputParams.userAssets[BORROW_X], trancheBorrowX, 0.01e16, 'borrowX should be the same for one tranche'
        );
        assertApproxEqRel(
            inputParams.userAssets[DEPOSIT_Y], trancheDepositY, 0.01e16, 'depositY should be the same for one tranche'
        );

        // two tranches
        inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        trancheBorrowX = sumOfSeriesOfBorrowX(startSqrtPriceQ72, 200, activeLiquidityAssets, desiredThresholdMag2);
        trancheDepositY = sumOfSeriesOfCollateralY(startSqrtPriceQ72, 200, activeLiquidityAssets, desiredThresholdMag2);
        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertXToL(trancheBorrowX, exchangeRateSqrtPriceQ72, Q72, false),
            netDebtX
        );

        assertApproxEqRel(
            inputParams.userAssets[BORROW_X], trancheBorrowX, 0.01e16, 'borrowX should be the same for two tranches'
        );
        assertApproxEqRel(
            inputParams.userAssets[DEPOSIT_Y], trancheDepositY, 0.01e16, 'depositY should be the same for two tranches'
        );

        // three tranches
        inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        trancheBorrowX = sumOfSeriesOfBorrowX(startSqrtPriceQ72, 300, activeLiquidityAssets, desiredThresholdMag2);
        trancheDepositY = sumOfSeriesOfCollateralY(startSqrtPriceQ72, 300, activeLiquidityAssets, desiredThresholdMag2);
        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertXToL(trancheBorrowX, exchangeRateSqrtPriceQ72, Q72, false),
            netDebtX
        );

        assertApproxEqRel(
            inputParams.userAssets[BORROW_X], trancheBorrowX, 0.01e16, 'borrowX should be the same for three tranches'
        );
        assertApproxEqRel(
            inputParams.userAssets[DEPOSIT_Y],
            trancheDepositY,
            0.01e16,
            'depositY should be the same for three tranches'
        );
    }

    function testMutateInputParamsForPartialLiquidation_YAgainstXFourTranches() public {
        uint256 startSqrtPriceQ72 = 477_803 * Q72 / 100_000; // B^8
        uint256 trancheCount = 4;
        uint256 desiredThresholdMag2 = 50;
        bool netDebtX = false;

        uint256 borrowY =
            sumOfSeriesOfBorrowY(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);
        uint256 depositX =
            sumOfSeriesOfCollateralX(startSqrtPriceQ72, 100 * trancheCount, activeLiquidityAssets, desiredThresholdMag2);

        uint256 exchangeRateSqrtPriceQ72 =
            Math.sqrt(Math.mulDiv(depositX * Saturation.EXPECTED_SATURATION_LTV_MAG2, Q72 ** 2, borrowY * MAG2));

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), depositX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        Saturation.update(satStruct, inputParams, alice, desiredThresholdMag2);

        // one tranche
        uint256 trancheBorrowY =
            sumOfSeriesOfBorrowY(startSqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);
        uint256 trancheDepositX =
            sumOfSeriesOfCollateralX(startSqrtPriceQ72, 100, activeLiquidityAssets, desiredThresholdMag2);

        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertYToL(trancheBorrowY, exchangeRateSqrtPriceQ72, Q72, true),
            netDebtX
        );

        assertApproxEqRel(
            inputParams.userAssets[BORROW_Y], trancheBorrowY, 0.01e16, 'borrowY should be the same for one tranche'
        );
        assertApproxEqRel(
            inputParams.userAssets[DEPOSIT_X], trancheDepositX, 0.01e16, 'depositX should be the same for one tranche'
        );

        // two tranches
        inputParams = inputParams = Validation.InputParams({
            userAssets: [uint256(0), depositX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        trancheBorrowY = sumOfSeriesOfBorrowY(startSqrtPriceQ72, 200, activeLiquidityAssets, desiredThresholdMag2);
        trancheDepositX = sumOfSeriesOfCollateralX(startSqrtPriceQ72, 200, activeLiquidityAssets, desiredThresholdMag2);

        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertYToL(trancheBorrowY, exchangeRateSqrtPriceQ72, Q72, true),
            netDebtX
        );

        assertApproxEqRel(
            inputParams.userAssets[BORROW_Y], trancheBorrowY, 0.01e16, 'borrowY should be the same for two tranches'
        );
        assertApproxEqRel(
            inputParams.userAssets[DEPOSIT_X], trancheDepositX, 0.01e16, 'depositX should be the same for two tranches'
        );

        // three tranches
        inputParams = inputParams = Validation.InputParams({
            userAssets: [uint256(0), depositX, 0, 0, 0, borrowY],
            sqrtPriceMinInQ72: exchangeRateSqrtPriceQ72,
            sqrtPriceMaxInQ72: exchangeRateSqrtPriceQ72,
            activeLiquidityScalerInQ72: Q72,
            activeLiquidityAssets: activeLiquidityAssets,
            reservesXAssets: 0, // ignored
            reservesYAssets: 0 // ignored
        });

        trancheBorrowY = sumOfSeriesOfBorrowY(startSqrtPriceQ72, 300, activeLiquidityAssets, desiredThresholdMag2);
        trancheDepositX = sumOfSeriesOfCollateralX(startSqrtPriceQ72, 300, activeLiquidityAssets, desiredThresholdMag2);

        Saturation.mutateInputParamsForPartialLiquidation(
            satStruct,
            inputParams,
            alice,
            Validation.convertYToL(trancheBorrowY, exchangeRateSqrtPriceQ72, Q72, true),
            netDebtX
        );

        assertApproxEqRel(
            inputParams.userAssets[BORROW_Y], trancheBorrowY, 0.01e16, 'borrowY should be the same for three tranches'
        );
        assertApproxEqRel(
            inputParams.userAssets[DEPOSIT_X],
            trancheDepositX,
            0.01e16,
            'depositX should be the same for three tranches'
        );
    }

    /**
     * @dev math for calc
     * ```math
     *   \sqrt{L_P} &= -\frac{L (Ltv + 1)}{Y\cdot Ltv}
     * ```
     */
    function testCalcLiqSqrtPrice_borrowLAgainstY() public pure {
        uint256 lX = 1e18;
        uint256 lY = 4e18;
        uint256 borrowL = Math.sqrt(lX * lY);
        uint256 depositY = 1e18;
        uint256[6] memory userAssets = [0, 0, depositY, borrowL, 0, 0];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 expectedLiqSqrtPrice1 = Q72 * borrowL * (START_LIQ_PLUS_ONE_IN_MAG2) / (depositY * START_LIQ_IN_MAG2);

        assertEq(liqSqrtPrice1, expectedLiqSqrtPrice1, 'liq price should be L * (LTV + 1) / (Y * LTV)');
        assertEq(liqSqrtPrice2, 0);
    }

    /**
     * @dev math for calc
     * ```math
     *   \sqrt{L_P} &= -\frac{L (Ltv + 1)}{Y}
     * ```
     */
    function testCalcLiqSqrtPrice_borrowYAgainstL() public pure {
        uint256 lX = 1e18;
        uint256 lY = 4e18;
        uint256 depositL = Math.sqrt(lX * lY);
        uint256 borrowY = 1e18;
        uint256[6] memory userAssets = [depositL, 0, 0, 0, 0, borrowY];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 expectedLiqSqrtPrice2 = Q72 * depositL * (START_LIQ_PLUS_ONE_IN_MAG2) / (borrowY * MAG2);

        assertEq(liqSqrtPrice1, 0);
        assertEq(liqSqrtPrice2, expectedLiqSqrtPrice2, 'liq price should be L * (LTV + 1) / Y');
    }

    /**
     * @dev math for calc
     * ```math
     *   \sqrt{L_P} &= - \frac{X}{L (Ltv + 1)}
     * ```
     */
    function testCalcLiqSqrtPrice_borrowXAgainstL() public pure {
        uint256 lX = 1e18;
        uint256 lY = 4e18;
        uint256 depositL = Math.sqrt(lX * lY);
        uint256 borrowX = 0.5e18;
        uint256[6] memory userAssets = [depositL, 0, 0, 0, borrowX, 0];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 expectedLiqSqrtPrice1 = Q72 * borrowX * MAG2 / (depositL * (START_LIQ_PLUS_ONE_IN_MAG2));

        assertApproxEqRel(liqSqrtPrice1, expectedLiqSqrtPrice1, 0, 'liq price should be X / (L * (LTV + 1))');
        assertEq(liqSqrtPrice2, 0);
    }

    /**
     * @dev math for calc
     * ```math
     *   \sqrt{L_P} &= - \frac{X\cdot Ltv}{L (Ltv + 1)}
     * ```
     */
    function testCalcLiqSqrtPrice_borrowLAgainstX() public pure {
        uint256 lX = 1e18;
        uint256 lY = 4e18;
        uint256 borrowL = Math.sqrt(lX * lY);
        uint256 depositX = 5e18;
        uint256[6] memory userAssets = [0, depositX, 0, borrowL, 0, 0];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 expectedLiqSqrtPrice2 = Q72 * depositX * START_LIQ_IN_MAG2 / (borrowL * (START_LIQ_PLUS_ONE_IN_MAG2));

        assertEq(liqSqrtPrice1, 0);
        assertEq(liqSqrtPrice2, expectedLiqSqrtPrice2, 'liq price should be (X * LTV) / (L * (LTV + 1))');
    }

    /**
     * @dev math for calc
     * ```math
     *   \sqrt{L_P} &= -\frac{X}{Y \cdot Ltv}
     * ```
     */
    function testCalcLiqSqrtPrice_borrowXAgainstY() public pure {
        uint256 depositY = 6e18;
        uint256 borrowX = 2e18;
        uint256[6] memory userAssets = [0, 0, depositY, 0, borrowX, 0];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 expectedLiqSqrtPrice1 = Math.sqrt(Q144 * borrowX * MAG2 / (depositY * START_LIQ_IN_MAG2));

        assertApproxEqAbs(liqSqrtPrice1, expectedLiqSqrtPrice1, 1, 'liq price should be sqrt(X / (Y * LTV))');
        assertEq(liqSqrtPrice2, 0);
    }

    /**
     * @dev math for calc
     * ```math
     *   \sqrt{L_P} &= -\frac{X\cdot Ltv}{Y}
     * ```
     */
    function testCalcLiqSqrtPrice_borrowYAgainstX() public pure {
        uint256 depositX = 6e18;
        uint256 borrowY = 2e18;
        uint256[6] memory userAssets = [0, depositX, 0, 0, 0, borrowY];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 expectedLiqSqrtPrice2 = Math.sqrt(Q144 * depositX * START_LIQ_IN_MAG2 / (borrowY * MAG2));

        assertEq(liqSqrtPrice1, 0);
        assertApproxEqAbs(liqSqrtPrice2, expectedLiqSqrtPrice2, 1, 'liq price should be sqrt(X * LTV / Y)');
    }

    /**
     * @dev math for calc
     * ```math
     * \begin{align*}
     *     \sqrt{L_P} &= \frac{-L (Ltv + 1) \pm \sqrt{\left( L (Ltv + 1)\right)^2 - 4 \cdot X\cdot Y \cdot Ltv}}{2\cdot Y \cdot Ltv} \\
     *     \sqrt{L_P} &= \frac{-L (Ltv + 1) \pm \sqrt{\left(L (Ltv + 1)\right)^2 - 4 \cdot Y \cdot X \cdot Ltv}}{2\cdot Y}
     * \end{align*}
     * ```
     */
    function testCalcLiqSqrtPrice_XYAndL() public pure {
        uint256 mintL = 20e18;
        uint256 borrowX = 10e18;
        uint256 borrowY = 5e18;

        uint256[6] memory userAssets = [mintL, 0, 0, 0, borrowX, borrowY];

        (uint256 liqSqrtPrice1, uint256 liqSqrtPrice2) = Saturation.calcLiqSqrtPriceQ72(userAssets);

        uint256 aNegative = borrowY * START_LIQ_IN_MAG2 / MAG2;
        uint256 b = mintL * (START_LIQ_PLUS_ONE_IN_MAG2) / MAG2;
        uint256 cNegative = borrowX;

        uint256 expectedLiqSqrtPrice1 =
            (b * Q72 - 2 ** 36 * Math.sqrt((b * b - 4 * aNegative * cNegative) * Q72)) / (2 * aNegative);

        uint256 expectedLiqSqrtPrice2 =
            (b * Q72 + 2 ** 36 * Math.sqrt((b * b - 4 * aNegative * cNegative) * Q72)) / (2 * borrowY);

        assertApproxEqRel(liqSqrtPrice1, expectedLiqSqrtPrice1, 0, 'liq price is solved with quadratic formula');
        assertApproxEqRel(liqSqrtPrice2, expectedLiqSqrtPrice2, 0, 'liq price2 is solved with quadratic formula');
    }

    function testRemoveHighestLeafAddSatButNotNewHighest() public {
        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0, 0, 0, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 2e18,
            reservesXAssets: 4e18,
            reservesYAssets: 1e18
        });

        uint128[6] memory allAssets;
        for (uint256 i = 0; i < 6; i++) {
            allAssets[i] += uint128(inputParams.userAssets[i]);
        }
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        uint256 targetSaturationInLAssets;
        uint256 targetNetXLiqSqrtPriceXInQ72;
        LXY memory accountLXYShares;

        // user1 creates 1st position
        targetSaturationInLAssets = 0.01e18;
        targetNetXLiqSqrtPriceXInQ72 = 0x1e66666666666666666; // 1.9 << 72;
        accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72);
        inputParams.userAssets[DEPOSIT_Y] = accountLXYShares.YD;
        inputParams.userAssets[BORROW_X] = accountLXYShares.XB;
        allAssets[DEPOSIT_Y] += uint128(inputParams.userAssets[DEPOSIT_Y]);
        allAssets[BORROW_X] += uint128(inputParams.userAssets[BORROW_X]);
        Saturation.update(satStruct, inputParams, vm.addr(1), Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.highestSetLeaf, 0, 'highest leaf set should be set');
        uint256 user1Leaf = satStruct.netXTree.highestSetLeaf;

        // user2 creates position with higher leaf
        targetSaturationInLAssets = 0.02e18;
        targetNetXLiqSqrtPriceXInQ72 = 0x1800000000000000000; // 1.5 << 72;
        accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72);
        inputParams.userAssets[DEPOSIT_Y] = accountLXYShares.YD;
        inputParams.userAssets[BORROW_X] = accountLXYShares.XB;
        allAssets[DEPOSIT_Y] += uint128(inputParams.userAssets[DEPOSIT_Y]);
        allAssets[BORROW_X] += uint128(inputParams.userAssets[BORROW_X]);
        Saturation.update(satStruct, inputParams, vm.addr(2), Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.highestSetLeaf, user1Leaf);

        // user2 reduces position to be in lower leaf vs user1 => highestSetLeaf is user1 leaf
        targetSaturationInLAssets = 0.005e18;
        targetNetXLiqSqrtPriceXInQ72 = 0x1800000000000000000; // 1.5 << 72;
        accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72);
        allAssets[DEPOSIT_Y] -= uint128(inputParams.userAssets[DEPOSIT_Y]);
        allAssets[BORROW_X] -= uint128(inputParams.userAssets[BORROW_X]);
        inputParams.userAssets[DEPOSIT_Y] = accountLXYShares.YD;
        inputParams.userAssets[BORROW_X] = accountLXYShares.XB;
        allAssets[DEPOSIT_Y] += uint128(inputParams.userAssets[DEPOSIT_Y]);
        allAssets[BORROW_X] += uint128(inputParams.userAssets[BORROW_X]);
        Saturation.update(satStruct, inputParams, vm.addr(2), Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertEq(satStruct.netXTree.highestSetLeaf, user1Leaf);
    }

    function testUpdateZeroActiveLiquidity() public {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0, 0, 0, 0],
            sqrtPriceMinInQ72: 0,
            sqrtPriceMaxInQ72: 0,
            activeLiquidityScalerInQ72: 0,
            activeLiquidityAssets: 0,
            reservesXAssets: 0,
            reservesYAssets: 0
        });

        address account = vm.addr(1);
        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertEq(satStruct.netXTree.nodes[0][0], 0);
        assertEq(satStruct.netYTree.nodes[0][0], 0);
    }

    function testUpdateZeroAccount() public {
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0, 0, 0, 0],
            sqrtPriceMinInQ72: 0,
            sqrtPriceMaxInQ72: 0,
            activeLiquidityScalerInQ72: 0,
            activeLiquidityAssets: 1,
            reservesXAssets: 1,
            reservesYAssets: 1
        });

        address account = address(0);
        vm.expectRevert(Saturation.CannotUpdateZeroAddress.selector);
        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
    }

    function testUpdateSuchThatFirstTrancheIsAlreadyFull() public {
        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0.00001e18, 0, 0.01e18, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });
        Saturation.update(satStruct, inputParams, vm.addr(1), Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        Saturation.update(satStruct, inputParams, vm.addr(2), Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateNetX() public {
        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0.00001e18, 0, 0.000001e18, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        address account = vm.addr(1);
        Saturation.update(satStruct, inputParams, account, Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateNetY() public {
        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0.001e18, 0, 0, 0, 0.00001e18],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        address account = vm.addr(1);
        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netYTree.nodes[0][0], 0);
    }

    function testAccruePenaltiesZeroDuration() public {
        uint256 activeLiquidityInLAssets = 0;
        uint256 externalLiquidity = 0;
        uint256 duration = 0;
        uint256 allAssetsDepositL = activeLiquidityInLAssets;
        uint256 allAssetsBorrowL = 0;
        uint256 allSharesBorrowL = 0;
        (uint256 penaltyInLAssets,) = Saturation.accruePenalties(
            satStruct, ZERO_ADDRESS, externalLiquidity, duration, allAssetsDepositL, allAssetsBorrowL, allSharesBorrowL
        );
        assertEq(penaltyInLAssets, 0);
    }

    function testAccruePenalties() public {
        uint256 currentSqrtPriceInXInQ72 = 2 << 72;
        uint256 startActiveLiquidityAssets = 4e18;
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        uint256 borrowX = sumOfSeriesOfBorrowX(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: currentSqrtPriceInXInQ72,
            sqrtPriceMaxInQ72: currentSqrtPriceInXInQ72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: startActiveLiquidityAssets,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        uint128[6] memory allAssets;
        uint256 externalLiquidity = 0;
        for (uint256 i = 0; i < 6; i++) {
            allAssets[i] += uint128(inputParams.userAssets[i]);
        }
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        address account = vm.addr(1);

        Saturation.update(satStruct, inputParams, account, desiredSaturationRatioInMAG2);

        // Reduce liquidity to push into penalty
        allAssets[DEPOSIT_L] -= uint128(inputParams.activeLiquidityAssets);
        inputParams.activeLiquidityAssets = startActiveLiquidityAssets / 10; // Reduce by 90%
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        // Add meaningful borrow L assets and shares for penalty calculation
        uint256 allAssetsDepositL = allAssets[DEPOSIT_L];
        uint256 allAssetsBorrowL = allAssetsDepositL / 10; // 10% utilization
        uint256 allSharesBorrowL = allAssetsBorrowL; // 1:1 asset:share ratio

        uint256 duration = 1 days;
        (uint256 penaltyInLAssets,) = Saturation.accruePenalties(
            satStruct, ZERO_ADDRESS, externalLiquidity, duration, allAssetsDepositL, allAssetsBorrowL, allSharesBorrowL
        );
        assertGt(penaltyInLAssets, 0, 'penalty should be > 0');
    }

    function testPenaltyForRepayAccountZero() public {
        uint256 penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netXTree, address(0));
        assertEq(penaltyInLAssets, 0);
        penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netYTree, address(0));
        assertEq(penaltyInLAssets, 0);
    }

    function testPenaltyForRepayPriceZero() public {
        uint256 penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netXTree, address(1));
        assertEq(penaltyInLAssets, 0);
        penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netYTree, address(1));
        assertEq(penaltyInLAssets, 0);
    }

    function testPenaltyForRepay() public {
        uint256 currentSqrtPriceInXInQ72 = 2 << 72;
        address account = vm.addr(1);
        uint256 activeLiquidityScalerInQ72 = Q72;
        uint256 startActiveLiquidityAssets = 4e18;
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        uint256 borrowX = sumOfSeriesOfBorrowX(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: currentSqrtPriceInXInQ72,
            sqrtPriceMaxInQ72: currentSqrtPriceInXInQ72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: startActiveLiquidityAssets,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        uint128[6] memory allAssets;
        for (uint256 i = 0; i < 6; i++) {
            allAssets[i] += uint128(inputParams.userAssets[i]);
        }
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        Saturation.update(satStruct, inputParams, account, desiredSaturationRatioInMAG2);

        // Reduce liquidity to push into penalty
        allAssets[DEPOSIT_L] -= uint128(inputParams.activeLiquidityAssets);
        inputParams.activeLiquidityAssets = startActiveLiquidityAssets / 10; // Reduce by 90%
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        uint256 allAssetsDepositL = allAssets[DEPOSIT_L];
        uint256 allAssetsBorrowL = allAssetsDepositL / 10; // 10% utilization
        uint256 allSharesBorrowL = allAssetsBorrowL; // 1:1 asset:share ratio

        uint256 duration = 1 days;

        Saturation.accruePenalties(
            satStruct, ZERO_ADDRESS, 0, duration, allAssetsDepositL, allAssetsBorrowL, allSharesBorrowL
        );
        uint256 penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netXTree, account);
        assertGt(penaltyInLAssets, 0);
    }

    function testPenaltyForRepayMultipleAccountsSameTranche() public {
        uint256 currentSqrtPriceInXInQ72 = 2 << 72;
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 startActiveLiquidityAssets = 4e18;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        uint256 borrowX = sumOfSeriesOfBorrowX(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: currentSqrtPriceInXInQ72,
            sqrtPriceMaxInQ72: currentSqrtPriceInXInQ72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: startActiveLiquidityAssets,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        uint128[6] memory allAssets;
        for (uint256 i = 0; i < 6; i++) {
            allAssets[i] += uint128(inputParams.userAssets[i]);
        }
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        Saturation.update(satStruct, inputParams, vm.addr(1), desiredSaturationRatioInMAG2);

        // Reduce liquidity to push into penalty
        allAssets[DEPOSIT_L] -= uint128(inputParams.activeLiquidityAssets);
        inputParams.activeLiquidityAssets = startActiveLiquidityAssets / 10; // Reduce by 90%
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        // Add meaningful borrow L assets and shares for penalty calculation
        uint256 allAssetsDepositL = allAssets[DEPOSIT_L];
        uint256 allAssetsBorrowL = allAssetsDepositL / 10; // 10% utilization
        uint256 allSharesBorrowL = allAssetsBorrowL; // 1:1 asset:share ratio

        uint256 duration = 1 days;

        Saturation.accruePenalties(
            satStruct, ZERO_ADDRESS, 0, duration, allAssetsDepositL, allAssetsBorrowL, allSharesBorrowL
        );

        // check penalties

        // addr(1) should have some penalty
        uint256 penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netXTree, vm.addr(1));
        assertGt(penaltyInLAssets, 0, 'addr(1) should have some penalty');

        // addr(2) should have no penalty on struct1
        penaltyInLAssets = Saturation.accrueAccountPenalty(satStruct.netXTree, vm.addr(2));
        assertEq(penaltyInLAssets, 0, 'addr(2) should no penalty');
    }

    function testPenaltyForRepaySingleVsMultipleAccounts() public {
        uint256 currentSqrtPriceInXInQ72 = 2 << 72;
        uint256 activeLiquidityScalerInQ72 = Q72;
        uint256 startActiveLiquidityAssets = 4e18;
        uint256 desiredSaturationRatioInMAG2 = Saturation.START_SATURATION_PENALTY_RATIO_IN_MAG2 + 1;
        uint256 targetLiqSqrtPriceXInQ72 = 195 * Q72 / 100; // 1.95

        uint256 borrowX = sumOfSeriesOfBorrowX(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );
        uint256 depositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, 300, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        );

        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, depositY, 0, borrowX, 0],
            sqrtPriceMinInQ72: currentSqrtPriceInXInQ72,
            sqrtPriceMaxInQ72: currentSqrtPriceInXInQ72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: startActiveLiquidityAssets,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        uint128[6] memory allAssets;
        for (uint256 i = 0; i < 6; i++) {
            allAssets[i] += uint128(inputParams.userAssets[i]);
        }
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        Saturation.update(satStruct, inputParams, vm.addr(1), desiredSaturationRatioInMAG2);

        // Reduce liquidity to push into penalty
        allAssets[DEPOSIT_L] -= uint128(inputParams.activeLiquidityAssets);
        inputParams.activeLiquidityAssets = startActiveLiquidityAssets / 10; // Reduce by 90% like burnFor in integration tests
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        // Add meaningful borrow L assets and shares for penalty calculation
        uint256 allAssetsDepositL = allAssets[DEPOSIT_L];
        uint256 allAssetsBorrowL = allAssetsDepositL / 10; // 10% utilization
        uint256 allSharesBorrowL = allAssetsBorrowL; // 1:1 asset:share ratio

        uint256 duration = 1 days;

        Saturation.accruePenalties(
            satStruct, ZERO_ADDRESS, 0, duration, allAssetsDepositL, allAssetsBorrowL, allSharesBorrowL
        );

        // addr(1) and addr(2) take positions on satStruct2 and penalty of addr(1) should be the same as in satStruct1
        Saturation.initializeSaturationStruct(satStruct2);
        allAssets[DEPOSIT_L] -= uint128(inputParams.activeLiquidityAssets);
        inputParams.activeLiquidityAssets = startActiveLiquidityAssets; // Reset to original value
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        // addr(1) takes single tranche position again
        Saturation.update(satStruct2, inputParams, vm.addr(1), desiredSaturationRatioInMAG2);

        // addr(2) takes multi tranche position
        uint256 smallerBorrowX =
            sumOfSeriesOfBorrowX(targetLiqSqrtPriceXInQ72, 30, startActiveLiquidityAssets, desiredSaturationRatioInMAG2); // 10x smaller series
        uint256 smallerDepositY = sumOfSeriesOfCollateralY(
            targetLiqSqrtPriceXInQ72, 30, startActiveLiquidityAssets, desiredSaturationRatioInMAG2
        ); // 10x smaller series

        (inputParams.userAssets[BORROW_X], inputParams.userAssets[DEPOSIT_Y]) = (smallerBorrowX, smallerDepositY);
        allAssets[DEPOSIT_Y] += uint128(inputParams.userAssets[DEPOSIT_Y]);
        allAssets[BORROW_X] += uint128(inputParams.userAssets[BORROW_X]);

        Saturation.update(satStruct2, inputParams, vm.addr(2), desiredSaturationRatioInMAG2);

        // liquidity decreases, penalties accrue
        allAssets[DEPOSIT_L] -= uint128(inputParams.activeLiquidityAssets);
        inputParams.activeLiquidityAssets = startActiveLiquidityAssets / 10; // Reduce by 90% like burnFor in integration tests
        allAssets[DEPOSIT_L] += uint128(inputParams.activeLiquidityAssets);

        allAssetsDepositL = allAssets[DEPOSIT_L];
        allAssetsBorrowL = allAssetsDepositL / 10; // 10% utilization
        allSharesBorrowL = allAssetsBorrowL; // 1:1 asset:share ratio

        Saturation.accruePenalties(
            satStruct2, ZERO_ADDRESS, 0, duration, allAssetsDepositL, allAssetsBorrowL, allSharesBorrowL
        );

        // check penalties

        // addr(1) should have some penalty
        uint256 penaltyInLAssets1 = Saturation.accrueAccountPenalty(satStruct.netXTree, vm.addr(1));
        assertGt(penaltyInLAssets1, 0, 'addr(1) should have some penalty');

        // addr(1) should have LOWER penalty
        // According to the penalty formula: penalty rate = (u_1 * f_interestPerSecond(u_1) * allAssetsDepositL) / WAD * satInLAssetsInPenalty
        uint256 penaltyInLAssets2 = Saturation.accrueAccountPenalty(satStruct2.netXTree, vm.addr(1));
        assertLt(
            penaltyInLAssets2,
            penaltyInLAssets1,
            'addr(1) should have lower penalty when risk is distributed across more tranches'
        );

        // addr(2) should have no penalty on struct1
        penaltyInLAssets1 = Saturation.accrueAccountPenalty(satStruct.netXTree, vm.addr(2));
        assertEq(penaltyInLAssets1, 0, 'addr(2) should no penalty on struct 1');

        // addr(2) should have the some penalty on struct2
        penaltyInLAssets2 = Saturation.accrueAccountPenalty(satStruct2.netXTree, vm.addr(2));
        assertGt(penaltyInLAssets2, 0, 'addr(2) should some penalty on struct 2');
    }

    function testStayInSameLeafWhenRemovingAndAddingSaturation() public {
        uint256 activeLiquidity = 4e18;
        uint256 currentSqrtPriceXInQ64 = 2 << 72;

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 1 << 72;
        LXY memory accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, accountLXYShares.YD, 0, accountLXYShares.XB, 0],
            sqrtPriceMinInQ72: currentSqrtPriceXInQ64,
            sqrtPriceMaxInQ72: currentSqrtPriceXInQ64,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: activeLiquidity,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });
        Saturation.update(satStruct, inputParams, vm.addr(1), Saturation.MAX_SATURATION_RATIO_IN_MAG2);

        accountLXYShares = createLXYAssetsYDXB(targetSaturationInLAssets / 1000, targetLiqSqrtPriceXInQ72);
        (inputParams.userAssets[BORROW_X], inputParams.userAssets[DEPOSIT_Y]) =
            (accountLXYShares.XB, accountLXYShares.YD);
        Saturation.update(satStruct, inputParams, vm.addr(2), Saturation.MAX_SATURATION_RATIO_IN_MAG2);

        inputParams.userAssets[BORROW_X]++;
        Saturation.update(satStruct, inputParams, vm.addr(2), Saturation.MAX_SATURATION_RATIO_IN_MAG2);
    }

    function testUpdateBorrowYDLBXB() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 1 << 72;
        int256 ratioYHatOverXHatInQ72 = -2 << 72;
        LXY memory accountLXYShares =
            createLXYAssetsYDLBXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, accountLXYShares.YD, accountLXYShares.LB, accountLXYShares.XB, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateBorrowXDYDLB() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e14;
        uint256 targetNetXLiqSqrtPriceXInQ72 = 194 * Q72 / 100; //1.95 << 72;
        int256 ratioYHatOverXHatInQ72 = 60 << 72;
        LXY memory accountLXYShares =
            createLXYAssetsXDYDLBNetX(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, accountLXYShares.XD, accountLXYShares.YD, accountLXYShares.LB, 0, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateBorrowLDXDYB() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 3 << 72;
        int256 ratioYHatOverXHatInQ72 = -1 << 72;
        LXY memory accountLXYShares =
            createLXYAssetsLDXDYB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [accountLXYShares.LD, accountLXYShares.XD, 0, 0, 0, accountLXYShares.YB],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netYTree.nodes[0][0], 0);
    }

    function testUpdateBorrowLDXBYB() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetNetXLiqSqrtPriceXInQ72 = 19 * Q72 / 10; //1.9 << 72;
        int256 ratioYHatOverXHatInQ72 = int256(2 * Q72 / 10); //0.2 << 72;
        LXY memory accountLXYShares =
            createLXYAssetsLDXBYBNetX(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [accountLXYShares.LD, 0, 0, 0, accountLXYShares.XB, accountLXYShares.YB],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateBorrowLBXD() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 3 << 72;
        LXY memory accountLXYShares = createLXYAssetsXDLB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, accountLXYShares.XD, 0, accountLXYShares.LB, 0, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netYTree.nodes[0][0], 0);
    }

    function testUpdateBorrowLDXB() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 1 << 72;
        LXY memory accountLXYShares = createLXYAssetsLDXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [accountLXYShares.LD, 0, 0, 0, accountLXYShares.XB, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateBorrowLBYD() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 1 << 72;
        LXY memory accountLXYShares = createLXYAssetsYDLB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, accountLXYShares.YD, accountLXYShares.LB, 0, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netXTree.nodes[0][0], 0);
    }

    function testUpdateBorrowLDYB() public {
        address account = vm.addr(1);

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 3 << 72;
        LXY memory accountLXYShares = createLXYAssetsLDYB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [accountLXYShares.LD, 0, 0, 0, 0, accountLXYShares.YB],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(satStruct.netYTree.nodes[0][0], 0);
    }

    function testSetOrUnsetFieldBitUpTheTree(
        uint256 leafIndex
    ) public {
        leafIndex = uint256(bound(leafIndex, 0, Saturation.LEAFS - 1));
        uint256 numChildrenAtLowestLevel = Saturation.CHILDREN_PER_NODE;
        uint256 nodeIndex = leafIndex / numChildrenAtLowestLevel;
        uint256 lowerNodePos = uint256(leafIndex % numChildrenAtLowestLevel);
        Saturation.setXorUnsetFieldBitUpTheTree(
            satStruct.netXTree, Saturation.LOWEST_LEVEL_INDEX, nodeIndex, lowerNodePos, 1
        );

        // follow set bits from root to leaf
        nodeIndex = 0;
        for (uint256 level = 0; level < Saturation.LEVELS_WITHOUT_LEAFS; level++) {
            uint256 field = Saturation.readFieldFromNode(satStruct.netXTree.nodes[level][nodeIndex]);
            uint256 nodePos = uint256(BitLib.ctz64(field));
            if (Saturation.CHILDREN_PER_NODE < nodePos + 1) assert(false);
            nodePos = uint256(Saturation.CHILDREN_PER_NODE - nodePos - 1);
            nodeIndex = nodeIndex * Saturation.CHILDREN_PER_NODE + nodePos;
        }
        assertEq(nodeIndex, leafIndex);

        nodeIndex = leafIndex / numChildrenAtLowestLevel;
        Saturation.setXorUnsetFieldBitUpTheTree(
            satStruct.netXTree, Saturation.LOWEST_LEVEL_INDEX, nodeIndex, lowerNodePos, 0
        );

        uint256 totalNodesAtNextLevel = 1;
        for (uint256 level = 0; level < Saturation.LEVELS_WITHOUT_LEAFS; level++) {
            for (nodeIndex = 0; nodeIndex < totalNodesAtNextLevel; nodeIndex++) {
                uint256 field = Saturation.readFieldFromNode(satStruct.netXTree.nodes[level][nodeIndex]);
                assertEq(field, 0);
            }
            totalNodesAtNextLevel *= Saturation.CHILDREN_PER_NODE;
        }
    }

    function testgetSatPercentageInWads() public {
        // no saturation
        assertEq(satStruct.netXTree.highestSetLeaf, 0, 'highest set leaf should be 0');
        assertEq(satStruct.netYTree.highestSetLeaf, 0, 'highest set leaf should be 0');
        assertEq(satStruct.maxLeaf, 0, 'max leaf should be 0');
        assertEq(Saturation.getSatPercentageInWads(satStruct), 0, 'should return 0 when no saturation');

        // delta = 1
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 2;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_DEFAULT_WAD,
            'should return 95% when delta is 1'
        );

        // delta = 2
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 3;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_DEFAULT_WAD,
            'should return 95% when delta is 2'
        );

        // delta = 3
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 4;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_DEFAULT_WAD,
            'should return 95% when delta is 3'
        );

        // delta = 4
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 5;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_4_WAD,
            'should return ~94.18% when delta is 4'
        );

        // delta = 5
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 6;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_5_WAD,
            'should return ~92.32% when delta is 5'
        );

        // delta = 6
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 7;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_6_WAD,
            'should return ~90.49% when delta is 6'
        );

        // delta = 7
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 8;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_7_WAD,
            'should return ~88.70% when delta is 7'
        );

        // delta = 8
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 9;
        assertEq(
            Saturation.getSatPercentageInWads(satStruct),
            SAT_PERCENTAGE_DELTA_8_WAD,
            'should return ~86.94% when delta is 8'
        );

        // delta = 9
        satStruct.netXTree.highestSetLeaf = 1;
        satStruct.maxLeaf = 10;
        assertEq(Saturation.getSatPercentageInWads(satStruct), 0, 'should return 0 when delta is >8');
    }

    // @dev this test is assuming 85% threshold for penalty and 336 shift for sat to leaf
    function testSatToLeaf() public pure {
        uint256 sat;
        uint256 leaf;

        sat = 0;
        leaf = Saturation.satToLeaf(sat);
        assertEq(leaf, 0, 'sat = 0 should be leaf = 0');

        uint256 satBelowMinimumPenalty = 849;
        leaf = Saturation.satToLeaf(satBelowMinimumPenalty);
        assertEq(leaf, 0, 'sat = 849 should be leaf = 0');

        uint256 minSatForLeaf1 = 850;
        leaf = Saturation.satToLeaf(minSatForLeaf1);
        assertEq(leaf, 1, 'sat = 850 should be leaf = 1');

        uint256 maxSatForLeaf1 = 864;
        leaf = Saturation.satToLeaf(maxSatForLeaf1);
        assertEq(leaf, 1, 'sat = 864 should be leaf = 1');

        uint256 minSatForLeaf2 = 865;
        leaf = Saturation.satToLeaf(minSatForLeaf2);
        assertEq(leaf, 2, 'sat = 865 should be leaf = 2');

        uint256 maxSatForLeaf2 = 881;
        leaf = Saturation.satToLeaf(maxSatForLeaf2);
        assertEq(leaf, 2, 'sat = 881 should be leaf = 2');

        // spot check a random saturation value and ensure it is in the correct leaf
        sat = 2 * uint256(type(uint112).max - 1) / 33;
        leaf = Saturation.satToLeaf(sat);
        assertEq(leaf, 3407, 'should be 3407 for 2/33 of max uint112');

        sat = Saturation.LOWEST_POSSIBLE_IN_PENALTY - 1;
        leaf = Saturation.satToLeaf(sat);
        assertEq(leaf, Saturation.LEAFS - 2, 'should be 4094 for max amount possible not in penalty');

        sat = Saturation.LOWEST_POSSIBLE_IN_PENALTY;
        leaf = Saturation.satToLeaf(sat);
        assertEq(leaf, Saturation.LEAFS - 1, 'should be 4095 for min amount always in penalty');

        sat = uint256(type(uint128).max);
        leaf = Saturation.satToLeaf(sat);
        assertEq(leaf, Saturation.LEAFS - 1, 'should be 4095 for max assets');
    }

    function testFindHighestSetLeafDownwards(
        uint256 leafIndex
    ) public {
        leafIndex = uint256(bound(leafIndex, 0, Saturation.LEAFS - 1));

        // Use a meaningful saturation value that will definitely set a leaf in the tree
        uint256 targetSat = 1000 + (leafIndex * 1000);

        int256 tranche = 0;
        uint256 activeLiquidityInLAssets = 4e18;

        // Update the tree with meaningful saturation to actually set a leaf
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            Saturation.SaturationPair({satInLAssets: uint128(targetSat), satRelativeToL: uint128(targetSat)}),
            vm.addr(1),
            tranche,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        // Verify that the tree has been updated and has a highest set leaf
        uint256 actualHighestLeaf = satStruct.netXTree.highestSetLeaf;
        assertGt(actualHighestLeaf, 0, 'Tree should have at least one set leaf');

        // Test findHighestSetLeafDownwards from the root
        uint256 leafIndexOut = Saturation.findHighestSetLeafDownwards(satStruct.netXTree, 0, 0);

        // The function should find the actual highest set leaf
        assertEq(leafIndexOut, actualHighestLeaf, 'Should find the actual highest set leaf');

        // Also verify the leaf was actually calculated correctly by satToLeaf
        uint256 expectedLeaf = Saturation.satToLeaf(targetSat);
        assertEq(actualHighestLeaf, expectedLeaf, 'Highest set leaf should match satToLeaf calculation');
    }

    function testBestLeafTwoAccountsSameLeafThenImproveBestThenRemoveBest() public {
        int256 tranche;
        uint256 activeLiquidityInLAssets = 4e18;
        uint256 sat = 1e10;
        uint256 highestSetLeaf;

        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(sat), satRelativeToL: uint128(sat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            vm.addr(1),
            tranche,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        highestSetLeaf = satStruct.netXTree.highestSetLeaf;
        assertGt(highestSetLeaf, 0);

        sat = 1e15;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(sat), satRelativeToL: uint128(sat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            vm.addr(2),
            tranche,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        assertGt(satStruct.netXTree.highestSetLeaf, highestSetLeaf);

        sat = 1e1;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(sat), satRelativeToL: uint128(sat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            vm.addr(2),
            tranche + 10,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        assertEq(satStruct.netXTree.highestSetLeaf, highestSetLeaf);
    }

    function testTotalSatAfterUpdateTreeOnceNetX() public {
        address account = vm.addr(1);
        int256 tranchePercentage = 1000;
        uint256 sat = 1e10;
        uint256 activeLiquidityInLAssets = 4e18;

        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(sat), satRelativeToL: uint128(sat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            tranchePercentage,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );
        uint256 thresholdLeaf;
        uint128 satCalc = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        // This tests the updateTreeGivenAccountTrancheAndSat function which
        // adds saturation directly to the tree WITHOUT applying SATURATION_TIME_BUFFER_IN_MAG2
        assertEq(satCalc, sat);
    }

    function testSaturationTimeBufferIsAppliedInUpdate() public {
        address account = vm.addr(1);
        uint256 activeLiquidityInLAssets = 4e18;

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [uint256(0), 0, 0.00001e18, 0, 0.000001e18, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: activeLiquidityInLAssets,
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        // Store initial state
        uint256 thresholdLeaf;
        uint256 initialSat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(initialSat, 0, 'should start with no saturation');

        // Use the update function which should apply the buffer
        Saturation.update(satStruct, inputParams, account, Saturation.MAX_SATURATION_RATIO_IN_MAG2);

        uint256 finalSat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);

        // The saturation should be greater than 0 and the buffer should have been applied in calcSat
        assertGt(finalSat, 0, 'should have some saturation after update');
    }

    function testTotalSatAfterUpdateTreeTwiceSameAccountSameTrancheNetX() public {
        address account = vm.addr(1);
        int16 newTranche = 10; // does not matter for this test
        uint256 newSat;
        uint256 activeLiquidityInLAssets = 4e18;
        uint256 thresholdLeaf = 0; // get total sat
        uint256 sat;

        newSat = 1e10;
        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        // This tests the updateTreeGivenAccountTrancheAndSat function which
        // adds saturation directly to the tree WITHOUT applying SATURATION_TIME_BUFFER_IN_MAG2
        assertEq(sat, newSat);

        newSat = 1e15;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        // This tests the updateTreeGivenAccountTrancheAndSat function which
        // adds saturation directly to the tree WITHOUT applying SATURATION_TIME_BUFFER_IN_MAG2
        assertEq(sat, newSat);
    }

    function testTotalSatAfterUpdateTreeTwiceSameAccountDiffTrancheSameLeafNetX() public {
        address account = vm.addr(1);
        int16 newTranche;
        uint256 newSat;
        uint256 activeLiquidityInLAssets = 4e18;
        uint256 thresholdLeaf = 0; // get total sat
        uint256 sat;

        newTranche = 10;
        newSat = 1e10;
        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat);

        newTranche = 100;
        newSat = 1e10 + 1;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat);
    }

    function testTotalSatAfterUpdateTreeTwiceSameAccountDiffTrancheDiffLeafNetX() public {
        address account = vm.addr(1);
        int16 newTranche;
        uint256 newSat;
        uint256 activeLiquidityInLAssets = 4e18;
        uint256 thresholdLeaf = 0; // get total sat
        uint256 sat;

        newTranche = 10;
        newSat = 1e10;
        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat);

        newTranche = 100;
        newSat = 1e15;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat);
    }

    function testTotalSatAfterUpdateTreeTwiceDiffAccountDiffTrancheSameLeafNetX() public {
        address account;
        int16 newTranche;

        uint256 activeLiquidityInLAssets = 4e18;
        uint256 thresholdLeaf = 0; // get total sat
        uint256 sat;

        account = vm.addr(1);
        newTranche = 10;
        uint256 newSat = 1e10;
        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat);

        account = vm.addr(2);
        newTranche = 100;
        uint256 newSat2 = 1e10 + 1;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(newSat2), satRelativeToL: uint128(newSat2)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranche * 100,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat + newSat2);
    }

    function testTotalSatAfterUpdateTreeTwiceDiffAccountSameTrancheIntoMultipleTranchesNetX() public {
        address account;
        int16 newTranchePercentage = 1000;

        uint256 activeLiquidityInLAssets = 4e18;
        uint256 thresholdLeaf = 0; // get total sat
        uint256 sat;

        account = vm.addr(1);
        uint256 newSat = uint256(3_519_061_583_577_708);
        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranchePercentage,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, newSat);

        account = vm.addr(2);
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netXTree,
            saturationPair,
            account,
            newTranchePercentage,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netXTree, thresholdLeaf);
        assertEq(sat, 2 * newSat);
    }

    function testTotalSatAfterUpdateTreeTwiceDiffAccountSameTrancheIntoMultipleTranchesNetY() public {
        address account;
        int16 newTranchePercentage;

        uint256 activeLiquidityInLAssets = 4e18;
        uint256 thresholdLeaf = 0; // get total sat
        uint256 sat;

        account = vm.addr(1);
        newTranchePercentage = 1000;
        uint256 newSat = uint256(3_519_061_583_577_708);
        Saturation.SaturationPair memory saturationPair =
            Saturation.SaturationPair({satInLAssets: uint128(newSat), satRelativeToL: uint128(newSat)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netYTree,
            saturationPair,
            account,
            newTranchePercentage,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netYTree, thresholdLeaf);
        assertEq(sat, newSat);

        account = vm.addr(2);
        newTranchePercentage = 1000;
        uint256 newSat2 = newSat;
        saturationPair = Saturation.SaturationPair({satInLAssets: uint128(newSat2), satRelativeToL: uint128(newSat2)});
        Saturation.updateTreeGivenAccountTrancheAndSat(
            satStruct.netYTree,
            saturationPair,
            account,
            newTranchePercentage,
            activeLiquidityInLAssets,
            Saturation.MAX_SATURATION_RATIO_IN_MAG2
        );

        sat = Saturation.calcTotalSatAfterLeafInclusive(satStruct.netYTree, thresholdLeaf);
        assertEq(sat, newSat + newSat2);
    }

    function testReadWriteFieldBit(uint256 node, uint256 bitPos) public pure {
        bitPos = bound(bitPos, 0, type(uint8).max);
        uint256 bit = Saturation.readFieldBitFromNode(node, bitPos);
        node = Saturation.writeFlippedFieldBitToNode(node, bitPos);
        uint256 flippedBit = Saturation.readFieldBitFromNode(node, bitPos);
        assertNotEq(bit, flippedBit);
    }

    function writeFieldToNode(uint256 field, uint256 nodeIn) private pure returns (uint256 nodeOut) {
        uint256 MASK = type(uint256).max >> (128 + 112);
        nodeOut = (nodeIn & ~MASK) | field;
    }

    function testReadWriteField(uint256 fieldIn, uint256 node) public pure {
        fieldIn = bound(fieldIn, 0, type(uint16).max);
        node = writeFieldToNode(fieldIn, node);
        uint256 fieldOut = Saturation.readFieldFromNode(node);
        assertEq(fieldIn, fieldOut);
    }

    function testUpdateBorrowMaxTrancheOverSaturated() public {
        address bob = vm.addr(2); // holds borrow position

        uint256 targetSaturationInLAssets = 1e13;
        uint256 targetLiqSqrtPriceXInQ72 = 1 << 72;
        int256 ratioYHatOverXHatInQ72 = -2 << 72;

        // Bob creates a debt position
        LXY memory bobLXYShares =
            createLXYAssetsYDLBXB(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72);

        uint256 activeLiquidityScalerInQ72 = Q72;
        Validation.InputParams memory inputParams = Validation.InputParams({
            userAssets: [0, 0, bobLXYShares.YD, bobLXYShares.LB, bobLXYShares.XB, 0],
            sqrtPriceMinInQ72: 2 << 72,
            sqrtPriceMaxInQ72: 2 << 72,
            activeLiquidityScalerInQ72: activeLiquidityScalerInQ72,
            activeLiquidityAssets: 4e18, // Alice's implied supply position
            reservesXAssets: 8e18,
            reservesYAssets: 2e18
        });

        Saturation.update(satStruct, inputParams, bob, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
        assertGt(
            satStruct.netXTree.nodes[0][0],
            0,
            'Confirms the saturation tree has been successfully updated as a result of the borrow'
        );

        // Alice burns enough liquidity to emulate a high saturation state
        // set the tree's highestSetLeaf close to maxLeaf
        uint256 maxLeaf = Saturation.satToLeaf(inputParams.activeLiquidityAssets);
        uint256 forcedHighLeaf = maxLeaf - (Saturation.SATURATION_MAX_BUFFER_TRANCHES - 1); // set the delta to be 2 to make highestSetLeaf close to maxLeaf
        satStruct.netXTree.highestSetLeaf = uint16(forcedHighLeaf);

        vm.expectRevert(Saturation.MaxTrancheOverSaturated.selector);
        Saturation.update(satStruct, inputParams, bob, Saturation.MAX_SATURATION_RATIO_IN_MAG2);
    }

    // liq sqrt price
    function calcFractionOfMaxTrancheSaturation(
        uint256 satFractionInQ72
    ) private pure returns (uint256 saturationInLAssets) {
        uint256 initialActiveLiquidity = 4e18;
        return calcFractionOfMaxTrancheSaturation(initialActiveLiquidity, satFractionInQ72);
    }

    function calcFractionOfMaxTrancheSaturation(
        uint256 activeLiquidity,
        uint256 satFractionInQ72
    ) private pure returns (uint256 saturationInLAssets) {
        uint256 maxSaturationPerTranche = activeLiquidity;
        maxSaturationPerTranche = uint256(
            Math.mulDiv(
                maxSaturationPerTranche,
                Saturation.MAX_SATURATION_RATIO_IN_MAG2,
                Saturation.SATURATION_TIME_BUFFER_IN_MAG2
            )
        );
        saturationInLAssets = uint256(Math.mulDiv(maxSaturationPerTranche, satFractionInQ72, Q72));
    }

    function boundTargetSaturation(
        uint256 targetSaturationInLAssetsInput,
        uint256 saturationMinInLAssets,
        uint256 saturationMaxInLAssets
    ) public pure returns (uint256 targetSaturationInLAssets) {
        targetSaturationInLAssets =
            uint256(bound(targetSaturationInLAssetsInput, saturationMinInLAssets, saturationMaxInLAssets));
    }

    function boundTargetSaturationAndLiqSqrtPrice(
        uint256 suggestedTargetSaturationInLAssets,
        uint256 saturationMinInLAssets,
        uint256 saturationMaxInLAssets,
        uint256 suggestedTargetLiqSqrtPriceXInQ72,
        uint256 liqSqrtPriceXMinInQ72,
        uint256 liqSqrtPriceXMaxInQ72
    ) public pure returns (uint256 boundedTargetSaturationInLAssets, uint256 boundedTargetLiqSqrtPriceXInQ72) {
        boundedTargetSaturationInLAssets =
            boundTargetSaturation(suggestedTargetSaturationInLAssets, saturationMinInLAssets, saturationMaxInLAssets);

        boundedTargetLiqSqrtPriceXInQ72 =
            bound(suggestedTargetLiqSqrtPriceXInQ72, liqSqrtPriceXMinInQ72, liqSqrtPriceXMaxInQ72);
    }

    function boundTargetSaturationAndLiqSqrtPrice(
        uint256 suggestedTargetSaturationInLAssets,
        uint256 suggestedTargetLiqSqrtPriceXInQ72
    ) public pure returns (uint256 boundedTargetSaturationInLAssets, uint256 boundedTargetLiqSqrtPriceXInQ72) {
        uint256 maxSaturationPerTrancheInLAssets = calcFractionOfMaxTrancheSaturation(Q72 / 100);
        // the following are practical limits, not theoretical limits
        // this is ok as this is testing functions to be used to generate test data
        // testing the actual functions at edges is separate
        (boundedTargetSaturationInLAssets, boundedTargetLiqSqrtPriceXInQ72) = boundTargetSaturationAndLiqSqrtPrice(
            suggestedTargetSaturationInLAssets,
            maxSaturationPerTrancheInLAssets,
            maxSaturationPerTrancheInLAssets * 100,
            suggestedTargetLiqSqrtPriceXInQ72,
            Q72, // 1
            Q72 * 4 // 4
        );
    }

    // Penalty rate per second tests

    // test calcSaturationPenaltyRatePerSecondInWads with all combinations of borrow utilizations and saturation levels
    function testCalcSaturationPenaltyRatePerSecondInWads() public pure {
        uint256[6] memory borrowUtilizations =
            [uint256(0), uint256(0.25e18), uint256(0.5e18), uint256(0.75e18), uint256(0.9e18), uint256(0.95e18)];

        uint256[6] memory saturationLevels = [
            SAT_PERCENTAGE_DELTA_DEFAULT_WAD, // 95.00%
            SAT_PERCENTAGE_DELTA_4_WAD, // ~94.18%
            SAT_PERCENTAGE_DELTA_5_WAD, // ~92.32%
            SAT_PERCENTAGE_DELTA_6_WAD, // ~90.49%
            SAT_PERCENTAGE_DELTA_7_WAD, // ~88.70%
            SAT_PERCENTAGE_DELTA_8_WAD // ~86.94%
        ];

        for (uint256 i = 0; i < borrowUtilizations.length; i++) {
            uint256 borrowUtilization = borrowUtilizations[i];
            for (uint256 j = 0; j < saturationLevels.length; j++) {
                uint256 satUtilizationInWads = saturationLevels[j];
                uint128 satInLAssetsInPenalty = 15; // 15%
                uint256 allAssetsDepositL = 100;
                uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
                    borrowUtilization, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
                );
                uint256 expectedPenaltyRatePerSecondInWads = getExpectedPenaltyRatePerSecondInWads(
                    borrowUtilization, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
                );

                assertEq(
                    penaltyRatePerSecondInWads,
                    expectedPenaltyRatePerSecondInWads,
                    'Penalty rate per second should match our manual calculation'
                );
            }
        }
    }

    // The following tests use this Desmos calculator to verify the expected penalty rate per year:
    // https://www.desmos.com/calculator/ewa7ha8fpw

    // spot check 25% borrow utilization with leaf delta=0 (95% saturation)
    function testCalcSaturationPenaltyRatePerSecondInWads_25PercentUtilization_95PercentSaturation() public pure {
        uint256 borrowUtilizationInWads = 0.25e18;
        uint256 satUtilizationInWads = MAX_SATURATION_PERCENT_IN_WAD; // 95% saturation
        uint128 satInLAssetsInPenalty = 15;
        uint256 allAssetsDepositL = 100;

        // calcSaturationPenaltyRatePerSecondInWads expects wads, no mag2
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );

        uint256 expectedPenaltyRatePerYearInWads = 700e16; // 700% per year.

        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            1e9, // note that 1e16 is 1%
            '25% borrow utilization with 95% saturation should result in 700% penalty rate per year'
        );
    }

    // spot check 25% borrow utilization with leaf delta=4 (94.18% saturation)
    function testCalcSaturationPenaltyRatePerSecondInWads_25PercentUtilization() public pure {
        uint256 borrowUtilizationInWads = 0.25e18;
        uint256 satUtilizationInWads = SAT_PERCENTAGE_DELTA_4_WAD; // ~94.18%
        uint128 satInLAssetsInPenalty = 15;
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );

        uint256 expectedPenaltyRatePerYearInWads = 667.61396808e16; // see desmos
        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            1e9, // note that 1e16 is 1%
            '25% borrow utilization with 94.18% saturation should result in 667% penalty rate per year'
        );
    }

    // spot check 50% borrow utilization with leaf delta=5 (92.32% saturation)
    function testCalcSaturationPenaltyRatePerSecondInWads_50PercentUtilization() public pure {
        uint256 borrowUtilizationInWads = 0.5e18;
        uint256 satUtilizationInWads = SAT_PERCENTAGE_DELTA_5_WAD; // ~92.32%
        uint128 satInLAssetsInPenalty = 15;
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );
        uint256 expectedPenaltyRatePerYearInWads = 419.573452661e16; // calculated in desmos
        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            1e9, // note that 1e16 is 1%
            '50% borrow utilization with 92.32% saturation should result in ~419% penalty rate per year'
        );
    }

    // spot check 75% borrow utilization with leaf delta=6 (90.49% saturation)
    function testCalcSaturationPenaltyRatePerSecondInWads_75PercentUtilization() public pure {
        uint256 borrowUtilizationInWads = 0.75e18;
        uint256 satUtilizationInWads = SAT_PERCENTAGE_DELTA_6_WAD; // ~90.49%
        uint128 satInLAssetsInPenalty = 15;
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );
        uint256 expectedPenaltyRatePerYearInWads = 213.546959372e16; // calculated in desmos
        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            1e9, // note that 1e16 is 1%
            '75% borrow utilization with 15% saturation should result in ~213% per year penalty rate per year'
        );
    }

    // spot check 90% borrow utilization with leaf delta=7 (88.70% saturation)
    function testCalcSaturationPenaltyRatePerSecondInWads_90PercentUtilization() public pure {
        uint256 borrowUtilizationInWads = 0.9e18;
        uint256 satUtilizationInWads = SAT_PERCENTAGE_DELTA_7_WAD; // ~88.70%
        uint128 satInLAssetsInPenalty = 5; // 15 not possible it would exceed 95% of ALA.
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );
        uint256 expectedPenaltyRatePerYearInWads = 266.732386629e16; // calculated manually
        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            2e9, // note that 1e16 is 1%
            '90% borrow utilization with 88.70% saturation should result in ~266% penalty rate per year'
        );
    }

    // // edge cases - 0% borrow utilization with 0% saturation
    function testCalcSaturationPenaltyRatePerSecondInWads_0PercentUtilization() public pure {
        uint256 borrowUtilizationInWads = 0;
        uint256 satUtilizationInWads = 0.85e16; // 85%
        uint128 satInLAssetsInPenalty = 15;
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );
        uint256 expectedPenaltyRatePerYearInWads = 0;
        assertEq(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            '0% borrow utilization with 0% saturation should result in 0 penalty rate per year'
        );
    }

    // edge cases - 95% borrow utilization with default saturation (95% saturation)
    function testCalcSaturationPenaltyRatePerSecondInWads_95PercentUtilization_95PercentSaturation() public pure {
        uint256 borrowUtilizationInWads = 0.95e18; // higher than the max possible utilization
        uint256 satUtilizationInWads = MAX_SATURATION_PERCENT_IN_WAD; // 95% saturation
        uint128 satInLAssetsInPenalty = 4; // only 5% is left
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );

        uint256 expectedPenaltyRatePerYearInWads = 175e16;

        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            2.1e9, // note that 1e16 is 1%
            '95% borrow utilization with 95% saturation should result in ~175% annual rate'
        );
    }

    // spot check 23% borrow utilization with default saturation (95% saturation) and 50% satInLAssetsInPenalty
    function testCalcSaturationPenaltyRatePerSecondInWads_23PercentUtilization_95PercentSaturation() public pure {
        uint256 borrowUtilizationInWads = 0.23e18;
        uint256 satUtilizationInWads = MAX_SATURATION_PERCENT_IN_WAD; // 95% saturation
        uint128 satInLAssetsInPenalty = 50; // 50%
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );
        uint256 expectedPenaltyRatePerYearInWads = 215.6e16; // calculated manually

        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            1e9, // note that 1e16 is 1%
            '95% borrow utilization with 95% saturation should result in ~215.6% penalty rate per year'
        );
    }

    // edge case - max borrow utilization, max saturation, max satInLAssetsInPenalty
    function testCalcSaturationPenaltyRatePerSecondInWads_90PercentUtilization_95PercentSaturation() public pure {
        uint256 borrowUtilizationInWads = 0.9e18;
        uint256 satUtilizationInWads = MAX_SATURATION_PERCENT_IN_WAD; // 95% saturation
        uint128 satInLAssetsInPenalty = 9; // 9% is the max
        uint256 allAssetsDepositL = 100;
        uint256 penaltyRatePerSecondInWads = Saturation.calcSaturationPenaltyRatePerSecondInWads(
            borrowUtilizationInWads, satUtilizationInWads, satInLAssetsInPenalty, allAssetsDepositL
        );
        uint256 expectedPenaltyRatePerYearInWads = 155.555555556e16; // calculated manually

        assertApproxEqAbs(
            penaltyRatePerSecondInWads * SECONDS_IN_YEAR,
            expectedPenaltyRatePerYearInWads,
            1e9, // 0.0001%
            '95% borrow utilization with 95% saturation should result in ~155% penalty rate per year'
        );
    }

    function getExpectedPenaltyRatePerSecondInWads(
        uint256 borrowUtilizationInWads,
        uint256 satUtilizationInWads,
        uint128 satInLAssetsInPenalty,
        uint256 allAssetsDepositL
    ) public pure returns (uint256 penaltyRatePerSecondInWads) {
        uint256 oneMinusCurrentUtilizationInWads = WAD - borrowUtilizationInWads;
        uint256 saturationBuffer = MAX_SATURATION_PERCENT_IN_WAD - satUtilizationInWads;
        uint256 targetUtilizationComponent =
            (oneMinusCurrentUtilizationInWads * saturationBuffer) / MAX_SATURATION_PERCENT_IN_WAD;

        uint256 targetUtilization;
        if (targetUtilizationComponent > MAX_UTILIZATION_PERCENT_IN_WAD) {
            targetUtilization = 0;
        } else {
            targetUtilization = MAX_UTILIZATION_PERCENT_IN_WAD - targetUtilizationComponent;
        }

        uint256 annualRate =
            Interest.getAnnualInterestRatePerSecondInWads(targetUtilization) * LIQUIDITY_INTEREST_RATE_MAGNIFICATION;
        uint256 liquidityProviderRateInWads = (oneMinusCurrentUtilizationInWads * annualRate) / WAD;
        penaltyRatePerSecondInWads = (liquidityProviderRateInWads * allAssetsDepositL) / satInLAssetsInPenalty;
    }
}
