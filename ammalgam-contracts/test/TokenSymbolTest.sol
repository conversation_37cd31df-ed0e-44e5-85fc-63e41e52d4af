// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {ReturnBombAttackStub} from 'test/stubs/ReturnBombAttackStub.sol';
import {TokenSymbol} from 'contracts/libraries/TokenSymbol.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {TokenWithBytes32SymbolStub} from 'test/stubs/TokenWithBytes32SymbolStub.sol';
import {TokenWithNoSymbolStub} from 'test/stubs/TokenWithNoSymbolStub.sol';
import {TokenWithStringSymbolStub} from 'test/stubs/TokenWithStringSymbolStub.sol';
import {TokenWithStringCAPSSymbolStub} from 'test/stubs/TokenWithStringCAPSSymbolStub.sol';
import {TestERC20} from 'test/shared/TestERC20.sol';

contract TokenSymbolTest is Test {
    ReturnBombAttackStub private returnBombTokenStub;
    TokenWithBytes32SymbolStub private token32BytesSymbolStub;
    TokenWithNoSymbolStub private tokenNoSymbolStub;
    TokenWithStringSymbolStub private tokenStringSymbolStub;
    TokenWithStringCAPSSymbolStub private tokenStringCAPSSymbolStub;

    address private wallet;
    address private other;
    IAmmalgamERC20 token;
    uint256 TOTAL_SUPPLY = 10_000e18;

    function testUniSymbolReturnBomb() public {
        returnBombTokenStub = new ReturnBombAttackStub();
        string memory symbolActual = TokenSymbol.uniSymbol(address(returnBombTokenStub));
        // should return all the characters that fit in 32 bytes
        string memory symbolExpected = substring(returnBombTokenStub.symbol(), 0, 32);
        assertEq(symbolActual, symbolExpected, 'Incorrect symbol returned');
    }

    function testUniSymbolReturns() public {
        token = new TestERC20(TOTAL_SUPPLY);
        string memory symbolActual = TokenSymbol.uniSymbol(address(token));
        assertEq(symbolActual, token.symbol(), 'Incorrect symbol returned');
    }

    function testUniSymbolCAPsReturns() public {
        string memory symbolExpected = 'CAPS';
        tokenStringCAPSSymbolStub = new TokenWithStringCAPSSymbolStub(symbolExpected);
        string memory symbolActual = TokenSymbol.uniSymbol(address(tokenStringCAPSSymbolStub));

        assertEq(symbolActual, symbolExpected, 'Incorrect symbol returned');
    }

    function testUniSymbol32Bytes() public {
        string memory symbolExpected = 'ABC';
        token32BytesSymbolStub = new TokenWithBytes32SymbolStub(stringToBytes32(symbolExpected));
        string memory symbolActual = TokenSymbol.uniSymbol(address(token32BytesSymbolStub));
        assertEq(symbolActual, symbolExpected, 'Incorrect symbol returned');
    }

    function testUniSymbolEmptyString() public {
        tokenStringSymbolStub = new TokenWithStringSymbolStub('');
        address tokenAddress = address(tokenStringSymbolStub);
        string memory symbolActual = TokenSymbol.uniSymbol(tokenAddress);
        // should return the address of mock contract
        assertEq(symbolActual, TokenSymbol.toHex(tokenAddress), 'Should return the address of mock contract');
    }

    function testUniSymbol32BytesEmpty() public {
        token32BytesSymbolStub = new TokenWithBytes32SymbolStub(stringToBytes32(''));
        address tokenAddress = address(token32BytesSymbolStub);
        string memory symbolActual = TokenSymbol.uniSymbol(tokenAddress);
        // should return the address of mock contract
        assertEq(symbolActual, TokenSymbol.toHex(tokenAddress), 'Should return the address of mock contract');
    }

    /**
     * Ref test case from 1inch: https://github.com/1inch/mooniswap/blob/02dccfab2ddbb8a409400288cb13441763370350/test/MooniFactory.js#L33,
     * 33 character test is relevant because we limit our max data copy from the uniSymbol static call to 96 bytes.
     * In this case, 33 chars symbol will take up 128 bytes. Calculated below:
     *  | Offset (32 bytes)                                                                 |
     *     | --------------------------------------------------------------------------------- |
     *     | 0x0000000000000000000000000000000000000000000000000000000000000020               |
     *
     *     | Length (32 bytes)                                                                 |
     *     | --------------------------------------------------------------------------------- |
     *     | 0x0000000000000000000000000000000000000000000000000000000000000021               |
     *
     *     | Actual Data (64 bytes, 33 bytes of data + 31 bytes of padding)                                                                                                   |
     *     | ------------------------------------------------------------------------------------------------------------------------- |
     *     | 3031323334353637383930313233343536373839303132333435363738393132000000000000000000000000000000000000000000000000000000000000000000  |
     *
     *  total data length = 32 + 32 + 64 = 128 bytes
     */
    function testUniSymbol33CharsSymbol() public {
        string memory thirtyThreeCharsSymbol = '012345678901234567890123456789012';
        tokenStringSymbolStub = new TokenWithStringSymbolStub(thirtyThreeCharsSymbol);
        string memory symbolActual = TokenSymbol.uniSymbol(address(tokenStringSymbolStub));
        // should return all the characters that fit in 32 bytes
        string memory symbolExpected = substring(thirtyThreeCharsSymbol, 0, 32);
        assertEq(symbolActual, symbolExpected, 'Incorrect symbol returned');
    }

    function testUniSymbolReturnsZeroAddress() public view {
        string memory symbol = TokenSymbol.uniSymbol(address(0));
        assertEq(symbol, TokenSymbol.toHex(address(0)), 'Should return the 0 address');
    }

    function testUniSymbolTokenWithoutSymbol() public {
        tokenNoSymbolStub = new TokenWithNoSymbolStub();
        address tokenAddress = address(tokenNoSymbolStub);
        string memory symbol = TokenSymbol.uniSymbol(tokenAddress);
        // should return the address of mock contract
        assertEq(symbol, TokenSymbol.toHex(tokenAddress), 'Should return the address of mock contract');
    }

    function testUniSymbolFuzz(
        string memory name
    ) public {
        uint256 nameLength = bytes(name).length;
        vm.assume(nameLength != 0);
        if (32 < nameLength) nameLength = 32;

        tokenStringSymbolStub = new TokenWithStringSymbolStub(name);
        string memory symbolActual = TokenSymbol.uniSymbol(address(tokenStringSymbolStub));
        // should return all the characters that fit in 32 bytes
        string memory symbolExpected = substring(name, 0, nameLength);
        assertEq(symbolActual, symbolExpected, 'Incorrect symbol returned');
    }

    function stringToBytes32(
        string memory source
    ) public pure returns (bytes32 result) {
        bytes memory tempEmptyStringTest = bytes(source);
        if (tempEmptyStringTest.length == 0) {
            return 0x0;
        }

        assembly {
            result := mload(add(source, 32))
        }
    }

    /**
     * @param str input
     * @param startIndex inclusive
     * @param endIndex exclusive
     */
    function substring(string memory str, uint256 startIndex, uint256 endIndex) private pure returns (string memory) {
        bytes memory strBytes = bytes(str);
        require(startIndex < endIndex, 'Start index must be less than end index');
        require(endIndex <= strBytes.length, 'End index out of bounds');

        bytes memory result = new bytes(endIndex - startIndex);
        for (uint256 i = startIndex; i < endIndex; i++) {
            result[i - startIndex] = strBytes[i];
        }
        return string(result);
    }
}
