[profile.default]
src = 'contracts'
libs = ['lib']
solc_version = "0.8.28"
no_match_test = "testFarmingLiquidity"
allow_internal_expect_revert = true  # solution for https://book.getfoundry.sh/cheatcodes/expect-revert#error

[profile.default.fuzz]
runs = 10000

[profile.default.fmt]
quote_style = 'single'
number_underscore = 'thousands'
multiline_func_header = 'params_first'

gas_reports = ["*"]
# See more config options https://github.com/foundry-rs/foundry/tree/master/config
