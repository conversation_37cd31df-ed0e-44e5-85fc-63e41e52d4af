#!/bin/bash

echo "formatting docs for docusaurus"

# Transform docs from mdbook format to docusaurus
find docs/src/contracts -type f -name "README.md" -exec bash -c '
    echo $1
    # Define the JSON content
    base=$(basename ${1%/*})
    capped_base="$(tr '[:lower:]' '[:upper:]' <<< ${base:0:1})${base:1}"
    echo $capped_base

    category_file="${1%/*}/_category_.json"
    echo "{ \"label\": \"${capped_base}\", \"link\": { \"type\": \"generated-index\" } }" > "$category_file"
    rm "$1"
' bash {} \;

# Find all markdown files in the docs directory and replace `/contracts` links
find docs/src/contracts -type f -name "*.md" -exec sed -i \
    's|](/contracts/|](/docs/developer-guide/contracts/|g' \
    {} \;
